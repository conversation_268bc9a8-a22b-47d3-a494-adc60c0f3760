
IF (CMAKE_BUILD_TYPE STREQUAL DEBUG)
	#交叉编译配置宏开关
	SET(CROSS_COMPILE 0)
	message("not cross_compile")
ELSE(CMAKE_BUILD_TYPE STREQUAL DEBUG)
	#交叉编译配置宏开关
	SET(CROSS_COMPILE 1)
	message("is cross_compile")
ENDIF()

#CMAKE最小版本
cmake_minimum_required(VERSION 3.14)

#设置目标平台系统
set(CMAKE_SYSTEM_NAME Linux)

#设置项目名称
project(gateway-iot)

#IF (CMAKE_BUILD_TYPE STREQUAL DEBUG)
#	ADD_DEFINITIONS(-DDEBUG)
#ENDIF()
ADD_DEFINITIONS(-DDEBUG)

#指定头文件目录
include_directories(./src/include)
include_directories(./src/var/ins)
include_directories(./src/net/ins)
include_directories(./src/main)
include_directories(./src/mq)
include_directories(./src/mqtt)
include_directories(src/util/ins)
include_directories(./src/json/include)
include_directories(./src/terminal/ins)
include_directories(./src/stm/ins)
include_directories(src/devices)
include_directories(src/util)
include_directories(./src/ins)
include_directories(./src/finger/ins)
include_directories(./src/http)
include_directories(./src/voice)
include_directories(./src/mongoose/ins)
include_directories(./src/log/ins)
include_directories(./src/log)
include_directories(./src/http/ins)
include_directories(./src/driver/ins)
include_directories(./src/agnss)
include_directories(./src/sqliteCpp)
include_directories(./src/iot)
include_directories(./src/func)
include_directories(./src/update)
include_directories(./src/config)
include_directories(./src/persistence)
include_directories(./src/manage)
if (CROSS_COMPILE)
	link_directories(./src/libs)
	set(TOOLCHAIN_DIR "/gcc-linaro")
	set(CMAKE_C_COMPILER   ${TOOLCHAIN_DIR}/bin/aarch64-linux-gnu-gcc)
	set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/bin/aarch64-linux-gnu-g++)

	# 设置交叉编译库路径
	set(CMAKE_FIND_ROOT_PATH ${TOOLCHAIN_DIR} ${TOOLCHAIN_DIR}/usr/include ${TOOLCHAIN_DIR}/usr/lib)
	#只在交叉编译库路径中寻找""
	set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
	#只在交叉编译库路径中寻找
	set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

	set(CMAKE_C_EXTENSIONS "-pipe -g -Wall -W -fPIE")
	set(CMAKE_CXX_EXTENSIONS "-pipe -g -Wall -W -fPIE")
	set(CMAKE_CXX_STANDARD 11)
	#add_compile_options(-DMEMWATCH -DMW_STDIO)
	set(CMAKE_CXX_STANDARD_REQUIRED ON)
	add_definitions(-D_GLIBCXX_USE_C99=1)
else(CROSS_COMPILE)
	#指定静态或动态链接库路径
	link_directories(./src/libs/pc)
	#set(TOOLCHAIN_DIR "/user")
	#set(CMAKE_C_COMPILER   ${TOOLCHAIN_DIR}/bin/cc)
	#set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/bin/c++)
endif(CROSS_COMPILE)


#RPATH ,安装后可在当前目录搜索动态库
set(CMAKE_SKIP_BUILD_RPATH FALSE)
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
set(CMAKE_INSTALL_RPATH $ORIGIN)

#add_executable(test src/main/test.cpp)

add_executable(gateway-iot
		#In main folder
		src/main/main.cpp

		#In util folder
		src/util/common_func.cpp

		src/update/mtd_device.cpp
		src/update/mtd_device.h

		#In mqttState folder
		src/mqtt/iot_mqtt.cpp
		src/iot/thing_tool.cpp

		#In net folder
		src/iot/thing_http.cpp
		#./src/net/http_protocol.cpp



		#In devices folder
		src/manage/device_manager.cpp
		src/devices/gateway_dev.cpp
		#src/devices/sensors.cpp


		#In logger folder
		src/log/glog/export.h
		src/log/glog/logging.h
		src/log/glog/raw_logging.h
		src/log/glog/stl_logging.h
		src/log/glog/vlog_is_on.h

		#Config
		src/config/gateway_config.cpp
		src/config/gateway_config.h
		src/update/upgrade_config.cpp


		#watchdog
		./src/watchdog/watchdog.cpp
		./src/watchdog/watchdog.h

		#In http folder
		src/http/http_method.cpp

		#In driver folder
		src/iot/thing_packet.cpp
		src/iot/thing_packet.h

		src/sqliteCpp/Backup.cpp
		src/sqliteCpp/Column.cpp
		src/sqliteCpp/Database.cpp
		src/sqliteCpp/Exception.cpp
		src/sqliteCpp/Savepoint.cpp
		src/sqliteCpp/Statement.cpp
		src/sqliteCpp/Transaction.cpp
		src/sqliteCpp/SQLiteCpp/Assertion.h
		src/sqliteCpp/SQLiteCpp/Backup.h
		src/sqliteCpp/SQLiteCpp/Column.h
		src/sqliteCpp/SQLiteCpp/Database.h
		src/sqliteCpp/SQLiteCpp/Exception.h
		src/sqliteCpp/SQLiteCpp/ExecuteMany.h
		src/sqliteCpp/SQLiteCpp/Savepoint.h
		src/sqliteCpp/SQLiteCpp/Statement.h
		src/sqliteCpp/SQLiteCpp/SQLiteCpp.h
		src/sqliteCpp/SQLiteCpp/Transaction.h
		src/sqliteCpp/SQLiteCpp/Utils.h
		src/sqliteCpp/SQLiteCpp/VariadicBind.h


		src/persistence/device_persistence.cpp
		src/persistence/device_persistence.h
		src/iot/thing_method.cpp
		src/iot/thing_method.h
		src/iot/thing_packet.cpp
		src/iot/thing_packet.h
		src/iot/thing_tool.cpp
		src/iot/thing_tool.h
		src/update/upgrade.cpp
		src/update/upgrade.h
		src/util/system_info.cpp
		src/util/system_info.h
		src/util/serialDevice.cpp
		src/util/serialDevice.h
		src/devices/manhole_cover_dev.cpp
		src/devices/manhole_cover_dev.h
		src/devices/weather_dev.cpp
		src/devices/weather_dev.h
		src/devices/soil_temperature_dev.cpp
		src/devices/soil_temperature_dev.h
		src/devices/soil_ph_dev.cpp
		src/devices/soil_ph_dev.h
		src/devices/hydrant_dev.cpp
		src/devices/hydrant_dev.h
		src/devices/radar_level_dev.cpp
		src/devices/radar_level_dev.h
		src/manage/device_factory.cpp
		src/manage/device_factory.h
		src/devices/rainfall_dev.cpp
		src/devices/rainfall_dev.h
		src/devices/wind_speed_dev.cpp
		src/devices/wind_speed_dev.h
		src/devices/wind_direction_dev.cpp
		src/devices/wind_direction_dev.h
		src/devices/lora_node.cpp
		src/devices/lora_node.h
		src/devices/radar_light_dev.cpp
		src/devices/radar_light_dev.h
		src/util/adc.cpp
		src/util/adc.h
		src/util/temperature_humidity.cpp
		src/util/temperature_humidity.h
		src/iot/thing_action.cpp
		src/iot/thing_action.h
		src/iot/thing_device.h
		src/iot/thing_interface.h
		src/iot/thing_queue.cpp
		src/iot/thing_queue.h
		src/manage/thing_impl.cpp
		src/manage/thing_impl.h
		src/devices/device.h
		src/iot/thing_message.h
		src/manage/message_dispatch.cpp
		src/manage/message_dispatch.h
		src/mqtt/ns_mqtt.cpp
		src/mqtt/ns_mqtt.h
		src/iot/thing_server.cpp
		src/iot/thing_server.h
		src/manage/device_interface.cpp
		src/manage/device_interface.h
		src/manage/device_manager_interface.h
		src/manage/message_dispatch_interface.h
		src/mqtt/ns_mqtt_interface.h
		src/mqtt/iot_mqtt_interface.h
		src/manage/iot_core.cpp
		src/manage/iot_core.h
		src/devices/device_helper.cpp
		src/devices/device_helper.h
		src/manage/schedule.cpp
		src/manage/schedule.h
		src/devices/transparent_dev.cpp
		src/devices/transparent_dev.h
		src/devices/displacement_dev.cpp
		src/devices/displacement_dev.h
		src/devices/sos_dev.cpp
		src/devices/sos_dev.h
		src/devices/lamp_switch_dev.cpp
		src/devices/lamp_switch_dev.h
		src/devices/sl_alarm_dev.cpp
		src/devices/sl_alarm_dev.h
		src/devices/th_dev.cpp
		src/devices/th_dev.h
		src/devices/smoke_dev.cpp
		src/devices/smoke_dev.h
		src/devices/infrared_dev.cpp
		src/devices/infrared_dev.h
		src/devices/magnetism_dev.cpp
		src/devices/magnetism_dev.h
		src/devices/water_pressure_dev.cpp
		src/devices/water_pressure_dev.h
		src/devices/liquid_level_dev.cpp
		src/devices/liquid_level_dev.h
		src/devices/water_immersion_dev.cpp
		src/devices/water_immersion_dev.h
		src/devices/water_ph_dev.cpp
		src/devices/water_ph_dev.h
		src/devices/liquid_level_underground_dev.cpp
		src/devices/liquid_level_underground_dev.h
		src/main/test.cpp
		src/devices/electricity_meter_dev.cpp
		src/devices/electricity_meter_dev.h
		src/devices/three_phase_electricity_meter_dev.cpp
		src/devices/three_phase_electricity_meter_dev.h
		src/devices/water_meter_dev.cpp
		src/devices/water_meter_dev.h
		src/devices/solenoidvalve_dev.cpp
		src/devices/solenoidvalve_dev.h
		src/devices/fx_theftproof_dev.cpp
		src/devices/fx_theftproof_dev.h
		src/util/HexTool.cpp
		src/util/HexTool.h)

#-fPIC在给定的作用域内设置一个命名的属性
set_property(TARGET gateway-iot PROPERTY POSITION_INDEPENDENT_CODE ON)

#链接库文件 libpthread.so
target_link_libraries(gateway-iot
		ssl
		pthread
		curl
		jsoncpp
		#ssh2

		mosquitto

		crypto

		sqlite3
		glogd.so
		-ldl
		)
