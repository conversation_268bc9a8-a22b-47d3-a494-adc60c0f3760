/**
 *  MQTT接口规范实现
 *  包含MQTT ClientID生成
 *  包含MQTT 用户名和密码生成
 *  PackMqttMessage 数据发送封装
 *  UnpackMqttMessage 数据接收解包
 */
#ifndef THING_TOOL_H
#define THING_TOOL_H

#include <string>
#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <cassert>
#include <string>
#include <iostream>
#include <sstream>
#include <sys/time.h>
#include "openssl/aes.h"
#include "openssl/pem.h"
#include "openssl/bio.h"
#include "openssl/evp.h"
#include <openssl/err.h>
#include <openssl/buffer.h>
#include <openssl/hmac.h>
#include <openssl/rand.h>
#include "json/json.h"




/**
 * MQTT连接使用的密码
 * ⽤ hmacmd5 算法对 secret_key 和 username ⽣成摘要，得到 sign 即密码
 * @param deviceSecret 设备秘钥
 * @param userName mqtt用户名
 * @return
 */
std::string GetMqttPassword(std::string &deviceSecret, std::string &userName);


/**
 * MQTT连接使用的username
 * @param productKey
 * @param deviceName
 * @return
 */
std::string GetMqttUsername(std::string &productKey, std::string &deviceName);



/**
 * MQTT连接clientId
 * @param productKey
 * @param deviceName
 * @return
 */
std::string GetMqttClientId(std::string &productKey, std::string &deviceName);



/**
 * 打包发送的数据，输出数据直接供MQTT发送
 * @param payload
 * @param deviceSecret
 * @param outResult 输出结果
 * @return  输出按协议封装后的数据，直接用于发送
 */
void PackMqttMessage(std::string &payload, std::string &deviceSecret, std::string &outResult);


/**
 * 打包发送的数据
 * @param payload
 * @param deviceSecret
 * @param outResult 输出结果
 * @return
 */
void PackMqttMessage(Json::Value &payload, std::string &deviceSecret, std::string &outResult);

/**
 * 解密MQTT接收到的数据
 * @param payload
 * @param payloadLen
 * @param deviceSecret  设备秘钥
 * @param outResult     输出结果
 * @return 输出平台下发的原数据
 */
int UnpackMqttMessage(const char *payload, int payloadLen, std::string &deviceSecret, std::string &outResult);


/**
 * base64解密
 * @param sIn 需要解密的字符串
 * @return 解密后的字符串
 */
std::string Base64Decode(std::string &sIn);

/**
 * base64加密
 * @param sIn 需要加密的字符串
 * @return 加密后的字符串
 */
std::string Base64Encode(char *sIn, int len);
/**
 * base64加密
 * @param sIn 需要加密的字符串
 * @return 加密后的字符串
 */
std::string Base64Encode(std::string &sIn);



/**
 * AES 128 ECB 加密
 * @param source  需加密数据
 * @param key     秘钥
 * @return
 */
std::string Aes128EcbEncrypt(const std::string& source, const std::string& key);

/**
 * AES 128 ECB 解密
 * @param cipherText
 * @param key
 * @return
 */
std::string Aes128EcbDecrypt(const std::string& cipherText, const std::string& key);


/**
 * 16进制数组转string
 * @param s
 * @param pHex
 * @param length
 */
void HexArrayToString(std::string &s, unsigned char *pHex, int length);


/**
 * 获取随机数接口
 * @param p 存放随机数缓冲
 * @param len 缓冲长度
 * @return 1 ，成功， 0 失败；
 */
int GetRandom(unsigned char *p, int len);


/**
 * 获取时间戳
 * @return
 */
std::string GetTimeStamp(void);
#endif //THING_TOOL_H
