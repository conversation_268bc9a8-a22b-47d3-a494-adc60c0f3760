/**
 * 能力集接口，外部需实现该接口
 */

#ifndef GATEWAY_IOT_THING_INTERFACE_H
#define GATEWAY_IOT_THING_INTERFACE_H

#include "json/json.h"
#include <string>
using namespace std;

/**
 * 能力集接口
 */
class ThingInterface {
public:


    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    virtual int Send(string &topic, string &payload) = 0;



    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    virtual int SubscribeTopic(int &mid, string &topic) = 0;



    /**
     * 查询设备秘钥接口，用户需保存设备的秘钥，物模型查询秘钥用于解密IOT数据，需外部实现该接口
     * @param productKey    产品的key
     * @param deviceName    设备的id
     * @return
     */
    virtual string GetDevSecret(string &productKey, string deviceName) = 0;



    /**
     * 收到IOT平台的消息接口，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    virtual int ReceiveIoTMessage(string &topic, Json::Value &payload) = 0;

    /**
     * 析构
     */
    virtual ~ThingInterface() = default;
};


#endif //GATEWAY_IOT_THING_INTERFACE_H
