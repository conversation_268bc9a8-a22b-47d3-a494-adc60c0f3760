//
// Created by seashell on 11/9/21.
//

#include "thing_server.h"
#include "thing_method.h"
#include <utility>




/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int ThingServer::Send(string &topic, string &payload)
{
    return mInterface->Send(topic, payload);
}

/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int ThingServer::Send(string &topic, string &payload, long messageId, Json::Value &response) {

    ThingMessage message;
    messageQueue.Clear();
    mInterface->Send(topic, payload);
    if (messageQueue.WaitTimeAndPop(5, message))
    {
        if (message.messageId == messageId)
        {
            response = message.payload;
            return 0;
        }
    }
    return -1;
}


/**
 * 收到IoT平台消息，进行解析并输出解析后的消息
 * @param topic
 * @param payload
 * @return
 */
int ThingServer::receiveMessageFromIoTServer(string &topic, string &payload) {

    string realData;
    string deviceId = GetDeviceIdFromTopic(topic);
    string productKey = GetProductKeyByTopic(topic);
    string deviceSecret = mInterface->GetDevSecret(productKey, deviceId);
    Json::Value resultValue = ReceiveMessage(deviceSecret, topic, payload);
    //unpackMqttMessage((const char *) payload.c_str(), payload.length(), deviceSecret, realData);
    //Json::Value resultValue = stringToJson(realData);
    //拦截能力集相关的消息
    if (IS_TOPIC_MESSAGE(topic, TOPIC_RES_REG_AUTO)
        || IS_TOPIC_MESSAGE(topic, TOPIC_RES_BIND)
        || IS_TOPIC_MESSAGE(topic, TOPIC_RES_CONNECT)
        || IS_TOPIC_MESSAGE(topic, TOPIC_RES_DELETE)
        || IS_TOPIC_MESSAGE(topic, TOPIC_RES_REG_DYNAMIC))
    {
        long messageId = stoll(resultValue["id"].asString());
        ThingMessage message;
        message.topic = topic;
        message.payload = resultValue;
        message.messageId = messageId;
        messageQueue.Push(message);
    }

    mInterface->ReceiveIoTMessage(topic, resultValue);
    return 0;

}



/**
 * 订阅设备主题接口，需外部实现该接口
 * @param mid       MQTT消息发送id，MQTT返回
 * @param topic     MQTT主题
 * @return
 */
int ThingServer::SubscribeTopic(int &mid, string &topic)
{
    return mInterface->SubscribeTopic(mid, topic);
}





/**
 *  设置服务器信息
 * @param httpApiBaseAddress        //HTTP 服务器基地址
 * @param httpApiPort               //HTTP端口
 * @param httpGatewayRegUrl         //api地址
 * @param gatewayProductSecret      //产品秘钥
 * @param gatewayVersion            //网关版本
 */
void ThingServer::setServerInfo(string _httpApiBaseAddress,
                                string _httpApiPort,
                                string _httpGatewayRegUrl,
                                string _gatewayVersion)
{
    httpApiBaseAddress = std::move(_httpApiBaseAddress);
    httpApiPort = std::move(_httpApiPort);
    httpGatewayRegUrl = std::move(_httpGatewayRegUrl);
    gatewayVersion = std::move(_gatewayVersion);
}



/**
 * 获取MQTT的client id
 * @param mqttUsername
 * @param deviceSecret
 * @return
 */
string ThingServer::getIoTMqttClientId(string productKey, string deviceName)
{
    return GetMqttClientId(productKey, deviceName);
}

/**
 * 获取MQTT的用户名
 * @param mqttUsername
 * @param deviceSecret
 * @return
 */
string ThingServer::getIoTMqttUserName(string productKey, string deviceName)
{
    return GetMqttUsername(productKey, deviceName);
}



/**
 * 获取MQTT的密码
 * @param mqttUsername
 * @param deviceSecret
 * @return
 */
string ThingServer::getIoTMqttPassword(string mqttUsername, string deviceSecret)
{
    return GetMqttPassword(deviceSecret, mqttUsername);
}


/**
 * gouzhao
 * @param anImpl
 * @return
 */
ThingServer::ThingServer(ThingInterface *impl) {
    mInterface = impl;
}


