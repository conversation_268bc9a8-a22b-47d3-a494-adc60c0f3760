/**
 *
 * 能力集方法，包含设备注册，子设备绑定，设备上线、订阅主题、设备解绑、设备下线等。
 *
 */
#ifndef PACKETFORWARDER_MESSAGE_ACTION_H
#define PACKETFORWARDER_MESSAGE_ACTION_H
#include "string"
#include "thing_packet.h"
#include "thing_device.h"
#include "thing_tool.h"
#include <json/value.h>

/**
 * 设备注册到平台，自动注册
 * @param isWaitForResponse 是否阻塞等待注册响应
 * @return
 */
int DeviceRegister(string &device_name, string &product_key, Json::Value &data);
/**
 * 设备注册主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceRegisterTopic(string &gw_device_name, string &gw_product_key);


/**
 * 设备绑定到网关
 * @param isWaitForResponse
 * @return
 */
int DeviceBind(string &device_name, string &product_key, Json::Value &data);
/**
 * 设备绑定主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceBindTopic(string &gw_device_name, string &gw_product_key);

/**
 * 取消设备绑定
 * @return
 */
int DeviceCancelBind(string &device_name, string &product_key, Json::Value &data);
/**
 * 组装设备取消绑定主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceCancelBindTopic(string &gw_device_name, string &gw_product_key);


/**
 * 设备上线
 * @param isWaitForResponse 是否阻塞等待响应
 * @return
 */
int DeviceOnline(string &device_name, string &product_key, Json::Value &data);
/**
 * 组装设备上线主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceOnlineTopic(string &gw_device_name, string &gw_product_key);


/**
 * 设备下线
 * @return
 */
int DeviceOffline(string &device_name, string &product_key, string &topic, Json::Value &data);



/**
 * 设备同步
 * @return
 */
int DeviceSync(string &device_name, string &product_key, string &topic, Json::Value &data);


/**
 * 设备上报版本号,OTA
 * @param device_name
 * @param product_key
 * @param topic
 * @param data
 * @return
 */
int DeviceReportVersion(string &device_name, string &product_key, string &device_version, string &topic, Json::Value &data);


/**
 * 设备上报ota progress
 * @param device_name
 * @param product_key
 * @param topic
 * @param data
 * @return
 */
int DeviceReportOTAProgress(string &device_name, string &product_key, string &step, string &desc, string &topic, Json::Value &data);


/**
 * 向平台上报设备事件
 * @param device_name
 * @param product_key
 * @param eventName
 * @param topic
 * @param data
 * @return
 */
int DeviceReportDeviceEvent(string &device_name, string &product_key, string eventName, string &topic, Json::Value &data);

/**
 * @description: 上报属性
 * @param {type}
 * @return:
 */
int DeviceReportPropEvent(string &device_name, string &product_key, string &topic, Json::Value &data);



/**
 * 设备应答平台
 * @param m_id
 * @param code
 * @param topic
 * @param data
 * @return
 */
int DeviceResponseToPlatform(string m_id, int code, Json::Value &data);
#endif //PACKETFORWARDER_MESSAGE_ACTION_H
