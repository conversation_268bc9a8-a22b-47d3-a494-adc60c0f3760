
/*
 * @Description:
 * @Version: 1.0
 * @Autor:
 * @LastEditTime : 2019-12-24 16:54:05
 */

#include <common_func.h>
#include "thing_http.h"
#include "http_method.h"


/**
 * @description: 把编码后的字符串中加号（+）替换成 %2B、星号（*）替换成 %2A、%7E 替换回波浪号（~）
 * @param sIn
 */
static void urlCodeReplace(string &sIn)
{
    stringReplace(sIn, "+", "%20");
    stringReplace(sIn, "*", "%2A");
    stringReplace(sIn, "~", "%7E");
}


/**
 * 协议制定的URL编码
 * @param name
 * @param value
 * @return
 */
static string urlCodeEncode(string name, string value)
{
    string nameBase64 = curl_easy_escape(NULL, name.c_str(), name.length());
    string valueBase64 = curl_easy_escape(NULL, value.c_str(), value.length());
    string tmp = nameBase64 + "=" + valueBase64;
    urlCodeReplace(tmp);
    return tmp;
}

/**
 * @description: 构造除sign参数外的公共参数
 * 1 按参数名升续排序
 * 2 对参数名称和值都使用URL编码
 * 3 编码后名称和值用=连接
 * 4 =之间依次使用&符号连接
 * @param pHttpParams:http参数
 * @return: 编码后的参数
 */
static string getRequestUrlCode(string &action, string &deviceName,string &productKey ,string &productSecret,string &version)
{
    string tmp;
    // action编码
    string sSerialize = urlCodeEncode(_PARAM_ACTION, action);

    Json::Value dataJson;
    dataJson[_PARAM_DEVICE_NAME] = deviceName;
    dataJson[_PARAM_PRODUCT_KEY] = productKey;
    string data = jsonTostring(dataJson, false);

    // 参数data, 非空,需要先经过AES加密后参与签名
    string sEnBody = Aes128EcbEncrypt(data, productSecret);
    // data编码
    sSerialize.append("&" + urlCodeEncode(_PARAM_DATA, Base64Encode(sEnBody)));

    // key编码
    sSerialize.append("&" + urlCodeEncode(_PARAM_KEY, productKey));

    // nonce编码(唯一随机数)
    u8 pRandom[6];
    GetRandom(pRandom, 6);
    HexArrayToString(tmp, pRandom, 6);
    tmp = urlCodeEncode(_PARAM_NONCE, tmp);
    sSerialize.append("&" + tmp);

    // timestamp编码
    tmp = GetTimeStamp();
    tmp = urlCodeEncode(_PARAM_TIMESTAMP, tmp);
    sSerialize.append("&" + tmp);

    // version编码
    sSerialize.append("&" + urlCodeEncode(_PARAM_VERSION, version));

    // 特殊字符编码替换
    //urlCodeReplace(sSerialize);
    return sSerialize;
}


/**
 * 获取http返回数据编码字符串
 * @param jReply
 * @return
 */
static string getReplyUrlCode(Json::Value jReply)
{
    string s, sSerialize;

    //code -> data -> id -> message(if has)
    //code编码
    if (jReply.isMember(_PARAM_CODE))
    {
        sSerialize = urlCodeEncode(_PARAM_CODE, jReply[_PARAM_CODE].asString());
    }

    //data编码(已经加密)
    if (jReply.isMember(_PARAM_DATA))
    {
        s = urlCodeEncode(_PARAM_DATA, jReply[_PARAM_DATA].asString());
        sSerialize.append("&" + s);
    }

    //id编码
    if (jReply.isMember(_PARAM_ID))
    {
        s = urlCodeEncode(_PARAM_ID, jReply[_PARAM_ID].asString());
        sSerialize.append("&" + s);
    }

    return sSerialize;
}

/**
 * @description: 获取签名数据
 * @param data->需要签名的数据 key->密钥
 * @return: 签名字符串
 */
static string getSignData(string data, string key)
{
    string sKey;
    string sSign, sBase64;
    u8 pMD5[16];
    u32 nLen = 0;

    //使用DeviceSecret+"&"作为密钥通过MD5计算HMAC值
    sKey = key;
    sKey.append("&");

    HMAC(EVP_md5(),
         sKey.c_str(),
         sKey.length(),
         (u8 *)data.c_str(),
         data.length(),
         pMD5,
         &nLen);

    //通过Base64将HAMC进行编码得到sign签名
    string tmp((char *)pMD5,nLen);
    sBase64 = Base64Encode(tmp);

    return sBase64;
}


/**
 * 获取网关设备注册的完整请求数据
 * @param action        自动注册“device_auto_register”
 * @param deviceName
 * @param productKey
 * @param productSecret
 * @param version       版本
 * @return
 */
string getGateWayDeviceRegisterBody(
        string &action,
        string &deviceName,
        string &productKey ,
        string &productSecret,
        string &version)
{
    //编码，构造规范化请求字符串,包括 “公共请求参数”（不包括 sign参数）、加密后的data业务参数和other参数
    string sBody = getRequestUrlCode(action,deviceName,productKey,productSecret,version);

    //根据http规范获取签名数据
    string sSign = getSignData(sBody, productSecret);

    //对得到的sign签名值再通过RFC3986规则进行编码
    sSign = urlCodeEncode(_PARAM_SIGN, sSign);

    //加入签名串
    sBody.append("&" + sSign);

    LOG(INFO) << "final body data->" << sBody << endl;
    return sBody;
}


/**
 * @description: http返回数据签名验证接口
 * @param jResult http返回json数据
 * @param key key->设备密钥
 * @return
 */
static int verifyReplySign(Json::Value jResult, string key)
{
    string sCode = getReplyUrlCode(jResult);
    string sSign = getSignData(sCode, key);
    return sSign.compare(jResult[_PARAM_SIGN].asString());
}


/**
 * @description: http返回消息解密
 * @param data
 * @param key
 * @return 解密后的json字符串
 */
static Json::Value decodeData(string data, string key)
{
    string sBase64, sDecrypt;
    Json::Value jData;

    //BASE64解码
    sBase64 = Base64Decode(data);

    //AES 解密Reply
    sDecrypt = Aes128EcbDecrypt(sBase64, key);
    jData = stringToJson(sDecrypt);

    return jData;
}



/**
 * 发送网关设备注册请求
 * @param url
 * @param data      按协议封装的数据
 * @param response  请求的应答数据
 * @param timeout   请求超时时间
 * @return
 */
int registerGatewayDevice(string &url, string &data, string &productSecret,string &response,Json::Value &jResponse, int timeout)
{
    //Json::Value jResponse;
    string header = HEADER_FORM_URLENCODED;
    int ret = HttpPost(url, data.c_str(), response, header, timeout);
    if (ret == RET_OK){
        /*网络请求成功*/
        return unPackRegisterMessage(productSecret, response, jResponse);
    }
    else{
        /*网络请求失败*/
        sleep(2);
        return RET_FAIL;
    }
}



/**
 * 解包注册返回的消息
 * @param productSecret
 * @param responseStr   http返回的原始数据
 * @param responseJson  解析后的JSON数据
 * @return 0注册成功 否则失败
 */
int  unPackRegisterMessage(string &productSecret, string &responseStr, Json::Value &responseJson)
{
    Json::Value jResult = stringToJson(responseStr);

    //返回码，当值为 "0" 标识接口返回成功
    if (jResult[_PARAM_CODE].asString() != "0"){
        LOG(ERROR) << "post error CODE=" << jResult[_PARAM_CODE].asString();
        responseJson = jResult;
        return RET_FAIL;
    }

    //校验签名
    if (RET_OK != verifyReplySign(jResult, productSecret)){
        LOG(ERROR) << "verify reply sign error";
        responseJson = jResult;
        return RET_FAIL;
    }

    //消息体解密
    responseJson = decodeData(jResult[_PARAM_DATA].asString(), productSecret);
    LOG(INFO) << "decode resp data->" << responseJson << endl;
    if (responseJson.isMember("deviceSecret")){
        return RET_OK;
    } else{
        return RET_FAIL;
    }

}