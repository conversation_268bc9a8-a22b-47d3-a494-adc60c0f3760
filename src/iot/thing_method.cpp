//
// Created by seashell on 5/31/21.
//

#include "thing_method.h"

/**
 * 设备注册到平台，自动注册
 * @param isWaitForResponse 是否阻塞等待注册响应
 * @return
 */
int DeviceRegister(string &device_name, string &product_key, Json::Value &data)
{
    Json::Value request;
    request["deviceName"] = device_name;
    request["productKey"] = product_key;
    data = PackCommonMsgBody(request, "thing.bind.register.auto");
    return RET_OK;
}

/**
 * 设备注册主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceRegisterTopic(string &gw_device_name, string &gw_product_key)
{
    string topic = "$gateway/" + gw_product_key + "/" + gw_device_name + "/bind/register/auto";
    return topic;
}



/**
 * 设备绑定到网关
 * @param isWaitForResponse
 * @return
 */
int DeviceBind(string &device_name, string &product_key, Json::Value &data)
{

    Json::Value txJs;
    txJs["deviceName"] = device_name;
    txJs["productKey"] = product_key;
    txJs["timestamp"] = GetTimeStamp();
    data = PackCommonMsgBody(txJs, "thing.bind.mapping.add");
    return RET_OK;

}


/**
 * 设备绑定主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceBindTopic(string &gw_device_name, string &gw_product_key)
{
    string topic = "$gateway/" + gw_product_key + "/" + gw_device_name + "/bind/mapping/add";
    return topic;
}




/**
 * 取消设备绑定
 * @param device_name
 * @param product_key
 * @param data
 * @return
 */
int DeviceCancelBind(string &device_name, string &product_key, Json::Value &data)
{
    Json::Value txJs;
    txJs["deviceName"] = device_name;
    txJs["productKey"] = product_key;
    txJs["timestamp"] = GetTimeStamp();
    data = PackCommonMsgBody(txJs, "thing.bind.mapping.delete");
    return RET_OK;
}

/**
 * 组装设备取消绑定主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceCancelBindTopic(string &gw_device_name, string &gw_product_key)
{
    string topic = "$gateway/" + gw_product_key + "/" + gw_device_name + "/bind/mapping/delete";
    return topic;
}


/**
 * 设备上线
 * @param isWaitForResponse 是否阻塞等待响应
 * @return
 */
int DeviceOnline(string &device_name, string &product_key, Json::Value &data)
{
    Json::Value request;
    request["deviceName"] = device_name;
    request["productKey"] = product_key;
    request["timestamp"] = GetTimeStamp();
    data = PackCommonMsgBody(request, "thing.bind.session.connect");
    return RET_OK;
}


/**
 * 组装设备上线主题
 * @param gw_device_name
 * @param gw_product_key
 * @return
 */
string DeviceOnlineTopic(string &gw_device_name, string &gw_product_key)
{
    string topic = "$gateway/" + gw_product_key + "/" + gw_device_name + "/bind/session/connect";
    return topic;
}

/**
 * 设备下线
 * @param device_name
 * @param product_key
 * @param topic
 * @param data
 * @return
 */
int DeviceOffline(string &device_name, string &product_key, string &topic, Json::Value &data)
{
    Json::Value request;
    request["deviceName"] = device_name;
    request["productKey"] = product_key;
    data = PackCommonMsgBody(request, "thing.bind.session.disconnect");
    topic = "$gateway/" + product_key + "/" + device_name + "/bind/session/disconnect";
    return RET_OK;
}



 /**
  * 设备同步
  * @param device_name
  * @param product_key
  * @param topic
  * @param data
  * @return
  */
int DeviceSync(string &device_name, string &product_key, string &topic, Json::Value &data)
{
    Json::Value request;
    request["deviceName"] = device_name;
    request["productKey"] = product_key;
    data = PackCommonMsgBody(request, "thing.property.desired.get");
    topic = "$gateway/" + product_key + "/" + device_name + "/property/desired/get";
    return RET_OK;
}


/**
 * 设备上报版本号,OTA
 * @param device_name
 * @param product_key
 * @param device_version
 * @param topic
 * @param data
 * @return
 */
int DeviceReportVersion(string &device_name, string &product_key, string &device_version, string &topic, Json::Value &data)
{
    Json::Value request;
    request["version"] = device_version;
    data = PackOtaMsgBody(request);
    topic = "$ota/report/version/" + product_key + "/" + device_name;
    return RET_OK;
}

/**
 * 设备上报ota progress
 * @param device_name
 * @param product_key
 * @param topic
 * @param data
 * @return
 */
int DeviceReportOTAProgress(string &device_name, string &product_key, string &step, string &desc, string &topic, Json::Value &data)
{
    Json::Value request;
    request["step"] = step;
    request["desc"] = desc;
    data = PackOtaMsgBody(request);
    topic = "$ota/report/progress/" + product_key + "/" + device_name;
    return RET_OK;
}
/**
 * 向平台上报设备事件
 * @param device_name
 * @param product_key
 * @param eventName
 * @param topic
 * @param data
 * @return
 */
int DeviceReportDeviceEvent(string &device_name, string &product_key, string eventName, string &topic, Json::Value &data)
{
    topic = GetEventReportTopic(product_key, device_name, eventName);
    data = PackEventMsgBody(data, GetEventReportMethod(eventName).c_str());
    return RET_OK;
}


 /**
  * 设备上报属性
  * @param device_name
  * @param product_key
  * @param topic
  * @param data
  * @return
  */
int DeviceReportPropEvent(string &device_name, string &product_key, string &topic, Json::Value &data)
{
    data = PackPropertyReportMsgBody(data, GetEventReportMethod("property").c_str());
    topic = GetEventReportTopic(product_key, device_name, "property");
    return RET_OK;
}


/**
 * 设备应答平台
 * @param m_id
 * @param code
 * @param topic
 * @param data
 * @return
 */
int DeviceResponseToPlatform(string m_id, int code, Json::Value &data)
{
    data = PackReplyMsgBody(m_id, code, data);
    return RET_OK;
}