//
// Created by seashell on 11/9/21.
//
#ifndef THING_SERVER_H
#define THING_SERVER_H

#include "thing_interface.h"
#include "thing_action.h"
#include "thing_interface.h"
#include "thing_tool.h"
#include "thing_device.h"
#include "thing_message.h"
#include "thing_queue.h"




/**
 * 能力集服务，对外总接口
 */
class ThingServer : public ThingAction{

#if 0
public:
    static ThingServer& getInstance(){
        static ThingServer instance;
        return instance;
    }

    ThingServer(const ThingServer& other) = delete;
    ThingServer& operator=(const ThingServer& other) = delete;

protected:
    ThingServer() = default;
    virtual ~ThingServer() = default;

#else

public:
    explicit ThingServer(ThingInterface *impl);
    ~ThingServer() = default;
#endif


public:

    /**
     * 解析IoT消息
     * @param topic
     * @param payload
     * @return
     */
    int  receiveMessageFromIoTServer(string &topic, string &payload);

    /**
     *
     * @param httpApiBaseAddress        //HTTP 服务器基地址
     * @param httpApiPort               //HTTP端口
     * @param httpGatewayRegUrl         //api地址
     * @param gatewayProductSecret      //产品秘钥
     * @param gatewayVersion            //网关版本
     */
    void setServerInfo(string _httpApiBaseAddress,
                       string _httpApiPort,
                       string _httpGatewayRegUrl,
                       string _gatewayVersion);



    /**
     * 获取MQTT的client id
     * @param mqttUsername
     * @param deviceSecret
     * @return
     */
    string getIoTMqttClientId(string productKey, string deviceName);


    /**
     * 获取MQTT的用户名
     * @param mqttUsername
     * @param deviceSecret
     * @return
     */
    string getIoTMqttUserName(string productKey, string deviceName);


    /**
     * 获取MQTT的密码
     * @param mqttUsername
     * @param deviceSecret
     * @return
     */
    string getIoTMqttPassword(string mqttUsername, string deviceSecret);


    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    int Send(string &topic, string &payload)override;



    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    int Send(string &topic, string &payload, long messageId, Json::Value &response) override;


    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    int SubscribeTopic(int &mid, string &topic) override;


private:
    ThingInterface *mInterface = nullptr;         //适配器
    ThingQueue<ThingMessage> messageQueue;      //消息队列,用于线程间通信，接收平台响应数据
};


#endif //THING_SERVER_H
