//
// Created by seashell on 11/3/21.
//

#ifndef GATEWAY_IOT_THING_QUEUE_H
#define GATEWAY_IOT_THING_QUEUE_H
#include <cstring>
#include <pthread.h>
#include <sys/time.h>
#include <string>
#include <queue>
#include <mutex>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <initializer_list>
#include <chrono>


using namespace std;

/**
 * 线程安全队列
 * T为队列元素类型
 * 因为有std::mutex和std::condition_variable类成员,所以此类不支持复制构造函数也不支持赋值操作符(=)
 * */
template<typename T>
class ThingQueue {
private:
    mutable std::mutex mut;                     //data_queue访问信号量
    mutable std::condition_variable data_cond;  //条件变量
    using queue_type = std::queue<T>;
    queue_type data_queue;
public:
    using value_type = typename queue_type::value_type;
    using container_type = typename queue_type::container_type;

    ThingQueue() = default;
    ThingQueue(const ThingQueue &) = delete;
    ThingQueue &operator=(const ThingQueue &) = delete;

    /**
     * 使用迭代器为参数的构造函数,适用所有容器对象
     * */
    template<typename _InputIterator>
    ThingQueue(_InputIterator first, _InputIterator last) {
        for (auto itor = first; itor != last; ++itor) {
            data_queue.push(*itor);
        }
    }

    explicit ThingQueue(const container_type &c) : data_queue(c) {}

    /**
     * 使用初始化列表为参数的构造函数
     * */
    ThingQueue(std::initializer_list<value_type> list) : ThingQueue(list.begin(), list.end()) {
    }

    /**
     * 将元素加入队列
     * */
    void Push(const value_type &new_value) {
        std::lock_guard<std::mutex> lk(mut);
        data_queue.push(std::move(new_value));
        data_cond.notify_all();
    }

    /**
     * 从队列中弹出一个元素,如果队列为空就阻塞
     * */
    value_type WaitAndPop() {
        std::unique_lock<std::mutex> lk(mut);
        data_cond.wait(lk, [this] { return !this->data_queue.empty(); });
        auto value = std::move(data_queue.front());
        data_queue.pop();
        return value;
    }


    /**
     * 从队列中弹出一个元素,如果队列为空就阻塞
     * */
    bool WaitTimeAndPop(int second, value_type &value) {
        std::unique_lock<std::mutex> lk(mut);
        auto now = std::chrono::system_clock::now();
        if (data_cond.wait_until(lk,now + std::chrono::seconds (second),[this] { return !this->data_queue.empty(); })){
            value = std::move(data_queue.front());
            data_queue.pop();
            return true;
        } else{
            return false;
        }
    }

    /**
     * 从队列中弹出一个元素,如果队列为空返回false
     * */
    bool TryPop(value_type &value) {
        std::lock_guard<std::mutex> lk(mut);
        if (data_queue.empty())
            return false;
        value = std::move(data_queue. front());
        data_queue.pop();
        return true;
    }

    /**
     * 返回队列是否为空
     * */
    auto Empty() const -> decltype(data_queue.empty()) {
        std::lock_guard<std::mutex> lk(mut);
        return data_queue.empty();
    }

    /**
     * 返回队列中元素数个
     * */
    auto Size() const -> decltype(data_queue.size()) {
        std::lock_guard<std::mutex> lk(mut);
        return data_queue.size();
    }

    /**
     * 清空队列
     * */
    void Clear()  {
        std::lock_guard<std::mutex> lk(mut);
        while (!data_queue.empty())
        {
            data_queue.pop();
        }
    }

}; /* ThingQueue */


#endif //GATEWAY_IOT_THING_QUEUE_H
