//
// Created by zhx on 2019/9/2.
//


#include <memory>
#include "thing_tool.h"

#define VER_LENGTH 3        //版本号长度
#define TIMESTAMP_LENGTH 10 //时间戳长度
#define SIGN_HEX_LENGTH 32  //签名HEX字符串长度
#define LINUX_IOT_VERSION "1.0"




/**
 * AES 128 ECB 加密
 * @param source
 * @param key
 * @return
 */
std::string Aes128EcbEncrypt(const std::string& source, const std::string& key)
{
    EVP_CIPHER_CTX *ctx;
    ctx = EVP_CIPHER_CTX_new();
    int ret = EVP_EncryptInit_ex(ctx, EVP_aes_128_ecb(), NULL, (const unsigned char*)key.data(), NULL);
    assert(ret == 1);
    unsigned char* result = new unsigned char[source.length() + 64];
    int len1 = 0;
    ret = EVP_EncryptUpdate(ctx, result, &len1, (const unsigned char*)source.data(), source.length());
    assert(ret == 1);
    int len2 = 0;
    ret = EVP_EncryptFinal_ex(ctx, result + len1, &len2);
    assert(ret == 1);
    ret = EVP_CIPHER_CTX_cleanup(ctx);
    assert(ret == 1);
    EVP_CIPHER_CTX_free(ctx);
    std::string res((char*)result, len1 + len2);
    delete[] result;
    return res;
}


/**
 * AES 128 ECB 解密
 * @param cipherText
 * @param key
 * @return
 */
std::string Aes128EcbDecrypt(const std::string& cipherText, const std::string& key)
{

    EVP_CIPHER_CTX *ctx;
    ctx = EVP_CIPHER_CTX_new();
    int ret = EVP_DecryptInit_ex(ctx, EVP_aes_128_ecb(), NULL, (const unsigned char*)key.data(), NULL);
    assert(ret == 1);
    unsigned char* result = new unsigned char[cipherText.length() + 64]; // 弄个足够大的空间
    int len1 = 0;
    ret = EVP_DecryptUpdate(ctx, result, &len1, (const unsigned char*)cipherText.data(), cipherText.length());
    assert(ret == 1);
    int len2 = 0;
    ret = EVP_DecryptFinal_ex(ctx, result + len1, &len2);
    assert(ret == 1);
    ret = EVP_CIPHER_CTX_cleanup(ctx);
    assert(ret == 1);
    EVP_CIPHER_CTX_free(ctx);
    std::string res((char*)result, len1 + len2);
    delete[] result;
    return res;
}


/**
 * 16进制数组转string
 * @param s
 * @param phex
 * @param length
 */
const unsigned char HEX_ARRAY[] = "0123456789abcdef";
void HexArrayToString(std::string &s, unsigned char *pHex, int length)
{
    int i, j = 0;
    s.resize(length * 2);
    for (i = 0, j = 0; i < length; i++)
    {
        s[j++] = HEX_ARRAY[((pHex[i] >> 4) & 0x0f)];
        s[j++] = HEX_ARRAY[(pHex[i] & 0x0f)];
    }
}

/**
 * base64加密
 * @param sIn 需要加密的字符串
 * @return 加密后的字符串
 */
std::string Base64Encode(char *sIn, int len)
{
    BIO *bmem = NULL;
    BIO *b64 = NULL;
    BUF_MEM *bptr = NULL;
    char *pOut = NULL;
    std::string s;

    if (sIn == nullptr)
        return "";

    b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);

    bmem = BIO_new(BIO_s_mem());
    b64 = BIO_push(b64, bmem);
    BIO_write(b64, sIn, len);
    BIO_flush(b64);
    BIO_get_mem_ptr(b64, &bptr);

    pOut = (char *)malloc(bptr->length + 1);
    if (NULL == pOut)
    {
        return "";
    }

    memcpy(pOut, bptr->data, bptr->length);
    pOut[bptr->length] = '\0';
    s = pOut;
    free(pOut);
    BIO_free_all(b64);
    return s;
}

/**
 * base64加密
 * @param sIn 需要加密的字符串
 * @return 加密后的字符串
 */
std::string Base64Encode(std::string &sIn)
{
    BIO *bmem = NULL;
    BIO *b64 = NULL;
    BUF_MEM *bptr = NULL;
    char *pOut = NULL;
    std::string s;

    if (sIn.empty())
        return NULL;

    b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);

    bmem = BIO_new(BIO_s_mem());
    b64 = BIO_push(b64, bmem);
    BIO_write(b64, sIn.c_str(), sIn.length());
    BIO_flush(b64);
    BIO_get_mem_ptr(b64, &bptr);

    pOut = (char *)malloc(bptr->length + 1);
    if (NULL == pOut)
    {
        return NULL;
    }

    memcpy(pOut, bptr->data, bptr->length);
    pOut[bptr->length] = '\0';
    s = pOut;
    free(pOut);
    BIO_free_all(b64);
    return s;
}


/**
 * base64解密
 * @param sIn 需要解密的字符串
 * @return 解密后的字符串
 */
std::string Base64Decode(std::string &sIn)
{
    BIO *bmem = NULL;
    BIO *b64 = NULL;
    BUF_MEM *bptr = NULL;

    int size = 0;
    char *pOut = NULL;

    if (sIn.empty())
        return NULL;

    b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);

    bmem = BIO_new_mem_buf(sIn.c_str(), sIn.length());
    bmem = BIO_push(b64, bmem);

    pOut = (char *)malloc(sIn.length());
    if (NULL == pOut)
    {
        return NULL;
    }

    size = BIO_read(bmem, pOut, sIn.length());
    std::string s = std::string(pOut, size);
    free(pOut);
    BIO_free_all(bmem);

    return s;
}




/**
 * 生成MQTT连接clientId
 * @param productKey
 * @param deviceName
 * @return
 */
std::string GetMqttClientId(std::string &productKey, std::string &deviceName)
{
    return productKey + deviceName;
}


/**
 * 获取随机数接口
 * @param p 存放随机数缓冲
 * @param len 缓冲长度
 * @return 1 ，成功， 0 失败；
 */
int GetRandom(unsigned char *p, int len)
{
    //TODO 随机数由数字和字母生成
    int ret;
    ret = RAND_bytes(p, len);
    return ret;
}

/**
 * 获取时间戳
 * @return
 */
std::string GetTimeStamp(void)
{
    std::string s;
    std::stringstream ss;
    struct timeval stamp;
    gettimeofday(&stamp, NULL);
    ss << stamp.tv_sec;
    ss >> s;
    return s;
}


/**
 * 生成MQTT连接使用的username
 * @param productKey
 * @param deviceName
 * @return
 */
std::string GetMqttUsername(std::string &productKey, std::string &deviceName)
{
    std::string nonce;
    std::string timestamp;
    // nonce编码(唯一随机数)
    unsigned char pRandom[6];
    GetRandom(pRandom, 6);
    HexArrayToString(nonce, pRandom, 6);

    // timestamp编码
    timestamp = GetTimeStamp();

    return productKey + deviceName + "|" + nonce + "|" + timestamp;
}



/**
 * MQTT连接使用的密码
 * ⽤ hmacmd5 算法对 secret_key 和 username ⽣成摘要，得到 sign 即密码
 * @param deviceSecret
 * @param userName
 * @return
 */
std::string GetMqttPassword(std::string &deviceSecret, std::string &userName)
{
    std::string hexStr;
    unsigned char pMD5[26] = {0};
    unsigned int nLen;
    HMAC(EVP_md5(),
         deviceSecret.c_str(),
         deviceSecret.length(),
         (unsigned char*)userName.c_str(),
         userName.length(),
         pMD5,
         &nLen);


    HexArrayToString(hexStr, pMD5, 16);
    return  hexStr;
}


/**
 *
 * (数据发送时)拼接data、version、timestamp（时间戳）得到字符串content，使用deviceSecret秘钥根据HAMCMD5签名
 * @param content
 * @param deviceSecret
 */
std::string sign_payload(std::string &content, std::string &deviceSecret)
{
    //hmac签名
    std::string hexStr;
    unsigned char pMD5[26] = {0};
    unsigned int nLen;
    HMAC(EVP_md5(),
         deviceSecret.c_str(),
         deviceSecret.length(),
         (unsigned char*)content.c_str(),
         content.length(),
         pMD5,
         &nLen);


    HexArrayToString(hexStr, pMD5, 16);
    return  hexStr;
}


/**
 * 最终要发送的payload
 * 固定顺序拼接version、timestamp、sign和data
 * @param sign
 * @param data
 * @return
 */

std::string splice(std::string &sign,std::string &timeStr, std::string &data)
{
    return  LINUX_IOT_VERSION + timeStr + sign + data;
}


/**
 * 打包发送的数据
 * @param payload
 * @param deviceSecret
 * @param outResult 输出结果
 * @return
 */
void PackMqttMessage(std::string &payload, std::string &deviceSecret, std::string &outResult)
{
    std::string encrypt = Aes128EcbEncrypt(payload, deviceSecret);
    std::string base64Data = Base64Encode(encrypt);
    std::string timeStamp = GetTimeStamp();
    std::string signContent = base64Data + LINUX_IOT_VERSION + timeStamp;
    std::string sign = sign_payload(signContent, deviceSecret);
    outResult = splice(sign, timeStamp, base64Data);
}


/**
 * 打包发送的数据
 * @param payload
 * @param deviceSecret
 * @param outResult 输出结果
 * @return
 */
void PackMqttMessage(Json::Value &payload, std::string &deviceSecret, std::string &outResult)
{
    //Json::FastWriter fWriter;
    //std::string payloadStr = fWriter.write(payload);
    Json::StreamWriterBuilder jswBuilder;
    jswBuilder["indentation"  ] = ""; // 缩进为 2 个空格
    //jswBuilder["emitUTF8"     ] = true; // 直接输出 UTF-8 字符
    jswBuilder["precisionType"] = "significant"; // 用科学计数法表示浮点数
    jswBuilder["precision"    ] = 6;             // 6 位有效数字

    std::unique_ptr<Json::StreamWriter>jsWriter(jswBuilder.newStreamWriter());
    std::ostringstream os;
    jsWriter->write(payload, &os);
    std::string payloadStr = os.str().c_str();

#if IOT_ENCRYPT
    //std::cout << "JSON: " << payloadStr <<std::endl;

    std::string encrypt = aes128EcbEncrypt(payloadStr, deviceSecret);
    std::string base64Data = base64Encode(encrypt);
    std::string timeStamp = getTimeStamp();
    std::string signContent = base64Data + LINUX_IOT_VERSION + timeStamp;
    std::string sign = sign_payload(signContent, deviceSecret);
    outResult = splice(sign, timeStamp, base64Data);

#else
    outResult = payloadStr;
#endif

}



/**
 * 解包，MQTT接收到的消息包
 * @param payload
 * @param payloadLen
 * @param deviceSecret
 * @param outResult
 * @return
 */
int UnpackMqttMessage(const char *payload, int payloadLen, std::string &deviceSecret, std::string &outResult)
{
    if (strlen(payload) != payloadLen)
        return -1;

    /* 版本号 */
    char ver[5] = {0};
    /* 时间戳 */
    char time[16] = {0};
    /* 平台签名 */
    char signServer[35] = {0};
    memcpy(ver, payload, VER_LENGTH);
    memcpy(time, payload + VER_LENGTH, TIMESTAMP_LENGTH);
    memcpy(signServer, payload + VER_LENGTH + TIMESTAMP_LENGTH, SIGN_HEX_LENGTH);
    std::string aesData = payload + (VER_LENGTH + TIMESTAMP_LENGTH + SIGN_HEX_LENGTH);
    std::string content = aesData + ver + time;
    std::string signLocal = sign_payload(content, deviceSecret);
    if (signLocal != signServer)
        return -1;

    std::string base64 = Base64Decode(aesData);
    outResult = Aes128EcbDecrypt(base64, deviceSecret);
    return 0;
}


