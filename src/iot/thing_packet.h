/**
 * 数据打包
 */
#ifndef LINUXIOT_MESSAGEPROTOCOL_H
#define LINUXIOT_MESSAGEPROTOCOL_H
#include <json/value.h>
#include "thing_tool.h"
#include <common_func.h>
#include <string>
using namespace std;

/**
 * 获取能力集事件上报的主题
 * @param productKey    产品key
 * @param deviceId      设备ID
 * @param identifier    事件ID
 * @return
 */
string GetEventReportTopic(const string &productKey, const string& deviceId, const string& identifier);

/**
 * 获取能力集事件上报方法
 * @param identifier    事件ID
 * @return
 */
string GetEventReportMethod(const string &identifier);


/**
 * 打包OTA相关的消息。
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackOtaMsgBody(const Json::Value &params);


/**
 * 获取事件消息体
 * @param params
 * @param action
 * @return
 */
Json::Value PackEventMsgBody(const Json::Value& params, const char *action);


/**
 * 打包能力集应答消息
 * @param msgId
 * @param code
 * @param data
 * @return
 */
Json::Value PackReplyMsgBody(string &msgId, int code, const Json::Value &data = "");


/**
 * 打包消息体
 * @param params
 * @param action
 * @return
 */
Json::Value PackCommonMsgBody(const Json::Value &params, const char *action);


/**
 * 打包普通消息体，属性上报、设备注册绑定上线通用。
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackPropertyReportMsgBody(const Json::Value &params, const char *action);
/**
 * 打包指定的属性
 * @param paraJs
 * @param members
 * @return
 */
Json::Value PackSpecialParams(Json::Value paraJs, const vector<string> &members);

/**
 * 打包指定的属性，附加时间
 * @param paraJs
 * @param members
 * @return
 */
Json::Value PackSpecialParamsWithTime(Json::Value paraJs, const vector<string>& members);

/**
 * 数据拷贝
 * @param to
 * @param from
 * @return
 */
int CopyValueWithTheSameKey(Json::Value &to, Json::Value &from);


/**
 * 根据指定的key获取属性
 * @param msgId
 * @param propertyJson
 * @param value
 * @return
 */
Json::Value GetPropertyWithSpecialKeys(string &msgId, const Json::Value& propertyJson, Json::Value &value);


/**
 * 打包消息主题
 * @param topicList
 */
void PackTopic(string prefix, string &productKey, string &deviceName, vector<string> &topicList);



/**
 * 从主题中获取设备id
 * @param topic
 * @return
 */
string GetDeviceIdFromTopic(string topic);


/**
 * 获取产品key
 * @param topic
 * @return
 */
string GetProductKeyByTopic(string topic);



/**
 * 通过TOPIC获取productKey和name
 * @param topic
 * @param product_key
 * @param device_name
 * @return
 */
int GetProductKeyAndDeviceNameFromTopic(string topic, string &product_key, string &device_name);
#endif //LINUXIOT_MESSAGEPROTOCOL_H
