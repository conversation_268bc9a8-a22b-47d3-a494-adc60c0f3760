/**
 * 能力集设备类，抽象了设备的查询属性、设置属性、
 */
#ifndef ThingDevice_DEVICE_H
#define ThingDevice_DEVICE_H

#include "json/json.h"
#include <string>
#include <common_func.h>


using namespace std;


#define J_PARAMS "params"
#define J_ID "id"
#define J_ACTION "action"
#define J_CODE "code"
#define J_MESSAGE "message"
#define J_DATA "data"




#define TOPIC_RES_REG_DYNAMIC "/bind/register/dynamic_res"   //动态注册应答
#define TOPIC_RES_REG_AUTO "/bind/register/auto_res"         //自动注册应答
#define TOPIC_RES_BIND "/bind/mapping/add_res"               //添加绑定应答
#define TOPIC_RES_CONNECT "/bind/session/connect_res"        //子设备上线应答
#define TOPIC_RES_DELETE "/bind/mapping/delete_res"          //删除绑定应答


#define TOPIC_RES_PRO_POST "/event/property/post_res"        //属性上报应答
#define TOPIC_PRO_GET "/service/property/get"                //获取属性应答
#define TOPIC_PRO_SET "/service/property/set"                //设置属性应答

#define IS_TOPIC_MESSAGE(a, b) a.find(b) != std::string::npos



class ThingDevice
{
public:
    string device_name;         //等同deviceName
    string device_secret;       //deviceSecret
    string product_key;         //产品key
    string product_secret;      //产品秘钥
    string message_id;          //接收到的mID
    bool is_register = false;   //是否注册
    bool is_bind = false;       //是否绑定
    bool is_online = false;     //设备是否上线
    bool is_gateway = false;    //是否是网关设备
    vector<string> m_topic;

    ThingDevice() =default;
    virtual ~ThingDevice() = default;




    /**
     * 收到平台能力集消息
     * @param str_topic     消息主题
     * @param j_value       payload
     * @param is_local_req  是否是本地请求，忽略该参数
     * @return
     */
    virtual bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool is_local_req) = 0;


    /**
     * 收到设备消息
     * @param sensor_data   设备数据
     * @return
     */
    virtual bool OnRxSensorMessage(string &sensor_data) = 0;


    /**
     * 发送消息到设备
     * @param base64Data base64编码后的数据
     * @return

    virtual int sendMessageToSensor(string &base64Data) { return 0;};
    */

    /**
     * 平台设置设备属性请求
     * @param req 属性
     * @return
     */
    virtual int SetProperty(Json::Value &req) = 0;


    /**
     * 平台获取设备数据请求
     * @param req 请求的数据
     * @param res 应答的数据
     * @return
     */
    virtual int GetProperty(Json::Value &req, Json::Value &res) = 0;


    /**
     * 设备上报属性
     * @return
     */
    virtual int PostProperty() =0;


    /**
     * 获取设备需要订阅的主题
     * @param topic
     */
    virtual void GetTopic(vector<string> &topic){
        for (const std::string &str : m_topic) {
            topic.emplace_back(str);
        }
    }


    /**
     * 计划任务
     * @return
     */
    virtual int ScheduledTasks(){return 0;};

};

#endif /*ThingDevice_DEVICE_H*/
