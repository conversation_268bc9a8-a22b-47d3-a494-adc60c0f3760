/**
 * 网关设备注册，包含生产请求数据，解析响应数据
 *
 * 示例：
    string action = "device_auto_register";
    string dev_type = 32;
    string product_secret = "abcdfsdfdsf";
    string version = "123";
    //按协议生成请求报文
    string realData = getGateWayDeviceRegisterBody(action,device_name,product_key,product_secret,version);

    Json::Value jResponse;
    string responseStr;
    //请求
    int ret = registerGatewayDevice(url, realData, responseStr,10);
    ..
    //解析报文
    ret = unPackRegisterMessage(product_secret,responseStr,jResponse);
    ..
 */
#ifndef HTTP_HELPER_H_
#define HTTP_HELPER_H_



#include "json/json.h"
#include "thing_tool.h"
#include "curl/curl.h"



#define HTTP_TIMEOUT 10         /*http请求最大等待时间*/
#define HEADER_APPLICATION_JSON "Content-Type:application/json"
#define HEADER_FORM_URLENCODED  "Content-Type:application/x-www-form-urlencoded"
#define HEADER_MULTIPART "Content-Type:multipart/form-data"

#define TYPE_HTTP_REQUEST    1
#define TYPE_HTTP_REPLY      2

#define _PARAM_ACTION       "action"
#define _PARAM_VERSION      "version"
#define _PARAM_KEY          "key"
#define _PARAM_SIGN         "sign"
#define _PARAM_NONCE        "nonce"
#define _PARAM_TIMESTAMP    "timestamp"
#define _PARAM_DATA         "data"

#define _PARAM_ID           "id"
#define _PARAM_CODE         "code"
#define _PARAM_MESSAGE      "message"

#define _PARAM_DEVICE_NAME  "deviceName"
#define _PARAM_PRODUCT_KEY  "productKey"

/**
 * 获取网关设备注册的完整请求数据
 * @param action        自动注册“device_auto_register”
 * @param deviceName
 * @param productKey
 * @param productSecret
 * @param version       版本
 * @return
 */
string getGateWayDeviceRegisterBody(
        string &action,
        string &deviceName,
        string &productKey ,
        string &productSecret,
        string &version);


/**
 * 发送网关设备注册请求
 * @param url
 * @param data      按协议封装的数据
 * @param response  请求的应答数据
 * @param timeout   请求超时时间
 * @return 0表示网络请求成功 否则失败
 */
int registerGatewayDevice(string &url,
                          string &data,
                          string &productSecret,
                          string &response,
                          Json::Value &jResponse,
                          int timeout = HTTP_TIMEOUT);


/**
 * 解包注册返回的消息
 * @param productSecret
 * @param responseStr   http返回的原始数据
 * @param responseJson  解析后的JSON数据
 * @return 0表示注册成功，否则失败
 */
int unPackRegisterMessage(string &productSecret, string &responseStr, Json::Value &responseJson);
#endif /*HTTP_HELPER_H_*/

