/**
 * 能力集设备类，抽象了设备的注册、绑定、上线、订阅、解绑、同步等动作
 */
#ifndef GATEWAY_IOT_THING_ACTION_H
#define GATEWAY_IOT_THING_ACTION_H

#include "json/json.h"
#include "thing_device.h"
#include <string>
#include <functional>
#include "thing_interface.h"

using namespace std;

class RegisterResponse
{
public:
    string secret;
    string device_name;
};



/**
 * 能力集动作类
 */
class ThingAction {


public:

    ThingAction();
    virtual ~ThingAction() = default;


    string httpApiBaseAddress;          //HTTP 服务器基地址
    string httpApiPort;                 //HTTP端口
    string httpGatewayRegUrl;           //api地址
    //string gatewayProductSecret;      //产品秘钥
    string gatewayVersion;              //网关版本
    //ThingInterface *mInterface;       //适配器


    /**
     * 网关设备注册
     * @param gwProductKey
     * @param gwProductSecret
     * @param gwDeviceName
     * @param secret
     * @return
     */
    int GatewayDevRegister(string &gwProductKey, string &gwProductSecret, string &gwDeviceName, string &secret);


    /**
     * 子设备注册到平台，自动注册
     * @param gwProductKey
     * @param gwDeviceName
     * @param gwDeviceSecret
     * @param subProductKey
     * @param subDeviceName
     * @param result
     * @return
     */
    int DevRegister(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName, RegisterResponse &result);

    /**
     *
     * 子设备绑定到网关
     * @param gatewayDevice
     * @param device
     * @return
     */
    int DevBind(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName);

    /**
     * 子设备取消设备绑定
     * @param gwProductKey
     * @param gwDeviceName
     * @param gwDeviceSecret
     * @param subProductKey
     * @param subDeviceName
     * @return
     */
    int DevCancelBind(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName);


    /**
     * 子设备上线
     * @param gwProductKey
     * @param gwDeviceName
     * @param gwDeviceSecret
     * @param subProductKey
     * @param subDeviceName
     * @return
     */
    int DevOnline(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName);



    /**
     * 子设备下线
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @return
     */
    int DevOffline(string &subProductKey, string &subDeviceName, string &subDeviceSecret);


    /**
     * 子设备同步
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @return
     */
    int DevSync(string &subProductKey, string &subDeviceName, string &subDeviceSecret);


    /**
     * 设备订阅消息主题
     * @param topicList
     * @return
     */
    int DevSubscribeTopic(vector<string> &topicList);


    /**
     * 向平台上报设备事件
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @param event_name
     * @param data
     * @return
     */
    int ReportDevEvent(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string event_name, Json::Value &data);


    /**
     * 上报属性给IOT平台
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @param data
     * @return
     */
    int ReportPropEvent(string &subProductKey, string &subDeviceName, string &subDeviceSecret, Json::Value &data);


    /**
     * 设备上报固件版本
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @param version
     * @return
     */
    int ReportVersion(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string &version);


    /**
     * 设备上报固件升级进度
     * @param subProductKey
     * @param subDeviceName
     * @param subDeviceSecret
     * @param version
     * @return
     */
    int ReportOTAProgress(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string &step, string &desc);

    /**
     * 应答平台的设置属性、获取属性、和服务调用
     * @param subDeviceSecret
     * @param msgId
     * @param code
     * @param topic
     * @param value
     * @return
     */
    int ResponseToPlatform(string &subDeviceSecret, string &msgId, int code, string &topic, Json::Value &value);


    /**
     * 解密从IoT平台返回的能力集消息
     * @param devSecret     设备秘钥，需要通过外部接口查询
     * @param topic         消息主题
     * @param payload       消息负荷
     * @return
     */
    Json::Value ReceiveMessage(string devSecret, string &topic, string &payload);


    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    virtual int Send(string &topic, string &payload) =0;



    /**
     * 发送设备数据，外部实现真正的发送
     * @param topic
     * @param payload
     * @return
     */
    virtual int Send(string &topic, string &payload, long messageId, Json::Value &response) = 0;


    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    virtual int SubscribeTopic(int &mid, string &topic) = 0;
};


#endif //GATEWAY_IOT_THING_ACTION_H
