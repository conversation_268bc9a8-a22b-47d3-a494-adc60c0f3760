//
// Created by seashell on 10/29/21.
//

#include "thing_action.h"
#include "thing_method.h"
#include "thing_http.h"


/**
 * 网关设备注册
 * @param gwProductKey
 * @param gwProductSecret
 * @param gwDeviceName
 * @param secret
 * @return
 */
int ThingAction::GatewayDevRegister(string &gwProductKey, string &gwProductSecret, string &gwDeviceName, string &secret)
{
    string sResp;
    stringstream ss;
    ss << "http://" << httpApiBaseAddress << ":" << httpApiPort << httpGatewayRegUrl;
    string url = ss.str();
    string action = "device_auto_register";
    string realData = getGateWayDeviceRegisterBody(action, gwDeviceName, gwProductKey, gwProductSecret, gatewayVersion);

    Json::Value jResponse;
    string responseStr;
    int ret = registerGatewayDevice(url, realData, gwProductSecret,responseStr,jResponse, 10);
    if (ret == RET_OK)
    {
        /*注册成功*/
        secret = jResponse["deviceSecret"].asString();
        return RET_OK;
    }
    else
    {
        /*注册失败或者已注册*/
        LOG(ERROR) << responseStr;
        if (jResponse["code"].asString() == "4009")
        {
            LOG(ERROR) << "网关注册失败,错误码:4009，设备名重复：" << gwDeviceName;
        }

    }

    return RET_FAIL;
}



/**
 * 子设备注册到平台，自动注册
 * @param gwProductKey
 * @param gwDeviceName
 * @param gwDeviceSecret
 * @param subProductKey
 * @param subDeviceName
 * @param result
 * @return
 */
int ThingAction::DevRegister(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName, RegisterResponse &result)
{
    LOG(INFO) << "register sub subDevice, subDevice id: " << subDeviceName;
    string outData;
    Json::Value data;
    DeviceRegister(subDeviceName, subProductKey, data);
    string topic = DeviceRegisterTopic(gwDeviceName, gwProductKey);
    PackMqttMessage(data, gwDeviceSecret, outData);

    long messageId = stoll(data["id"].asString());
    Json::Value response;
    if (Send(topic, outData, messageId, response) == RET_OK)
    {
        LOG(INFO) << "register response: " << jsonTostring(response);
        if (response.isMember(J_CODE) && ("0" == response[J_CODE].asString()))
        {
            result.device_name = response[J_DATA]["deviceName"].asString();
            result.secret = response[J_DATA]["deviceSecret"].asString();
            LOG(INFO) << "register subDevice success, device_secret->" << result.secret << "  " << "device_name->" << result.device_name;
            return RET_OK;
        } else{
            LOG(ERROR) << "register subDevice failed," << jsonTostring(response);
        }

    } else{
        LOG(INFO) << "register sub subDevice failed, subDevice id: " << subDeviceName;
    }

    return RET_FAIL;
}


/**
 *
 * 子设备绑定到网关
 * @param gatewayDevice
 * @param device
 * @return
 */
int ThingAction::DevBind(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName)
{
    LOG(INFO) << "bind sub device to gateway device.";
    Json::Value data;
    string outData;
    DeviceBind(subDeviceName, subProductKey, data);
    string topic = DeviceBindTopic(gwDeviceName, gwProductKey);
    PackMqttMessage(data, gwDeviceSecret, outData);

    long messageId = stoll(data["id"].asString());
    Json::Value response;
    if (Send(topic, outData, messageId, response) == RET_OK)
    {
        if (response.isMember(J_CODE) && ("0" == response[J_CODE].asString()))
        {
            LOG(INFO) << "sub device bind response: " << jsonTostring(response);
            return  RET_OK;
        }else{
            LOG(ERROR) << "bind device failed," << jsonTostring(response);
        }
    } else{
        LOG(ERROR) << "bind sub device to gateway device failed.";
    }
    return RET_FAIL;
}



 /**
  * 子设备取消设备绑定
  * @param gwProductKey
  * @param gwDeviceName
  * @param gwDeviceSecret
  * @param subProductKey
  * @param subDeviceName
  * @return
  */
int ThingAction::DevCancelBind(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName)
{
    string outData;
    Json::Value data;
    DeviceCancelBind(subDeviceName, subProductKey, data);
    string topic = DeviceBindTopic(gwDeviceName, gwProductKey);
    PackMqttMessage(data, gwDeviceSecret, outData);

    long messageId = stoll(data["id"].asString());
    Json::Value response;
    if (Send(topic, outData, messageId, response) == RET_OK)
    {
        LOG(INFO) << "sub device delete bind response: " << jsonTostring(response);
        if (response.isMember(J_CODE) && ("0" == response[J_CODE].asString()))
        {
            return  RET_OK;
        }
    }
    return RET_FAIL;
}


/**
 * 子设备上线
 * @param gwProductKey
 * @param gwDeviceName
 * @param gwDeviceSecret
 * @param subProductKey
 * @param subDeviceName
 * @return
 */
int ThingAction::DevOnline(string &gwProductKey, string &gwDeviceName, string &gwDeviceSecret, string &subProductKey, string &subDeviceName)
{
    LOG(INFO) << "online device.";

    string outData;
    Json::Value data;
    DeviceOnline(subDeviceName, subProductKey, data);
    string topic = DeviceOnlineTopic(gwDeviceName, gwProductKey);
    PackMqttMessage(data, gwDeviceSecret, outData);

    long messageId = stoll(data["id"].asString());
    Json::Value response;
    if (Send(topic, outData, messageId, response) == RET_OK)
    {

        if (response.isMember(J_CODE) && ("0" == response[J_CODE].asString()))
        {
            LOG(INFO) << "sub device online response: " << jsonTostring(response);
            return  RET_OK;
        }else{
            LOG(ERROR) << "online device failed," << jsonTostring(response);
        }
    } else{
        LOG(INFO) << "online sub device failed.";
    }
    return RET_FAIL;
}


/**
 * 子设备下线
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @return
 */
int ThingAction::DevOffline(string &subProductKey, string &subDeviceName, string &subDeviceSecret)
{
    string outData;
    string topic;
    Json::Value data;
    DeviceOffline(subDeviceName, subProductKey, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);
}



/**
 * 子设备同步
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @return
 */
int ThingAction::DevSync(string &subProductKey, string &subDeviceName, string &subDeviceSecret)
{
    string outData;
    string topic;
    Json::Value data;
    DeviceSync(subDeviceName, subProductKey, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);

}


/**
 * 设备订阅消息主题
 * @param topicList
 * @return
 */
int ThingAction::DevSubscribeTopic(vector<string> &topicList)
{
    int mid = 1;
    for (auto & i : topicList)
    {
        LOG(INFO) << i << endl;
        if (SubscribeTopic(mid, i) != RET_OK)
            return RET_FAIL;
    }

    return RET_OK;
}




/**
 * 向平台上报设备事件
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @param event_name
 * @param data
 * @return
 */
int ThingAction::ReportDevEvent(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string event_name, Json::Value &data)
{
    string outData;
    string topic;
    DeviceReportDeviceEvent(subDeviceName, subProductKey, event_name, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);

}



/**
 * 上报属性给IOT平台
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @param data
 * @return
 */
int ThingAction::ReportPropEvent(string &subProductKey, string &subDeviceName, string &subDeviceSecret, Json::Value &data)
{
    string outData;
    string topic;
    DeviceReportPropEvent(subDeviceName, subProductKey, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);

}



/**
 * 设备上报固件版本
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @param version
 * @return
 */
int ThingAction::ReportVersion(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string &version)
{
    string outData;
    string topic;
    Json::Value data;
    DeviceReportVersion(subDeviceName, subProductKey, version, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);

}



/**
 * 设备上报固件升级进度
 * @param subProductKey
 * @param subDeviceName
 * @param subDeviceSecret
 * @param version
 * @return
 */
int ThingAction::ReportOTAProgress(string &subProductKey, string &subDeviceName, string &subDeviceSecret, string &step, string &desc)
{
    string outData;
    string topic;
    Json::Value data;
    DeviceReportOTAProgress(subDeviceName, subProductKey, step, desc, topic, data);
    PackMqttMessage(data, subDeviceSecret, outData);
    return Send(topic, outData);
}
/**
 * 应答平台的设置属性、获取属性、和服务调用
 * @param subDeviceSecret
 * @param msgId
 * @param code
 * @param topic
 * @param value
 * @return
 */
int ThingAction::ResponseToPlatform(string &subDeviceSecret, string &msgId, int code, string &topic, Json::Value &value)
{
    string outData;
    DeviceResponseToPlatform(msgId, code, value);
    PackMqttMessage(value, subDeviceSecret, outData);
    return Send(topic, outData);
}



/**
 * 解密从IoT平台返回的能力集消息
 * @param devSecret
 * @param topic
 * @param payload
 * @return
 */
Json::Value ThingAction::ReceiveMessage(string devSecret, string &topic, string &payload)
{
    string realData;

#if IOT_ENCTYPT
    unpackMqttMessage((const char *) payload.c_str(), payload.length(), devSecret, realData);
    return stringToJson(realData);
#else
    return stringToJson(payload);
#endif
}


ThingAction::ThingAction() {

    /**
     * curl全局初始化
     */
    curl_global_init(CURL_GLOBAL_NOTHING);

}










