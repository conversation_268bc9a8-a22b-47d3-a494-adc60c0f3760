//
// Created by zhx on 2020/1/16.
//


#include <regex>
#include "thing_packet.h"


using namespace std;
static int mSendID; //发送给服务端的mID
/**
 * 拼装事件上报topic
 * @param productKey
 * @param deviceId
 * @param identifier
 * @return
 */
string GetEventReportTopic(const string &productKey, const string &deviceId, const string &identifier)
{
    string topic;

    topic.append("$thing/");
    topic.append(productKey);
    topic.append("/");
    topic.append(deviceId);
    topic.append("/event/");
    topic.append(identifier);
    topic.append("/post");

    return topic;
}

/**
 * 获取事件上报方法
 * @param identifier
 * @return
 */
string GetEventReportMethod(const string &identifier)
{
    string method;
    method.append("thing.event.");
    method.append(identifier);
    method.append(".post");
    return method;
}

/**
 * 打包回复消息体
 * @param code
 * @param data
 * @return
 */
Json::Value PackReplyMsgBody(string &msgId, int code, const Json::Value &data)
{
    Json::Value msgBody;

    msgBody["id"] = msgId;
    msgBody["code"] = to_string(code);
    msgBody["data"] = data;
    return msgBody;
}

/**
 * 打包普通消息体，属性上报、设备注册绑定上线通用。
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackCommonMsgBody(const Json::Value &params, const char *action)
{
    Json::Value msgBody;

    msgBody["id"] = to_string(mSendID++);
    msgBody["params"] = params;
    msgBody["action"] = Json::Value(action);

    return msgBody;
}


/**
 * 打包普通消息体，属性上报、设备注册绑定上线通用。
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackPropertyReportMsgBody(const Json::Value &params, const char *action)
{
    Json::Value allParams;
    Json::Value::Members members;
    members = params.getMemberNames();
    for (const auto &member : members)
    {
        Json::Value temp;
        allParams[member]["value"] = params[member];
        allParams[member]["time"] = GetTimeStamp();
    }

    Json::Value msgBody;
    msgBody["id"] = to_string(mSendID++);
    msgBody["params"] = allParams;
    msgBody["action"] = Json::Value(action);

    return msgBody;
}


/**
 * 打包OTA相关的消息。
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackOtaMsgBody(const Json::Value &params)
{
    Json::Value msgBody;

    msgBody["id"] = to_string(mSendID++);
    msgBody["params"] = params;

    return msgBody;
}


/**
 * 打包事件上报发送消息
 * @param params 消息体包含的业务参数
 * @param action 方法id
 * @return
 */
Json::Value PackEventMsgBody(const Json::Value &params, const char *action)
{
    Json::Value msgBody;
    Json::Value jValue;

    jValue["value"] = params;
    jValue["time"] = GetTimeStamp();
    msgBody["id"] = to_string(mSendID++);
    msgBody["params"] = jValue;
    msgBody["action"] = Json::Value(action);

    return msgBody;
}

/**
 * 从paraJs中提取members中出现的key，组成一个新的json
 * 属性获取或者设备注册时使用到，打包一个属性
 * @param paraJs   源JSON数据
 * @param members  指定的的key数组
 * @return
 */
Json::Value PackSpecialParams(Json::Value paraJs, const vector<string> &members)
{
    Json::Value params;
    for (const auto &member : members)
    {
        if (paraJs.isMember(member))
            params[member] = paraJs[member];
    }
    return params;
}

/***
 * 从paraJs中提取members中出现的key，组成一个新的json，并且每个新的属性节点包含value和time属性
 * 属性上报时打包属性
 * "switch": {
			"value": "on",
			"time": "1537846356"
		}
 * @param paraJs
 * @param members
 * @return
 */
Json::Value PackSpecialParamsWithTime(Json::Value paraJs, const vector<string> &members)
{
    Json::Value params;
    for (const auto &member : members)
    {
        if (paraJs.isMember(member))
        {
            Json::Value temp;
            temp["value"] = paraJs[member];
            temp["time"] = GetTimeStamp();
            params[member] = temp;
        }
    }
    return params;
}

/**
 * 拷贝两边都存在的key所对应的值
 * @param to
 * @param from
 * @return
 */
int CopyValueWithTheSameKey(Json::Value &to, Json::Value &from)
{
    int ret = RET_OK;
    Json::Value::Members members;
    members = from.getMemberNames();
    for (const auto &member : members)
    {
        if (to.isMember(member))
            to[member] = from[member];
        else
            ret = RET_FAIL;
    }
    return ret;
}

/**
 * 根据平台指定要获取的属性名（key）,生成应答消息。
 * @param value 平台请求的属性JSON数组
 * @return
 */
Json::Value GetPropertyWithSpecialKeys(string &msgId, const Json::Value &propertyJson, Json::Value &value)
{
    vector<string> get_params;
    for (const auto &i : value)
    {
        get_params.push_back(i.asString());
    }
    Json::Value params_json = PackSpecialParams(propertyJson, get_params);
    return PackReplyMsgBody(msgId, 0, params_json);
}

/**
 * 打包消息主题
 * @param productKey
 * @param deviceName
 * @param topicList
 */
void PackTopic(string prefix, string &productKey, string &deviceName, vector<string> &topicList)
{
    for (auto &topic : topicList)
    {
        stringReplace(topic, ".", "/");
        topic = prefix + "/" + productKey + "/" + deviceName + "/" + topic;
    }
}




/**
 * 获取设备product_key
 * @param topic
 * @return
 */
string GetProductKeyByTopic(string topic)
{
    string product_key;
    smatch  result;
    regex pattern("(\\$gateway|\\$thing)\\/(.{16})\\/");
    if(regex_search(topic,result,pattern)) {
        for (const auto &it : result)
            product_key = it;
    }

    if (topic.find("$ota") != string::npos)
    {
        smatch  ota_result1;
        regex pattern3("(\\$ota\\/update\\/firmware)\\/(.{16})\\/");
        if(regex_search(topic,ota_result1,pattern3)) {
            for (const auto &it : ota_result1)
                product_key = it;
        }

    }

    return product_key;
}




/**
 * 获取设备id
 * @param topic
 *
    string str = "mb/FS4Ftn1DBFy39zS6/8cdKBy4T7XG421Wu/thing/event/prop/post";
    regex pattern("mb/(.{16}\\/.*)");
    if(regex_match(str,pattern)){
        LOG(ERROR) << "success------------";
    } else{
        LOG(ERROR) << "failure------------";
    }
    smatch  result;
    regex pattern2("mb/(.{16})\\/");
    if(regex_search(str,result,pattern2)){
        for (auto it = result.begin(); it != result.end(); ++it)
            LOG(ERROR) << *it <<endl;
 * @return
 */
string GetDeviceIdFromTopic(string topic)
{
    string strDeviceId;
    smatch  result;
    regex pattern2("(\\$gateway|\\$thing)\\/(.{16})\\/(.{16})\\/");
    if(regex_search(topic,result,pattern2)) {
        for (const auto &it : result)
            strDeviceId = it;
    }

    if (topic.find("$ota") != string::npos)
    {
        smatch  ota_result2;
        regex pattern4("(\\$ota\\/update\\/firmware)\\/(.{16})\\/(.{16})");
        if(regex_search(topic,ota_result2,pattern4)) {
            for (const auto &it : ota_result2)
                strDeviceId = it;
        }
    }

    return strDeviceId;
}


/**
 * 通过TOPIC获取productKey和name
 * @param topic
 * @param product_key
 * @param device_name
 * @return
 */
int  GetProductKeyAndDeviceNameFromTopic(string topic, string &product_key, string &device_name)
{
    smatch  key_result;
    regex pattern("(\\$gateway|\\$thing)\\/(.{16})\\/");
    if(regex_search(topic,key_result,pattern)) {
        for (const auto &it : key_result)
            product_key = it;
    }

    smatch  name_result;
    regex pattern2("(\\$gateway|\\$thing)\\/(.{16})\\/(.{16})\\/");
    if(regex_search(topic,name_result,pattern2)) {
        for (const auto &it : name_result)
            device_name = it;
    }


    if (topic.find("$ota") != string::npos)
    {
        smatch  ota_result1;
        regex pattern3("(\\$ota\\/update\\/firmware)\\/(.{16})\\/");
        if(regex_search(topic,ota_result1,pattern3)) {
            for (const auto &it : ota_result1)
                product_key = it;
        }

        smatch  ota_result2;
        regex pattern4("(\\$ota\\/update\\/firmware)\\/(.{16})\\/(.{16})");
        if(regex_search(topic,ota_result2,pattern4)) {
            for (const auto &it : ota_result2)
                device_name = it;
        }

    }


    return 0;
}