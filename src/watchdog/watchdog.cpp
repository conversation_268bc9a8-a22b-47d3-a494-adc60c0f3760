//
// Created by zhx on 2019/8/31.
//

#include "watchdog.h"
#include <unistd.h>
#include <sys/stat.h>
//#include <syslog.h>
#include <errno.h>
#include <fcntl.h>
#include <cstdio>
#include "glog/logging.h"


static int wdtFd = -1;


/**
 * 启动看门狗
 * @return
 */
int startDog()
{
    wdtFd = open("/dev/watchdog", O_WRONLY);
    if (wdtFd == -1){
        LOG(ERROR) << "fail to open watchdog DeviceAbility";
    }
    return wdtFd;
}


/**
 * 喂狗
 * @return
 */
int feedDog()
{
    if (wdtFd != -1){
        return  write(wdtFd, "a", 1);
    }
    return  -1;
}




/**
 * 杀狗，在升级或者特殊场景可能需要此操作
 * @return
 */
int killDog()
{
    if (wdtFd != -1)
    {
        if (write(wdtFd, "V", 1) != -1){
            close(wdtFd);
            wdtFd = -1;
            LOG(INFO) << "watchdog is closed\n";
        } else{
            return -1;
        }
    }
    return 0;
}