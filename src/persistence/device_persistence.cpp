//
// Created by seashell on 5/25/21.
//

#include <cstdio>
#include <iostream>
#include "device_persistence.h"
#include "SQLiteCpp/SQLiteCpp.h"

static SQLite::Database *db;


/**
 * 初始化设备
 * @return
 */
int DevicePersistence::initDB(){

    try
    {
        // Open a database file in create/write mode
        db = new SQLite::Database("gateway.db3", SQLite::OPEN_READWRITE|SQLite::OPEN_CREATE);
        /* Create SQL statement */
        db->exec("CREATE TABLE IF NOT EXISTS DEVICE("  \
                "dev_eui TEXT PRIMARY KEY   NOT NULL," \
                "dev_name TEXT   NOT NULL," \
                "dev_secret           TEXT    NOT NULL," \
                "product_key           TEXT    NOT NULL," \
                "product_secret           TEXT    NOT NULL," \
                "alias           TEXT    NOT NULL," \
                "is_bind           INT    NOT NULL," \
                "is_register           INT    NOT NULL," \
                "is_gateway        INT     NOT NULL," \
                "company_id            TEXT     NOT NULL," \
                "dev_type            INT     NOT NULL," \
                "f_port        INT     NOT NULL," \
                "app_id         TEXT     NOT NULL );");

    }
    catch (std::exception& e)
    {
        LOG(ERROR) << "SQLite exception: " << e.what() << std::endl;
        return EXIT_FAILURE; // unexpected error : exit the example program
    }

    return EXIT_SUCCESS;
}



/**
 * 添加新设备
 * @param device
 * @return
 */
int DevicePersistence::addDevice(DeviceParam &device) {

    try
    {
        SQLite::Statement query(*db,"INSERT INTO DEVICE  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");
        query.bind(1,device.device_eui);
        query.bind(2,device.device_name);
        query.bind(3,device.device_secret);
        query.bind(4,device.product_key);
        query.bind(5,device.product_secret);
        query.bind(6,device.alias);
        query.bind(7,device.is_bind);
        query.bind(8,device.is_register);
        query.bind(9,device.is_gateway);
        query.bind(10,device.company_id);
        query.bind(11,device.dev_type);
        query.bind(12,device.f_port);
        query.bind(13,device.app_id);
        int nb = query.exec ();
        LOG(INFO) << "INSERT INTO DEVICE SUCCESSFULLY " << nb << std::endl;
    }
    catch (std::exception& e)
    {
        LOG(ERROR) << "SQLite exception: " << e.what() << std::endl;
        return EXIT_FAILURE; // unexpected error : exit the example program
    }

    return EXIT_SUCCESS;
}



/**
 * 更新某个设备，通过DEVEUI
 * @param device
 * @return
 */
int DevicePersistence::updateDevice(DeviceParam &device) {

    int nb = 0;
    try
    {
        SQLite::Statement query(*db,"UPDATE DEVICE SET dev_name=?,"
                                    "dev_secret=?,"
                                    "product_key=?,"
                                    "product_secret=?,"
                                    "alias=?,"
                                    "is_bind=?,"
                                    "is_register=?,"
                                    "is_gateway=?,"
                                    "company_id=?, "
                                    "dev_type=?, "
                                    "f_port=?,"
                                    "app_id=? where dev_eui=?");
        //query.bind(1,ThingDevice.dev_eui);
        query.bind(1,device.device_name);
        query.bind(2,device.device_secret);
        query.bind(3,device.product_key);
        query.bind(4,device.product_secret);
        query.bind(5,device.alias);
        query.bind(6,device.is_bind);
        query.bind(7,device.is_register);
        query.bind(8,device.is_gateway);
        query.bind(9,device.company_id);
        query.bind(10,device.dev_type);
        query.bind(11,device.f_port);
        query.bind(12,device.app_id);
        query.bind(13,device.device_eui);

        nb = query.exec ();
        if (nb != 1){
            LOG(ERROR) << "UPDATE DEVICE FAILED " << query.getErrorMsg() << nb << std::endl;
        } else{
            LOG(INFO) << "UPDATE DEVICE SUCCESSFULLY " << nb << std::endl;
        }
    }
    catch (std::exception& e)
    {
        LOG(ERROR) << "SQLite exception: " << e.what() << std::endl;
        return EXIT_FAILURE; // unexpected error : exit the example program
    }
    return nb;
}


/**
 * 删除某个设备
 * @param dev_eui
 * @return
 */
int DevicePersistence::deleteDevice(string &dev_eui) {

    SQLite::Statement query(*db,"delete from DEVICE where dev_eui=?");
    query.bind(1, dev_eui);
    return query.exec();

}

/**
 * 获取某个设备的信息，通过DevEUI
 * @param dev_eui
 * @param device
 * @return
 */
bool DevicePersistence::selectDevice(string dev_eui, DeviceParam &device) {

    bool isExist = false;
    SQLite::Statement   query(*db, "SELECT * FROM DEVICE where dev_eui=?");
    query.bind(1, dev_eui);
    while (query.executeStep())
    {
        isExist = true;
        LOG(INFO) << "Eui:"<<query.getColumn("dev_eui") <<endl;
        device.device_eui = query.getColumn("dev_eui").getString();
        device.device_name = query.getColumn("dev_name").getString();
        device.device_secret = query.getColumn("dev_secret").getString();
        device.product_key = query.getColumn("product_key").getString();
        device.product_secret = query.getColumn("product_secret").getString();
        device.alias = query.getColumn("alias").getString();


        device.company_id = query.getColumn("company_id").getString();
        device.dev_type = query.getColumn("dev_type");
        device.f_port = query.getColumn("f_port");
        device.app_id = query.getColumn("app_id").getString();
        device.is_bind = query.getColumn("is_bind").getInt() != 0;
        device.is_register = query.getColumn("is_register").getInt() != 0;
        device.is_gateway = query.getColumn("is_gateway").getInt() != 0;

    }

    return isExist;
}


/**
 * 获取网关设备的信息
 * @param device
 * @return
 */
int DevicePersistence::selectGatewayInfo(DeviceParam &device){

    int rc = 0;
    SQLite::Statement   query(*db, "select * from DEVICE where is_gateway=true");
    while (query.executeStep())
    {
        rc += 1;
        device.device_eui = query.getColumn("dev_eui").getString();
        device.device_name = query.getColumn("dev_name").getString();
        device.device_secret = query.getColumn("dev_secret").getString();
        device.product_key = query.getColumn("product_key").getString();
        device.product_secret = query.getColumn("product_secret").getString();
        device.alias = query.getColumn("alias").getString();


        device.company_id = query.getColumn("company_id").getString();
        device.dev_type = query.getColumn("dev_type");
        device.f_port = query.getColumn("f_port");
        device.app_id = query.getColumn("app_id").getString();
        device.is_bind = query.getColumn("is_bind").getInt() != 0;
        device.is_register = query.getColumn("is_register").getInt() != 0;
        device.is_gateway = query.getColumn("is_gateway").getInt() != 0;

    }

    return rc;
}





/**
 * 通过设备的productkey和devName查询设备信息
 * @param product_key
 * @param dev_name
 * @param device
 * @return
 */
int DevicePersistence::getDevWithProductKeyAndName(string &product_key, string dev_name, DeviceParam &device){

    int rc = 0;
    SQLite::Statement   query(*db, "SELECT * FROM DEVICE where product_key=? and dev_name=?");
    query.bind(1, product_key);
    query.bind(2, dev_name);
    while (query.executeStep())
    {
        rc += 1;
        LOG(INFO) << "mGateway Eui:"<<query.getColumn("dev_eui") <<endl;

        device.device_eui = query.getColumn("dev_eui").getString();
        device.device_name = query.getColumn("dev_name").getString();
        device.device_secret = query.getColumn("dev_secret").getString();
        device.product_key = query.getColumn("product_key").getString();
        device.product_secret = query.getColumn("product_secret").getString();
        device.alias = query.getColumn("alias").getString();

        device.company_id = query.getColumn("company_id").getString();
        device.dev_type = query.getColumn("dev_type");
        device.f_port = query.getColumn("f_port");
        device.app_id = query.getColumn("app_id").getString();
        device.is_bind = query.getColumn("is_bind").getInt() != 0;
        device.is_register = query.getColumn("is_register").getInt() != 0;
        device.is_gateway = query.getColumn("is_gateway").getInt() != 0;

    }

    return rc;
}