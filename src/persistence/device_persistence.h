//
// Created by seashell on 5/25/21.
//
#ifndef DEVICE_PERSISTENCE_H
#define DEVICE_PERSISTENCE_H


#include "sqlite/sqlite3.h"
#include <string>
#include "glog/logging.h"

using namespace std;

#include "device.h"


#define DEV_EUI_LEN 20
#define DEV_NAME_LEN 20
#define DEV_SECRET_LEN 20
#define PRODUCT_KEY_LEN 20
#define PRODUCT_SECRET_LEN 20
#define DEV_ALIAS_LEN 20



class DevicePersistence {

public:
    /**
     * 初始化设备
     * @return
     */
    static int initDB();

    /**
     * 添加新设备
     * @param device
     * @return
     */
    static int addDevice(DeviceParam &device);

    /**
     * 更新某个设备，通过DEVEUI
     * @param device
     * @return
     */
    static int updateDevice(DeviceParam &device);

    /**
     * 删除某个设备
     * @param dev_eui
     * @return
     */
    static int deleteDevice(string &dev_eui);

    /**
     * 获取某个设备的信息，通过DevEUI
     * @param dev_eui
     * @param device
     * @return
     */
    static bool selectDevice(string dev_eui, DeviceParam &device);

    /**
     * 获取网关设备的信息
     * @param device
     * @return
     */
    static int selectGatewayInfo(DeviceParam &device);



    /**
     * 通过设备的productkey和devName查询设备信息
     * @param product_key
     * @param dev_name
     * @param device
     * @return
     */
    static int getDevWithProductKeyAndName(string &product_key, string dev_name, DeviceParam &device);

};

#endif //DEVICE_PERSISTENCE_H
