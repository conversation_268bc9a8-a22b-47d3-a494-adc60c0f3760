#include <json/json.h>
#include <fstream>
#include <cerrno>
#include <cstring>
#include <unistd.h>
#include <sys/reboot.h>
#include <iostream>
#include "gateway_config.h"

static Json::Value gConfig;

/**
 * 读取配置文件
 * @param json
 * @return
 */
int GatewayConfig::LoadGlobalConfig()
{
    Json::Reader reader;
    Json::Value root;
    std::ifstream configFile(CONFIG_FILE_NAME);
    int err = 0;
    bool isSuccess = configFile.is_open();
    if (isSuccess == false)
    {
        if (errno == 2)
        {
            ifstream source(CONFIG_DEFAULT_FILE_NAME, ios::binary);
            ofstream dest(CONFIG_FILE_NAME, ios::binary);
            dest << source.rdbuf();
            source.close();
            dest.close();

            configFile.open(CONFIG_FILE_NAME);
            if (!configFile.is_open())
            {
                std::cerr << "could not open file " << CONFIG_FILE_NAME << ":" << strerror(errno) << std::endl;
                return errno;
            }
        }
        else
        {
            std::cerr << "could not open file " << CONFIG_FILE_NAME << ":" << strerror(errno) << std::endl;
            return errno;
        }
    }

    if (reader.parse(configFile, root))
    {
        gConfig = root;
    }
    string dd = jsonTostring(gConfig,true);
    configFile.close();
    return RET_OK;
}

/**
 * 保存配置文件
 * @param json
 * @return
 */
int GatewayConfig::SaveGlobalConfig()
{
    Json::StyledWriter typeWriter;
    std::ofstream wConfigFile(CONFIG_FILE_NAME);
    if (!wConfigFile.is_open())
    {
        std::cerr << "could not open file " << CONFIG_FILE_NAME << ":" << strerror(errno) << std::endl;
        return -1;
    }
    std::string jsonStr = typeWriter.write(gConfig);
    wConfigFile << jsonStr;
    wConfigFile.close();
    return 0;
}


/**
 * 获取uuid
 * @return
 */
string GatewayConfig::GetUUID()
{
    //TODO UUID应该由硬件ID组成，生产阶段就不需要写入UUID
    if (gConfig[IOT_SERIAL_NUMBER].asString() == ""){
        gConfig[MQTT_INFO][MQTT_SER_ADDRESS].asString() = getDiskUuid().substr(0, 16);
        SaveGlobalConfig();
    }
    return gConfig[IOT_SERIAL_NUMBER].asString();
}


/**
 * 获取固件版本
 * @return
 */
string GatewayConfig::GetFirmwareVersion()
{
    return gConfig[IOT_VERSION].asString();
}



/**
 * 恢复出厂设置，重启设备
 * @return
 */
int GatewayConfig::FactoryReset()
{
    unlink(CONFIG_FILE_NAME);
    reboot(RB_AUTOBOOT);
    return RET_OK;
}

/**
 * 获取MQTT服务器地址
 * @return
 */
string GatewayConfig::GetMqttServerAddress()
{
    return gConfig[MQTT_INFO][MQTT_SER_ADDRESS].asString();
}



/**
 * 获取MQTT 服务端口
 * @return
 */
int GatewayConfig::GetMqttPort()
{
    return gConfig[MQTT_INFO][MQTT_SER_PORT].asInt();
}

/**
 * 获取keepalive时间
 * @return
 */
int GatewayConfig::GetMqttKeepalive()
{
    return gConfig[MQTT_INFO][MQTT_SER_KEEPALIVE].asInt();
}


/**
 * 获取HTTP服务器地址
 * @return
 */
string GatewayConfig::GetHttpServerAddress()
{
    return gConfig[HTTP_INFO][HTTP_SER_ADDRESS].asString();
}

/**
 * 获取HTTP服务端口
 * @return
 */
string GatewayConfig::GetHttpServerPort()
{
    return gConfig[HTTP_INFO][HTTP_SER_PORT].asString();
}


/**
 * 获取HTTP服务api的url
 * @return
 */
string GatewayConfig::GetHttpServerUrl()
{
    return gConfig[HTTP_INFO][HTTP_SER_URL].asString();
}

/**
 * 获取SN，网关SN
 * @return
 */
string GatewayConfig::GetSN()
{
    gConfig[IOT_SERIAL_NUMBER] = "SNSSDFDSKFJKDSFJK";
    return gConfig[IOT_SERIAL_NUMBER].asString();
}


/**
 * 根据设备类型获取productKey
 * @param dev_type
 * @return
 */
string GatewayConfig::GetProductKeyWithDevType(string dev_type)
{
    return gConfig[PRODUCT_INFO][dev_type][PRODUCT_KEY].asString();
}


/**
 *
 * @param dev_type
 * @return
 */
int GatewayConfig::GetDevTypeWithProductKey(string productKey)
{
    Json::Value productInfo = gConfig[PRODUCT_INFO];
    Json::Value::Members mem = productInfo.getMemberNames();
    for (auto iter = mem.begin(); iter != mem.end(); iter++)
    {
        string key = *iter;
        if (productInfo[key][PRODUCT_KEY].asString() == productKey)
        {
            return stoi(key);
        }
    }

    return -1;
}


/**
 * 根据设备类型获取productSecret
 * @param dev_type
 * @return
 */
string GatewayConfig::GetProductSecretWithDevType(string dev_type)
{
    return gConfig[PRODUCT_INFO][dev_type][PRODUCT_SECRET].asString();
}
