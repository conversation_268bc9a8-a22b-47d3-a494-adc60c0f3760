/**
 * 网关配置信息
 *
 */


#ifndef GATEWAY_CONFIG_H
#define GATEWAY_CONFIG_H

#include "common_func.h"


/**
 * config
*/
#define CONFIG_FILE_NAME "yciot.json"                       //网关配置文件的名称
#define CONFIG_DEFAULT_FILE_NAME "yciot_default.json"       //网关配置文件的默认名称，恢复出厂设置时使用该配置文件

#define PRODUCT_INFO "productInfo"                          //产品信息
#define PRODUCT_KEY "product_key"                           //产品KEY
#define PRODUCT_SECRET "product_secret"                     //产品秘钥

#define MQTT_INFO "mqtt_info"                               //MQTT信息
#define MQTT_SER_ADDRESS "mqtt_server_address"              //MQTT服务器地址
#define MQTT_SER_PORT "mqtt_server_port"                    //MQTT服务器端口
#define MQTT_SER_KEEPALIVE "mqtt_server_keepalive"          //MQTT服务器心跳

#define HTTP_INFO "http_info"
#define HTTP_SER_ADDRESS "http_server_address"              //MQTT服务器地址
#define HTTP_SER_PORT "http_server_port"                    //MQTT服务器端口
#define HTTP_SER_URL "http_server_url"                      //MQTT服务器心跳

#define IOT_SERIAL_NUMBER "iot_sn"                          //序列号
#define IOT_VERSION "iot_version"                           //版本

#define NS_MQTT_USERNAME    "admin"                         //NS 用户名
#define NS_MQTT_PASSWORD    "admin"                        //NS 秘密


class GatewayConfig {


public:

    /**
     * 获取固件版本
     * @return 固件版本
     */
    static string GetFirmwareVersion();

    /**
     * 恢复出厂设置，重启设备
     * @return 0 success,else failed
     */
    static int FactoryReset();

    /**
     * 获取全局的配置
     * @return
     */
    static int LoadGlobalConfig();

    /**
     * 保存全局的配置
     * @return
     */
    static int SaveGlobalConfig();


    /**
     * 根据设备类型获取productKey
     * @param dev_type  设备类型
     * @return
     */
    static string GetProductKeyWithDevType(string dev_type);



    /**
     * 根据productKey获取设备类型
     * @param productKey  产品key
     * @return
     */
    static int GetDevTypeWithProductKey(string productKey);

    /**
     * 根据设备类型获取productSecret
     * @param dev_type
     * @return
     */
    static string GetProductSecretWithDevType(string dev_type);


    /**
     * 获取uuid
     * @return
     */
    static string GetUUID();


    /**
     * 获取MQTT服务器地址
     * @return address
     */
    static string GetMqttServerAddress();


    /**
     * 获取HTTP服务器地址
     * @return
     */
    static string GetHttpServerAddress();

    /**
     * 获取HTTP服务端口
     * @return
     */
    static string GetHttpServerPort();


    /**
     * 获取HTTP服务api的url
     * @return
     */
    static string GetHttpServerUrl();

    /**
     * 获取MQTT 服务端口
     * @return
     */
    static int GetMqttPort();

    /**
     * 获取keepalive时间
     * @return
     */
    static int GetMqttKeepalive();

    /**
     * 获取设备SN
     * @return
     */
    static string GetSN();
};

#endif //GATEWAY_CONFIG_H
