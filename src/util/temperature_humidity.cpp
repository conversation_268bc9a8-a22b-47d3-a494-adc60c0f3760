//
// Created by seashell on 9/23/21.
//

#include "temperature_humidity.h"

#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <sys/stat.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h>
#include <string.h>
#include <stdint.h>
#include <time.h>
#include <errno.h>
#include <string.h>
#define SOFTRESET                        0xFE
#define TRIGGER_TEMPERATURE_NO_HOLD      0xF3   //监测温度
#define TRIGGER_HUMIDITY_NO_HOLD         0xF5   //监测湿度
//#define I2C_API_IOCTL  /* Use I2C userspace driver ioctl API */
#define I2C_API_RDWR /* Use I2C userspace driver read/write API */

static inline void mSleep(unsigned long ms);
static inline void dumpBuf(const char *prompt, uint8_t *buf, int size);
int sht2XInit(void);
int sht2XSoftReset(int fd);
//int sht2x_get_serialnumber(int fd, uint8_t *serialnumber, int size);
int sht2XGetTempHumidity(int fd, float *temp, float *rh);

int GetBoardTemperatureHumidity(float &temperature, float &humidity)
{
    int          fd;
    fd = sht2XInit();

    if(fd < 0)
    {
        printf("SHT2x initialize failure\n");
        close(fd);
        return 1;
    }
    if(sht2XSoftReset(fd) < 0 )
    {
        printf("SHT2x softreset failure\n");
        close(fd);
        return 2;
    }

    if(sht2XGetTempHumidity(fd, &temperature, &humidity) < 0 )
    {
        printf("SHT2x get get temperature and relative humidity failure\n");
        close(fd);
        return 3;
    }

    //printf("Temperature=%lf ℃ relative humidity=%lf \n", temperature, humidity);

    close(fd);
    return 0;
}


static inline void mSleep(unsigned long ms)
{
    struct timespec cSleep;
    unsigned long ulTmp;
    cSleep.tv_sec = ms / 1000;

    if (cSleep.tv_sec == 0)
    {
        ulTmp = ms * 10000;
        cSleep.tv_nsec = ulTmp * 100;
    }
    else
    {
        cSleep.tv_nsec = 0;
    }
    nanosleep(&cSleep, 0);
}

static inline void dumpBuf(const char *prompt, uint8_t *buf, int size)
{
    int          i;
    if( !buf )
    {
        return ;
    }
    if( prompt )
    {
        printf("%s ", prompt);
    }
    for(i=0; i<size; i++)
    {
        printf("%02x ", buf[i]);
    }
    printf("\n");
    return ;
}

int sht2XInit(void)
{
    int     fd;
    if( (fd=open("/dev/i2c-0", O_RDWR)) < 0)
    {
        printf("i2c device open failed: %s\n", strerror(errno));
        return -1;
    }
    /* set I2C mode and SHT2x slave address */
    ioctl(fd, I2C_TENBIT, 0);    /* Not 10-bit but 7-bit mode */
    ioctl(fd, I2C_SLAVE, 0x40); /* 我的sht20设备地址 */
    return fd;
}

/* 初始化设备 */
int sht2XSoftReset(int fd)
{
    uint8_t           buf[4];
    if( fd<0 )
    {
        printf("%s line [%d] %s() get invalid input arguments\n", __FILE__, __LINE__, __func__ );
        return -1;
    }
    /* software reset SHT2x */
    memset(buf, 0, sizeof(buf));
    buf[0] = SOFTRESET;
    write(fd, buf, 1);
    mSleep(50);

    return 0;
}

int sht2XGetTempHumidity(int fd, float *temp, float *rh)
{
    uint8_t           buf[4];
    if( fd<0 || !temp || !rh )
    {
        printf("%s line [%d] %s() get invalid input arguments\n", __FILE__, __LINE__, __func__ );
        return -1;
    }
    /* send trigger temperature measure command and read the data */
    memset(buf, 0, sizeof(buf));
    buf[0]=TRIGGER_TEMPERATURE_NO_HOLD;
    write(fd, buf, 1);  //发送监测温度命令

    mSleep(85); /* datasheet: typ=66, max=85 */
    memset(buf, 0, sizeof(buf));
    read(fd, buf, 3);  //读取温度
    //dumpBuf("Temperature sample data: ", buf, 3);
    *temp = 175.72 * (((((int) buf[0]) << 8) + buf[1]) / 65536.0) - 46.85;

    /* send trigger humidity measure command and read the data */
    memset(buf, 0, sizeof(buf));
    buf[0] = TRIGGER_HUMIDITY_NO_HOLD;
    write(fd, buf, 1);  //发送湿度监测命令
    mSleep(29); /* datasheet: typ=22, max=29 */
    memset(buf, 0, sizeof(buf));
    read(fd, buf, 3);
    //dumpBuf("Relative humidity sample data: ", buf, 3);
    *rh = 125 * (((((int) buf[0]) << 8) + buf[1]) / 65536.0) - 6;

    return 0;
}
