#include <vector>
#include <string>
#include <cstdint>
#include <stdexcept>
#include <map>
#include <sstream>
#include <iomanip>

class TlvParser {
public:
    // TLV 结构体
    struct Tlv {
        uint32_t tag;
        uint32_t length;
        std::vector<uint8_t> value;
        std::vector<Tlv> children; // 用于嵌套的 TLV 结构
    };

    // 解析 TLV 数据
    static std::vector<Tlv> parse(const std::vector<uint8_t>& data) {
        std::vector<Tlv> result;
        size_t pos = 0;
        
        while (pos < data.size()) {
            Tlv tlv;
            
            // 解析 Tag
            pos = parseTag(data, pos, tlv.tag);
            
            // 解析 Length
            pos = parseLength(data, pos, tlv.length);
            
            // 检查长度是否有效
            if (pos + tlv.length > data.size()) {
                throw std::runtime_error("Invalid TLV data: length exceeds available data");
            }
            
            // 提取 Value
            tlv.value.assign(data.begin() + pos, data.begin() + pos + tlv.length);
            
            // 尝试解析嵌套的 TLV (如果 value 看起来像 TLV 数据)
            if (tlv.length > 2 && isPotentialTlv(tlv.value)) {
                try {
                    tlv.children = parse(tlv.value);
                } catch (...) {
                    // 如果解析失败，保持原始 value
                }
            }
            
            result.push_back(tlv);
            pos += tlv.length;
        }
        
        return result;
    }

    // 构建 TLV 数据
    static std::vector<uint8_t> build(const Tlv& tlv) {
        std::vector<uint8_t> result;
        
        // 添加 Tag
        encodeTag(tlv.tag, result);
        
        // 添加 Length
        encodeLength(tlv.length, result);
        
        // 添加 Value
        result.insert(result.end(), tlv.value.begin(), tlv.value.end());
        
        return result;
    }

    // 构建嵌套 TLV 数据
    static std::vector<uint8_t> build(const std::vector<Tlv>& tlvs) {
        std::vector<uint8_t> result;
        
        for (const auto& tlv : tlvs) {
            auto tlvData = build(tlv);
            result.insert(result.end(), tlvData.begin(), tlvData.end());
        }
        
        return result;
    }

    // 将 TLV 结构转换为可读字符串
    static std::string toString(const Tlv& tlv, int indent = 0) {
        std::ostringstream oss;
        std::string indentStr(indent, ' ');
        
        oss << indentStr << "Tag: 0x" << std::hex << std::setw(2) << std::setfill('0') << tlv.tag
            << ", Length: " << std::dec << tlv.length;
        
        if (!tlv.children.empty()) {
            oss << " (Nested TLVs):\n";
            for (const auto& child : tlv.children) {
                oss << toString(child, indent + 2);
            }
        } else {
            oss << ", Value: ";
            if (tlv.length <= 8) {
                // 短值直接显示
                for (auto byte : tlv.value) {
                    oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte) << " ";
                }
            } else {
                // 长值显示前4字节和最后4字节
                for (size_t i = 0; i < 4 && i < tlv.value.size(); ++i) {
                    oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(tlv.value[i]) << " ";
                }
                if (tlv.value.size() > 8) {
                    oss << "... ";
                    for (size_t i = tlv.value.size() - 4; i < tlv.value.size(); ++i) {
                        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(tlv.value[i]) << " ";
                    }
                }
            }
            oss << "\n";
        }
        
        return oss.str();
    }

private:
    // 解析 Tag (支持多字节 Tag)
    static size_t parseTag(const std::vector<uint8_t>& data, size_t pos, uint32_t& tag) {
        if (pos >= data.size()) {
            throw std::runtime_error("Invalid TLV data: missing tag");
        }
        
        tag = data[pos++];
        
        // 检查是否是多字节 Tag (bit 5-1 都为1)
        if ((tag & 0x1F) == 0x1F) {
            while (pos < data.size()) {
                uint8_t nextByte = data[pos++];
                tag = (tag << 8) | nextByte;
                
                // 检查是否还有后续字节 (bit 8 为1表示还有)
                if ((nextByte & 0x80) == 0) {
                    break;
                }
            }
        }
        
        return pos;
    }

    // 解析 Length (支持多字节 Length)
    static size_t parseLength(const std::vector<uint8_t>& data, size_t pos, uint32_t& length) {
        if (pos >= data.size()) {
            throw std::runtime_error("Invalid TLV data: missing length");
        }
        
        uint8_t firstByte = data[pos++];
        
        if (firstByte < 0x80) {
            // 短格式
            length = firstByte;
        } else {
            // 长格式
            uint8_t lengthOfLength = firstByte & 0x7F;
            length = 0;
            
            if (pos + lengthOfLength > data.size()) {
                throw std::runtime_error("Invalid TLV data: incomplete length field");
            }
            
            for (uint8_t i = 0; i < lengthOfLength; ++i) {
                length = (length << 8) | data[pos++];
            }
        }
        
        return pos;
    }

    // 编码 Tag
    static void encodeTag(uint32_t tag, std::vector<uint8_t>& output) {
        if (tag <= 0xFF) {
            output.push_back(static_cast<uint8_t>(tag));
        } else {
            // 多字节 Tag 编码
            std::vector<uint8_t> bytes;
            while (tag > 0) {
                bytes.insert(bytes.begin(), static_cast<uint8_t>(tag & 0xFF));
                tag >>= 8;
            }
            
            // 设置多字节标志
            bytes[0] |= 0x1F;
            for (size_t i = 0; i < bytes.size() - 1; ++i) {
                bytes[i] |= 0x80;
            }
            
            output.insert(output.end(), bytes.begin(), bytes.end());
        }
    }

    // 编码 Length
    static void encodeLength(uint32_t length, std::vector<uint8_t>& output) {
        if (length < 0x80) {
            // 短格式
            output.push_back(static_cast<uint8_t>(length));
        } else {
            // 长格式
            std::vector<uint8_t> bytes;
            while (length > 0) {
                bytes.insert(bytes.begin(), static_cast<uint8_t>(length & 0xFF));
                length >>= 8;
            }
            
            output.push_back(static_cast<uint8_t>(0x80 | bytes.size()));
            output.insert(output.end(), bytes.begin(), bytes.end());
        }
    }

    // 检查数据是否可能是 TLV
    static bool isPotentialTlv(const std::vector<uint8_t>& data) {
        if (data.size() < 2) return false;
        
        try {
            size_t pos = 0;
            uint32_t tag, length;
            
            // 尝试解析 Tag
            pos = parseTag(data, pos, tag);
            
            // 尝试解析 Length
            pos = parseLength(data, pos, length);
            
            // 检查是否有足够的空间存放 Value
            return (pos + length <= data.size());
        } catch (...) {
            return false;
        }
    }
};