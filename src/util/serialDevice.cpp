#include <termios.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <linux/kernel.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <sys/select.h>
#include <sys/time.h>
#include <iostream>
#include "common_func.h"
#include "serialDevice.h"

serialDevice::serialDevice()
{
    this->mFd = -1;
}

serialDevice::~serialDevice()
{
    CloseSerial();
}

//-------------------------------------------------------------------------------------------------------------------------
// 串口参数配置接口
// nfd: 设备ID
// baudrate: 波特率(2400/4800/9600/19200/38400/115200)
// databits: 数据位(5/6/7/8)
// parity: 奇偶校验位(n/N:无, o/O:奇校验, e/E:偶校验)
// stopbits: 停止位(1/2)
// flowcontrol: 流控(0:无 1: 硬 2: 软)
//-------------------------------------------------------------------------------------------------------------------------
int serialDevice::ConfigSerial(int baudrate, int databits, char parity, int stopbits, int flowcontrol)
{
    struct termios options;

    int speed;
    if (tcgetattr(mFd, &options) != 0)
    {
        perror("tcgetttr");
        return -1;
    }

    options.c_cflag &= ~CSIZE;

    // 设置波特率
    switch (baudrate)
    {
    case 2400:
        cfsetispeed(&options, B2400);
        cfsetospeed(&options, B2400);
        break;

    case 4800:
        cfsetispeed(&options, B4800);
        cfsetospeed(&options, B4800);
        break;

    case 9600:
        cfsetispeed(&options, B9600);
        cfsetospeed(&options, B9600);
        break;

    case 19200:
        cfsetispeed(&options, B19200);
        cfsetospeed(&options, B19200);
        break;

    case 38400:
        cfsetispeed(&options, B38400);
        cfsetospeed(&options, B38400);
        break;
    case 115200:
        cfsetispeed(&options, B115200);
        cfsetospeed(&options, B115200);
        break;
    default:
        cfsetispeed(&options, B9600);
        cfsetospeed(&options, B9600);
        break;
    }

    if (tcsetattr(mFd, TCSANOW, &options) != 0)
    {
        perror("set baudrate error");
        return -1;
    }

    tcflush(mFd, TCIOFLUSH);

    if (tcgetattr(mFd, &options) < 0)
    {
        perror("tcgetattr error");
        return -1;
    }

    // 设置控制模式
    options.c_cflag |= CLOCAL;
    options.c_cflag |= CREAD;

    // 流控
    switch (flowcontrol)
    {
    // 无
    case 0:
        options.c_cflag &= ~CRTSCTS;
        break;
    // 硬件
    case 1:
        options.c_cflag |= CRTSCTS;
        break;

    // 软件
    case 2:
        options.c_cflag |= IXON | IXOFF | IXANY;
        break;
    }

    // 设置数据长度
    switch (databits)
    {
    case 5:
        options.c_cflag &= ~CSIZE;
        options.c_cflag |= CS5;
        break;

    case 6:
        options.c_cflag &= ~CSIZE;
        options.c_cflag |= CS6;
        break;

    case 7:
        options.c_cflag &= ~CSIZE;
        options.c_cflag |= CS7;
        break;

    case 8:
        options.c_cflag &= ~CSIZE;
        options.c_cflag |= CS8;
        break;
    default:
        break;
    }

    // 设置奇偶校验位
    switch (parity)
    {
    case 'n':
    case 'N': //无校验
    {
        options.c_cflag &= ~PARENB;
        options.c_iflag &= ~INPCK;
    }
    break;

    case 'o': //奇校验
    case 'O':
    {
        options.c_cflag |= (PARODD | PARENB);
        options.c_iflag |= INPCK;
    }
    break;

    case 'e': //偶校验
    case 'E':
    {
        options.c_cflag |= PARENB;
        options.c_cflag &= ~PARODD;
        options.c_iflag |= INPCK;
    }
    break;

    default:
        break;
    }

    // 设置停止位
    switch (stopbits)
    {
    case 1:
        options.c_cflag &= ~CSTOPB;
        break;

    case 2:
        options.c_cflag |= CSTOPB;
        break;

    default:
        break;
    }

    // Set Raw Mode
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    options.c_oflag &= ~OPOST; //Output

    options.c_cc[VTIME] = 0;
    options.c_cc[VMIN] = 1;

    // 处理未接收字符,溢出数据可以接收，但不读
    tcflush(mFd, TCIFLUSH);

    // 激活配置,将修改后的termios数据设置到串口中, TCSANOW:所有改变立即生效
    if ((tcsetattr(mFd, TCSANOW, &options)) != 0)
    {
        printf("set config error\n");
        return -1;
    }
    return 0;
}

//-------------------------------------------------------------------------------------------------------------------------
// 发送串口数据
// nfd: 设备ID
// pdata: 发送数据缓存
// data_len: 所需发送数据长度
//-------------------------------------------------------------------------------------------------------------------------
int serialDevice::SendMessage(unsigned char *pdata, int data_len)
{
    int len = 0;

    if (!IsOpen())
    {
        return -1;
    }

    len = write(mFd, pdata, data_len);

    if (len == data_len)
    {
        return len;
    }
    else
    {
        tcflush(mFd, TCOFLUSH); //TCOFLUSH刷新写入的数据但不传送
        return -1;
    }
}

//-------------------------------------------------------------------------------------------------------------------------
// 读取串口数据
// nfd: 设备ID
// pdata: 读取数据缓存
// data_len: 所读取数据长度，通常为pbuf缓存长度
//-------------------------------------------------------------------------------------------------------------------------
int serialDevice::RecvMessage(unsigned char *pdata, int data_len)
{
    int readcnt, timecnt = 0;

    if (!IsOpen())
    {
        return -1;
    }

    while (1)
    {
        readcnt = read(mFd, pdata, data_len);
        if (readcnt != -1)
        {
            break;
        }

        if (timecnt++ > 300)
        {
            std::cout << "recv timeout";
            return -1;
        }

        usleep(1000 * 10);
    }
    printHex("recv <--", pdata, readcnt);
    return readcnt;
}

//-------------------------------------------------------------------------------------------------------------------------
// 打开串口
// pDEv: 设备名称(eg. /dev/ttyAMA1)
//-------------------------------------------------------------------------------------------------------------------------
bool serialDevice::OpenSerial(const char *pDev, int baudrate, int databits, char parity, int stopbits, int flowcontrol)
{
    int nRet = -1;

    if (this->IsOpen())
    {
        return false;
    }

    this->mFd = open(pDev, O_RDWR | O_NOCTTY | O_NDELAY);
    if (mFd < 0)
    {
        std::cout << "serial open failed";
        return false;
    }
    std::cout << "serial open succeed ";
    // 串口参数配置
    nRet = ConfigSerial(baudrate, databits, parity, stopbits, flowcontrol);

    if (nRet == -1)
    {
        std::cout << "serial config failed";
        return false;
    }

    // 8bit数据, 发送和接收FIFO使能
    system("himm 0x1210102c 0x70");
    // UART 接收/发送使能
    system("himm 0x12101030 0x00000f01");

    return true;
}

//-------------------------------------------------------------------------------------------------------------------------
// 关闭串口
// nfd: 设备ID
//-------------------------------------------------------------------------------------------------------------------------
void serialDevice::CloseSerial()
{
    if (this->mFd > 0)
    {
        close(this->mFd);
        this->mFd = -1;
    }
}

//-------------------------------------------------------------------------------------------------------------------------
// 返回串口打开状态
// false: 已关闭
// true: 已打开
//-------------------------------------------------------------------------------------------------------------------------
bool serialDevice::IsOpen()
{
    if (this->mFd > 0)
    {
        return true;
    }
    return false;
}

//-------------------------------------------------------------------------------------------------------------------------
// 串口测试接口
//
//-------------------------------------------------------------------------------------------------------------------------
int serialTest()
{
#if 0
    int nRet, s_count, r_count;
    int timecount = 0;
    u8 pSend[32];
    u8 pRecv[32];
    u8 nIndex = 0;
    pthread_t pserial_recv_id;
    serialDevice *pserialDevice = new serialDevice();

    if (!pserialDevice->SerialOpen("/dev/ttyAMA1", 9600, 8, 'N', 1, 0))
    {        
        return -1;
    }

    // 查询播放状态 AA 01 00 AB
    // 查询文件夹目录曲目 AA 11 00 BB
    memset(pSend, 0, sizeof(pSend));
    pSend[nIndex++] = 0xAA;
    pSend[nIndex++] = 0x11;
    pSend[nIndex++] = 0x00;
    pSend[nIndex++] = 0xBB;
    
    s_count = pserialDevice->SerialSendMessage(pSend, nIndex);

    r_count = pserialDevice->SerialRecvMessage(pRecv, sizeof(pRecv));

    if (pserialDevice)
    {
        delete pserialDevice;
    }
    
    printf("serial fininshed\n");

#endif
}