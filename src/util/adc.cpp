//
// Created by seashell on 9/23/21.
//

#include "adc.h"
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <sys/stat.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h>
#include <string.h>
#include <stdint.h>
#include <time.h>
#include <errno.h>
#include <string.h>
#define SOFTRESET                        0xFE
#define TRIGGER_TEMPERATURE_NO_HOLD      0xF3   //监测温度
#define TRIGGER_HUMIDITY_NO_HOLD         0xF5   //监测湿度
//#define I2C_API_IOCTL  /* Use I2C userspace driver ioctl API */
#define I2C_API_RDWR /* Use I2C userspace driver read/write API */

#define AN1_VOLTAGE 1
#define AN2_VOLTAGE 2

float get_an2_voltage(int fd);
float get_an1_voltage(int fd);
float get_ad_voltage(int fd,int anx) ;
static inline void msleep(unsigned long ms);
int sgm_init(void);

#if 0
int main(int argc, char **argv)
{
    int          fd;
    float        an1_voltage;
    float        an2_voltage;


    fd = sgm_init();

    if(fd < 0)
    {
        printf("SHT2x initialize failure\n");
        return 1;
    }

    an1_voltage = get_an1_voltage(fd);
    an2_voltage = get_an2_voltage(fd);
    printf("AN1 VOLTAGE=%lf V AN2 VOLTAGE=%lf V\n", an1_voltage, an2_voltage);

    close(fd);
}
#endif

static inline void msleep(unsigned long ms)
{
    struct timespec cSleep;
    unsigned long ulTmp;
    cSleep.tv_sec = ms / 1000;

    if (cSleep.tv_sec == 0)
    {
        ulTmp = ms * 10000;
        cSleep.tv_nsec = ulTmp * 100;
    }
    else
    {
        cSleep.tv_nsec = 0;
    }
    nanosleep(&cSleep, 0);
}



int sgm_init(void)
{
    int     fd;
    if( (fd=open("/dev/i2c-0", O_RDWR)) < 0)
    {
        printf("i2c device open failed: %s\n", strerror(errno));
        return -1;
    }
    else{
        //printf("open /dev/i2c-0 success\n");
    }
    /* set I2C mode and SHT2x slave address */
    ioctl(fd, I2C_TENBIT, 0);    /* Not 10-bit but 7-bit mode */
    ioctl(fd, I2C_RETRIES, 5);
    ioctl(fd, I2C_SLAVE, 0x48); /* 我的sht20设备地址 */
    return fd;
}


float get_ad_voltage(int fd,int anx)
{
    uint8_t	buf[10] = {0};
    if( fd<0 )
    {
        printf("%s line [%d] %s() get invalid input arguments\n", __FILE__, __LINE__, __func__ );
        return -1;
    }

    /*
    config register address
    */
    buf[0]=0x01;//config register address

    if(anx == AN1_VOLTAGE){
        /*
        config register High字节数据
        bit[15] OS=1 启动转换; bit[14:12] MUX[2:0]=000
        AN1,101: AINP = AIN1 and AINN = GND
        Bits [11:9] PGA[2:0]=010，输入范围±2.048V；bit[8] MODE =1，单次转换）
        */
        buf[1]=0xd5;//48v an1
    }else{
        /*
        config register Low字节数据
        bit[15] OS=1 启动转换; bit[14:12] MUX[2:0]=000
        110: AINP = AIN2 and AINN = GND
        Bits [11:9] PGA[2:0]=010，输入范围±2.048V；bit[8] MODE =1，单次转换）
        */
        buf[1]=0xe5;//12V an2
    }
    /*
    bit[7:5]DR[2:0]=111,数据率 800Hz；bit[4]Comparator mode=0 默认滞回模式；bit[3]COMP_POL=0 低有效；bit[2]COMP_LAT=0 不锁存；
    bit[1:0]COMP_QUE=11 关闭比较器
    */
    buf[2]=0x83;//100Mhz

    int r = write(fd, buf, 3);  //发送监测温度命令
    if(r == -1){
        perror("error:");
    }

    //conversion register
    buf[0]=0x00;
    r = write(fd, buf, 1);
    if(r == -1){
        perror("error:");
    }

    //mSleep(100);
    //read register value
    memset(buf, 0, sizeof(buf));
    r = read(fd, buf, 2);
    if(r == -1){
        perror("error:");
    }

    unsigned short regValue = (buf[0] << 8) + buf[1];
    float voltage = (float)regValue/32768 * 2.045 *12 + 0.15;
    return voltage;
}



float getPoeVoltage()
{
    int          fd;
    float        an1_voltage;
    fd = sgm_init();

    if(fd < 0)
    {
        printf("SHT2x initialize failure\n");
        close(fd);
        return 1;
    }

    //get voltage of an1 48V
    an1_voltage = get_ad_voltage(fd,AN1_VOLTAGE);

    //close fd
    close(fd);

    return an1_voltage;
}


float getDcVoltage()
{
    int          fd;
    float        an2_voltage;
    fd = sgm_init();

    if(fd < 0)
    {
        printf("SHT2x initialize failure\n");
        close(fd);
        return 1;
    }

    //get voltage of an1 48V
    an2_voltage = get_ad_voltage(fd,AN2_VOLTAGE);

    //close fd
    close(fd);

    return an2_voltage;
}

