/*
 * @Description: 
 * @Author: 
 * @Create Date: 
 * @Version: 1.0
 * @LastEditTime: 2020-05-27 11:15:48
 * @LastEditors: Please set LastEditors
 */
#include "common_func.h"
#include <dirent.h>
#include <unistd.h>
#include "string.h"
#include <stdarg.h>
#include <sys/time.h>
#include <math.h>
#include <algorithm>
#include <sstream>
#include <fstream>
#include <thing_tool.h>
#include "openssl/pem.h"
#include "openssl/bio.h"
#include "openssl/evp.h"
#include "openssl/rand.h"
#include "openssl/md5.h"


/**
 * system call,return cmd exec result,
 * @param cmd  if cmd ==0 is sucess else fail
 * @param result cmd output or error message
 * @return
 */
int systemX(string &cmd, string &result,bool isOut)
{
    int rc = 0;
    int ret = -1;
    FILE *fp = NULL;

    cmd = cmd + " 2>&1";

    do {
        fp = popen(cmd.c_str(), "r");
        if (NULL == fp) {
            perror("popen error\n");
            break;
        }

        if (isOut){
            char tmp[1024]; //设置一个合适的长度，以存储每一行输出
            while (fgets(tmp, 1024, fp) != NULL) {
                //if (tmp[strlen(tmp) - 1] == '\n') {
                //    tmp[strlen(tmp) - 1] = '\0'; //去除换行符
                //}
                result += tmp;
            }
        }

        rc = pclose(fp);
        if (-1 == rc) {
            perror("pclose error\n");
            break;
        }
        //printf("子进程结束状态 = %d\n", rc);
        if (!WIFEXITED(rc)) {
            perror("work command failed\n");
            break;
        } else {
            ret = WEXITSTATUS(rc);
        }
    } while(0);

    if (NULL == fp || -1 == rc) {
        //strncpy(result, strerror(errno), BUF_SIZE);
        result = strerror(errno);
    }
    fp = NULL;

    if (ret != 0){
        std::cout << strerror(errno) <<endl;
    }

    return ret;
}

/**
 * 执行一个shell命令，输出结果逐行存储在result中，并返回行数
 * @param cmd
 * @param result
 * @return
 */
int32_t executeSysCmd(const char *cmd, vector<string> &result) {
    result.clear();
    FILE *pp = popen(cmd, "r"); //建立管道
    if (!pp) {
        return -1;
    }
    char tmp[1024]; //设置一个合适的长度，以存储每一行输出
    while (fgets(tmp, sizeof(tmp), pp) != NULL) {
        if (tmp[strlen(tmp) - 1] == '\n') {
            tmp[strlen(tmp) - 1] = '\0'; //去除换行符
        }
        result.push_back(tmp);
    }
    pclose(pp); //关闭管道
    return result.size();
}


void ledInit(void)
{

}


/**
 * 域名转IP
 * @param domain
 * @param out
 * @return
 */
int domainToIP(const char *domain, string &out)
{
    if (domain == NULL)
    {
        return -3;
    }

    //判断是否本身为ip,为ip直接返回
    int a, b, c, d;
    int ret = 0;
    ret = sscanf(domain, "%d.%d.%d.%d", &a, &b, &c, &d);
    if (ret == 4 && (a >= 0 && a <= 255) && (b >= 0 && b <= 255) && (c >= 0 && c <= 255) && (d >= 0 && d <= 255))
    {
        out = domain;
        return 0;
    }
    CURLcode res;
    CURL *curl;
    char *ipstr = NULL;
    if ((curl = curl_easy_init()) == NULL)
    {
        curl_global_cleanup();
        return -1;
    }
    if ((curl = curl_easy_init()) == NULL)
    {
        curl_global_cleanup();
        return -2;
    }
    curl_easy_setopt(curl, CURLOPT_DNS_SERVERS, "*********");
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    curl_easy_setopt(curl, CURLOPT_URL, domain);

    res = curl_easy_perform(curl);
    if (res != CURLE_OK)
    {
        curl_easy_cleanup(curl);
        curl_global_cleanup();
        return res;
    }

    res = curl_easy_getinfo(curl, CURLINFO_PRIMARY_IP, &ipstr);
    if (res != CURLE_OK)
    {
        curl_easy_cleanup(curl);
        curl_global_cleanup();
        return res;
    }
    out = ipstr;

    curl_easy_cleanup(curl);
    curl_global_cleanup();

    return CURLE_OK;
}



/**
 * 更新时间
 * @return
 */
int updateTime(void)
{
    int currentTime = stoi(GetTimeStamp());
    if (currentTime > 1573738696 && (currentTime % 4) != 0)
    {
        return RET_OK;
    }

    char timeSource[][100] = {
            "ntpclient -s -d -c 1 -i 5 -h time.pool.aliyun.com | grep Source",
            "ntpclient -s -d -c 1 -i 5 -h cn.pool.ntp.org | grep Source",
            "ntpclient -s -d -c 1 -i 5 -h time.apple.com | grep Source",
            "ntpclient -s -d -c 1 -i 5 -h pool.ntp.org | grep Source",
            "ntpclient -s -d -c 1 -i 5 -h time.nist.gov | grep Source"};
    for (int i = 0; i < 10; ++i)
    {

        int index = i % 4;
        const char *insbuf = timeSource[index]; //"ntpclient -s -d -c 1 -i 5 -h pool.ntp.org | grep Source";
        int len;
        FILE *stream;
        u8 buf[1024];
        memset(buf, 0, sizeof(buf));

        stream = popen(insbuf, "r");
        if (stream == NULL)
        {
            pclose(stream);
            return RET_FAIL;
        }

        len = fread(buf, sizeof(char), sizeof(buf), stream);
        if (len <= 0)
        {
            pclose(stream);
            return RET_FAIL;
        }

        if (strstr((const char *)buf, "Source") != NULL)
        {
            system("hwclock -w -u");
            break;
        }
        else
        {
            continue;
        }
    }
    return RET_OK;
}


/**
 * 获取主机hostip
 * @param domain
 * @param out
 * @param isWriteToHosts
 * @return
 */
int getHostWithDomain(string domain, string &out, bool isWriteToHosts)
{
    string command = "ping -q -c 1 ";
    command += domain;
    command += "| grep PING | sed -e \"s/).*//\" | sed  -e \"s/.*(//\"";
    //"ping -q -c 1 ivms.fj-yuchen.com | grep PING | sed -e \"s/).*//\" | sed  -e \"s/.*(//\""
    const char *insbuf = command.c_str();

    int len;
    FILE *stream;
    u8 buf[1024];
    memset(buf, 0, sizeof(buf));

    stream = popen(insbuf, "r");
    if (stream == NULL)
    {
        pclose(stream);
        return RET_FAIL;
    }

    len = fread(buf, sizeof(char), sizeof(buf), stream);
    if (len <= 0)
    {
        pclose(stream);
        return RET_FAIL;
    }

    string result((const char *)buf, len);
    if (result.find("unknown") == std::string::npos)
    {
        out = result;
        if (isWriteToHosts)
        {
        }
        return RET_OK;
    }
    else
    {
        out = result;
        return RET_FAIL;
    }
}


/*
void getCPUID()
{

    std::ifstream cpu_file("/proc/cpuinfo");
    int err = 0;
    bool isSuccess = cpu_file.is_open();
    if (isSuccess == false){
        LOG(ERROR) << "get cpuinfo error";
    } else{
        cpu_file.get
    }



}*/


/**
 * 执行系统命令
 * @param cmd
 * @return
 */
string executeSysCmd(string cmd)
{
    FILE *fp;
    string result;
    char buf[256];
    LOG(INFO) << cmd;
    fp = popen(cmd.c_str(), "r");
    fgets(buf, sizeof(buf), fp);
    result = buf;
    pclose(fp);
    return result;
}

/**
 * 获取disk——uuid
 * @return
 */
string getDiskUuid(){
    return executeSysCmd("dbus-uuidgen");
}


//-------------------------------------------------------------------------------------------------------------------------
// 打印数据流-Hex
// 参数：
// 返回值: None
//-------------------------------------------------------------------------------------------------------------------------
void printHex(const char *strp, u8 *pData, u32 nLen)
{
    u32 i;
    printf("%s", strp);
    for (i = 0; i < nLen; i++)
    {
        if (0 == (i % 32))
            printf("\n");
        printf("%02x ", pData[i]);
    }
    printf("\n");
}

//-------------------------------------------------------------------------------------------------------------------------
// 计算异或
// 参数：
// 返回值: None
//-------------------------------------------------------------------------------------------------------------------------
u8 calXor(u8 *pData, u32 nCount)
{
    u32 i;
    u8 nXor = 0;
    for (i = 0; i < nCount; i++)
    {
        nXor ^= pData[i];
    }
    return nXor;
}

//-------------------------------------------------------------------------------------------------------------------------
// 计算checksum
// 参数：
// 返回值: None
//-------------------------------------------------------------------------------------------------------------------------
u8 calChecksum(u8 *pData, u32 nCount)
{
    u32 i;
    u8 nSum = 0;
    for (i = 0; i < nCount; i++)
    {
        nSum += pData[i];
    }
    return nSum;
}

//-------------------------------------------------------------------------------------------------------------------------
// 字符串分割
// 参数：
// 返回值: None
//-------------------------------------------------------------------------------------------------------------------------
u8 splitString(const std::string &s, std::vector<std::string> &v, const std::string &c)
{
    std::string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while (std::string::npos != pos2)
    {
        v.push_back(s.substr(pos1, pos2 - pos1));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if (pos1 != s.length())
    {
        v.push_back(s.substr(pos1));
    }
    return v.size();
}

//-------------------------------------------------------------------------------------------------------------------------
// string转16进制数组
// 参数：
// 返回值:
//-------------------------------------------------------------------------------------------------------------------------
void stringToHexArray(string s, u8 *phex, u32 &length)
{
    int i, sz, iRet = 0;
    u8 ucTmp;

    //先把字符串转为大写
    transform(s.begin(), s.end(), s.begin(), ::toupper);
    sz = s.size();
    sz = sz / 2;
    for (i = 0; i < sz; i++)
    {
        ucTmp = s.at(2 * i);
        if (ucTmp >= 0x30 && ucTmp <= 0x39)
        {
            ucTmp -= 0x30;
        }
        else if (ucTmp >= 0x41 && ucTmp <= 0x46)
        {
            ucTmp -= 0x37;
        }
        *(phex + i) = ucTmp << 4;

        ucTmp = s.at(2 * i + 1);
        if (ucTmp >= 0x30 && ucTmp <= 0x39)
        {
            ucTmp -= 0x30;
        }
        else if (ucTmp >= 0x41 && ucTmp <= 0x46)
        {
            ucTmp -= 0x37;
        }
        *(phex + i) += ucTmp;
    }

    length = i;
}

//-------------------------------------------------------------------------------------------------------------------------
// 获取当前时间-微秒级
// 参数:　
// 返回值: 0：OK  1：fail
//-------------------------------------------------------------------------------------------------------------------------
long getCurrentuSec(void)
{
    long uSec;
    struct timeval tps;
    gettimeofday(&tps, NULL);
    uSec = 1000000 * tps.tv_sec + tps.tv_usec;
    return uSec & 0x7fffffff;
}

//-------------------------------------------------------------------------------------------------------------------------
// 获取当前时间-毫秒级
// 参数:　
// 返回值: 0：OK  1：fail
//-------------------------------------------------------------------------------------------------------------------------
long getCurrentmSec(void)
{
    long uSec;
    struct timeval tps;
    gettimeofday(&tps, NULL);
    uSec = 1000000 * tps.tv_sec + tps.tv_usec;
    return uSec;
    //return getCurrentuSec() / 1000;
}

//-------------------------------------------------------------------------------------------------------------------------
// 获取当前时间-秒级
// 参数:　
// 返回值: 0：OK  1：fail
//-------------------------------------------------------------------------------------------------------------------------
long getCurrentSec(void)
{
#if 1
    timespec time;
    clock_gettime(CLOCK_REALTIME, &time);
    return time.tv_sec;
#else
    return getCurrentmSec() / 1000;
#endif
}

//-------------------------------------------------------------------------------------------------------------------------
// json转string
// 参数:　
// 返回值: json
//-------------------------------------------------------------------------------------------------------------------------
string jsonTostring(Json::Value json, bool jsonStyle)
{
    Json::StyledWriter sWriter;
    Json::FastWriter fWriter;

    return jsonStyle ? sWriter.write(json) : fWriter.write(json);
}

//-------------------------------------------------------------------------------------------------------------------------
// hex转string
// 参数:　
// 返回值: string
//-------------------------------------------------------------------------------------------------------------------------
string hexTostring(u32 hex)
{
    char buf[8];
    string str;
    sprintf(buf, "%x", hex);
    str = buf;
    return str;
}

/**
 * string转json
 * @param str
 * @return
 */
Json::Value stringToJson(string str)
{
    Json::Value json;
    Json::Reader reader;
    reader.parse(str, json);
    return json;
}

/**
 * 字符串替换
 * @param strSrc 源string
 * @param strOld 被替换的字符串
 * @param strNew 替换的新字符串
 */
void stringReplace(string &strSrc, const string &strOld, const string &strNew)
{
    string::size_type pos = 0;
    string::size_type srcLen = strOld.size();
    string::size_type dstLen = strNew.size();

    while ((pos = strSrc.find(strOld, pos)) != string::npos)
    {
        strSrc.replace(pos, srcLen, strNew);
        pos += dstLen;
    }
}

/**
 * @description: 检查目录是否存在
 * @param {type} 
 * @return: true:存在 false:不存在
 */
bool isFolderExist(const char *path)
{
    DIR *dp;
    if ((dp = opendir(path)) == NULL)
    {
        return false;
    }

    closedir(dp);
    return true;
}

/**
 * @description: 检查文件(所有类型)是否存在
 * @param {type} 
 * @return: true:存在 false:不存在 
 * @access return -1:不存在 0:存在
 */
bool isFileExist(const char *path)
{
    bool b;
    int ret = access(path, F_OK);
    return (b = ((-1 == ret) ? false : true));
    /*
	if (-1 != access(path, F_OK))
		return true;
	return false;*/
}

#define MD5_MAX_LENGTH 1024
#define MD5_LENGTH 16
/**
 * @description: 计算文件md5签名
 * @param {type} filePath->文件路径
 * @param md5Out->输出md5字符串
 * @return: RET_OK, RET_FAIL
 */
int doFile_MD5(string filePath, string &md5Out)
{
    std::ifstream fp(filePath.c_str(), ios::in | ios::binary);
    u8 pMD5[MD5_LENGTH];
    string hexString;
    char pBuffer[MD5_MAX_LENGTH];

    if (fp.fail())
    {
        LOG(ERROR) << "open md5 file error";
        return RET_FAIL;
    }

    MD5_CTX md5_ctx;
    MD5_Init(&md5_ctx);

    while (!fp.eof())
    {
        fp.read(pBuffer, MD5_MAX_LENGTH);
        int length = fp.gcount();
        if (length)
        {
            MD5_Update(&md5_ctx, pBuffer, length); //将当前文件块加入并更新MD5
        }
    }
    MD5_Final(pMD5, &md5_ctx);
    HexArrayToString(hexString, pMD5, MD5_LENGTH);
    md5Out = hexString;
    return RET_OK;
}

int doMD5(char *data, int len, string &md5Out)
{
    MD5_CTX ctx;
    string s;
    u8 pMD5[MD5_LENGTH];

    MD5_Init(&ctx);
    MD5_Update(&ctx, data, len);
    MD5_Final(pMD5, &ctx);

    HexArrayToString(md5Out, pMD5, MD5_LENGTH);
    return RET_OK;
}
/**
 * @description: 执行指定路径的shell脚本文件
 * @param {type} :filePath-> shell脚本路径
 * @return: 
 */
string doShell(string filePath)
{
    FILE *fp;
    string cmd;
    string result;
    char buf[256];
#if 0
	if (!isFileExist(filePath.c_str()))
	{
		LOG(ERROR) << "shell file run.sh no exist";
		return "failed";
	}
#endif
    cmd = "sh " + filePath;
    LOG(INFO) << cmd;
    fp = popen(cmd.c_str(), "r");
    fgets(buf, sizeof(buf), fp);
    result = buf;
    pclose(fp);
    return result;
}

/**
 * @description: 判断是否存在sd卡分区
 * @param {type} 
 * @return: 
 */
bool isExistSdCard()
{
    return  false;
    //TODO modify sd card
    FILE *fp;
    string cmd;
    string result;
    char buf[256];
    cmd = "cat /proc/partitions | grep mmcblk0p1 | awk '{print "
          "$4"
          "}'";
    LOG(INFO) << cmd;

    fp = popen(cmd.c_str(), "r");
    fgets(buf, sizeof(buf), fp);
    result = buf;
    pclose(fp);
    LOG(INFO) << result;
    if (result.find("mmcblk0p1") != std::string::npos)
        return true;

    return false;
}

/**
 * @description: 挂载分区
 * @param:partition-> /dev/mmcblk0p1
 * @return: 
 */
int mountSdCard(string partition)
{
    string cmd;

    if (!isExistSdCard())
    {
        //LOG_WARNING << "sdcard no exist";
        return RET_FAIL;
    }

    cmd = "mount -t vfat " + partition + " " + "/mnt";
    LOG(INFO) << "mount sdcard:" << cmd;
    system(cmd.c_str());
    return RET_OK;
}


int bcd_decimal_code( int bcd)//bcd转十进制
{
    int sum = 0, i, c=1;//sum返回十进制，i循环计数，c每次翻10倍
    for( i = 1; bcd > 0; i++)
    {
        if( i >= 2)
        {
            c*=10;
        }
        sum += (bcd%16) * c;
        bcd /= 16;//  /16同理与十进制除10将小数点左移一次，%16也同理
    }
    return sum;
}



int decimal_bcd_code(int decimal)//十进制转BCD码
{
    int sum = 0, i;//i计数变量，sum返回的BCD码
    for ( i = 0; decimal > 0; i++)
    {
        sum |= ((decimal % 10 ) << ( 4*i));
        decimal /= 10;
    }
    return sum;
}




typedef struct _mem_info_t
{
    char name[20];
    unsigned long total;
    char name2[20];
}mem_info_t;

void get_memory_usage( float &mem_g, float &usage)
{
    FILE* fp = NULL;
    char buf[256] = {0};
    mem_info_t info;
    double mem_total, mem_used_rate;

    fp = fopen("/proc/meminfo", "r");
    if (fp == NULL){
        perror("meminfo error\n");
        mem_g = usage = 0;
        return;
    }

    fgets(buf, sizeof(buf), fp);
    sscanf(buf, "%s %lu %s\n", info.name, &info.total, info.name2);
    mem_total = info.total;
    fgets(buf, sizeof(buf), fp);
    sscanf(buf, "%s %lu %s\n", info.name, &info.total, info.name2);
    mem_used_rate = (1 - info.total / mem_total) * 100;
    mem_total = mem_total / (1024 * 1024); //KB -> GB

    //printf("内存大小为: %.0lfG, 占用率为: %.2lf%%\n", mem_total, mem_used_rate);
    mem_g = mem_total;
    usage = mem_used_rate;
    fclose(fp);
}
#include <sys/statvfs.h>
#define SYSTEM_BITS  64
#define PATH "/"
#define KB 1024
#define MB 1024*1024
#define GB  1024*1024*1024
void get_hard_drive_usage(float &total_g, float &usage)
{

    int state;
    struct statvfs vfs;
    fsblkcnt_t block_size = 0;
    fsblkcnt_t block_count = 0;
    fsblkcnt_t total_size;
    fsblkcnt_t free_size;
    fsblkcnt_t used_size;
    fsblkcnt_t avail_size;
    /*读取根目录，就是整个系统的大小*/
    state = statvfs(PATH,&vfs);   /*设置路径，查看不同文件目录的大小*/
    if(state < 0){
        //printf("read statvfs error!!!\n");
        total_g = 0;
        usage = 0;
        return;
    }

    block_size = vfs.f_bsize; /*获取一个block的大小*/
    /*获取总容量*/
    total_size = vfs.f_blocks * block_size;
    /*获取可用容量*/
    free_size = vfs.f_bfree * block_size;
    /*获取使用容量*/
    used_size = (vfs.f_blocks - vfs.f_bavail) * block_size;
    /*获取有效容量*/
    avail_size = vfs.f_bavail * block_size;

    //printf(" total_size   = %0.2lf  GB\n",(double)total_size / (GB) );
    //printf(" free_size   = %0.2lf  GB\n",(double)free_size / (GB) );
    //printf(" used_size  = %0.2lf  GB\n",(double)used_size / (GB) );
    //printf(" avail_size  = %0.2lf  GB\n",(double)avail_size / (GB) );
    //printf(" percent: %0.2lf%%\n",(double) used_size/total_size);
    total_g = (double)total_size / (GB);
    usage = (double) used_size/total_size * 100;

    //printf("total_g :%0.2lf used_size: %0.2lf\n",total_g, usage);

#if 0
    FILE* fp = NULL;
    int h = 0;
    char buf[1024], a[80], d[80], e[80], f[80];
    float b, c;
    float total = 0, used = 0;

    fp = popen("df | grep -E \'Filesystem|/dev/root|/dev/mmcblk\'", "r");
    fgets(buf, 512, fp);
    std::cout<<buf <<endl;
    while (6 == fscanf(fp, "%s %f %f %s %s %s", a, &b, &c, d, e, f))
    {
        std::cout << "b:" << b << "c:" <<c <<endl;
        total += b;
        used += c;
    }
    pclose(fp);

    total_g =  total / (1024 * 1024);
    usage = used / total * 100;
#endif
}

/* cpu_info_t结构体存放cpu相关信息 */
typedef struct _cpu_info
{
    char name[20];
    unsigned int user;
    unsigned int nice;
    unsigned int system;
    unsigned int idle;
    unsigned int iowait;
    unsigned int irq;
    unsigned int softirq;
}cpu_info_t;

/* 从/proc/stat文件中获取cpu的相关信息 */
void get_cpu_occupy(cpu_info_t* info)
{
    FILE* fp = NULL;
    char buf[256] = {0};

    fp = fopen("/proc/stat", "r");
    fgets(buf, sizeof(buf), fp);

    sscanf(buf, "%s %u %u %u %u %u %u %u", info->name, &info->user, &info->nice,
           &info->system, &info->idle, &info->iowait, &info->irq, &info->softirq);

    fclose(fp);
}

/* 计算cpu的使用率 */
double calc_cpu_rate(cpu_info_t* old_info, cpu_info_t* new_info)
{
    double od, nd;
    double usr_dif, sys_dif, nice_dif;
    double user_cpu_rate;
    double kernel_cpu_rate;

    od = (double)(old_info->user + old_info->nice + old_info->system + old_info->idle + old_info->iowait + old_info->irq + old_info->softirq);
    nd = (double)(new_info->user + new_info->nice + new_info->system + new_info->idle + new_info->iowait + new_info->irq + new_info->softirq);

    if (nd - od)
    {
        user_cpu_rate = (new_info->user - old_info->user) / (nd - od) * 100;
        kernel_cpu_rate = (new_info->system - old_info->system) / (nd - od) * 100;

        return user_cpu_rate + kernel_cpu_rate;
    }
    return 0;

}



/**
 * CPU使用率
 * @return
 */
void get_cpu_usage(float &usage)
{
    static cpu_info_t info1 = {0};
    static cpu_info_t info2 = {0};
    get_cpu_occupy(&info2);
    usage = calc_cpu_rate(&info1, &info2);
    memcpy((void *)&info1,(void *)&info2,sizeof(cpu_info_t));
}



void get_hostname_ip(char *ip)
{
    FILE* fp = NULL;
    char buf[256] = {0};

    fp = popen("hostname -I", "r");
    if (fp == NULL){
        perror("error\n");
    } else{
        fgets(buf, sizeof(buf), fp);
        sscanf(buf, "%15s", ip);
    }
    fclose(fp);

}

