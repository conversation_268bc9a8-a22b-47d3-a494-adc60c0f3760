/*
 * @Description: 
 * @Author: 
 * @Create Date: 
 * @Version: 1.0
 * @LastEditTime: 2020-05-26 17:37:49
 * @LastEditors: Please set LastEditors
 */
#ifndef __GENERALFUNC_H
#define __GENERALFUNC_H

#include <cstdio>
#include <string>
#include <vector>
#include <cstdio>
#include <cstdlib>
#include <json/json.h>
#include <json/value.h>
#include <glog/logging.h>
#include <unistd.h>
#include "curl/curl.h"
#include "curl/easy.h"

using namespace std;

#define PRINTF_CHAR 1
#define PRINTF_HEX 0
typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;

typedef signed char s8;
typedef signed short s16;
typedef signed int s32;

typedef void (*FuncDef_Check)(void *);
typedef void (*FuncDef_Null)(void);
typedef u32 (*FuncDef_IntR)(void);
typedef u32 (*FuncDef_Data)(u8 *, u32);
typedef void (*FuncDef_SetResp)(u32);
typedef void (*FuncDef_StrI)(u8 *);
typedef void (*FuncDef_IntSI)(s32); //SI->入参为单个INT型
typedef void *(*FuncDef_P)(void *);

#define RET_OK 0
#define RET_FAIL 1
#define RET_ING 3
#define RET_WAIT_ACK 4


int systemX(string &cmd, string &result,bool isOut = false);

/**
 * 执行一个shell命令，输出结果逐行存储在result中，并返回行数
 * @param cmd
 * @param result
 * @return
 */
int32_t executeSysCmd(const char *cmd, vector<string> &result);
/**
 * 域名转IP
 * @param domain
 * @param out
 * @return
 */
int domainToIP(const char *domain, string &out);


/**
 * 初始化测温模块
 * @return
 */
int initTempReg(void);

/**
 * 更新时间
 * @return
 */
int updateTime(void);

/**
 * 获取主机hostip
 * @param domain
 * @param out
 * @param isWriteToHosts
 * @return
 */
int getHostWithDomain(string domain, string &out, bool isWriteToHosts = false);

/**
 * 执行系统命令
 * @param cmd
 * @return
 */
string executeSysCmd(string cmd);

/**
 * 获取disk——uuid
 * @return
 */
string getDiskUuid();


void printHex(const char *strp, u8 *pData, u32 nLen);

u8 calXor(u8 *pData, u32 nCount);
u8 calChecksum(u8 *pData, u32 nCount);
u8 splitString(const std::string &s, std::vector<std::string> &v, const std::string &c);
//void char2string(std::string &s, u8 *pbuffer, u32 nLen);

long getCurrentuSec(void);
long getCurrentmSec(void);
long getCurrentSec(void);
Json::Value stringToJson(string str);
string jsonTostring(Json::Value json, bool jsonStyle = true);
string hexTostring(u32 hex);
void stringReplace(string &strSrc, const string &strOld, const string &strNew);
//void hexArrayToString(string &s, u8 *phex, u32 length);
void stringToHexArray(string s, u8 *phex, u32 &length);
bool isFolderExist(const char *path);
bool isFileExist(const char *path);
int doFile_MD5(string filePath, string &md5Out);
int doMD5(char *data, int len, string &md5Out);
string doShell(string filePath);
bool isExistSdCard();
int mountSdCard(string partition);

int bcd_decimal_code( int bcd);
int decimal_bcd_code(int decimal);

void get_memory_usage(float &mem_g, float &usage);
void get_hard_drive_usage(float &total_g, float &usage);
void get_cpu_usage(float &usage);
void get_hostname_ip(char *ip);
#endif