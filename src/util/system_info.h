//
// Created by seashell on 6/25/21.
//

#ifndef GATEWAY_IOT_SYSTEM_INFO_H
#define GATEWAY_IOT_SYSTEM_INFO_H

#include <string>
using namespace std;

/**
 * @description: telnet设置
 * @param {type}
 * @return:
 */
int EnableTelnet(int val);


/**获取内存使用率
 * @description:
 * @param {type}
 * @return:
 */
float GetMemoryUsage();

/**获取CPU使用率
 * @description:
 * @param {type}
 * @return:
 */
string GetCpuUsage();


/**
 * @description: 获取CPU内部温度
 * @param T= [(tsensor_temp_code - 125)/806] * 165 - 40
 * @return:
 */
float GetCpuTemp(void);


#endif //GATEWAY_IOT_SYSTEM_INFO_H
