//
// Created by seashell on 5/10/24.
//

#include "HexTool.h"


std::string HexTool::floatToHex(float value) {
    std::stringstream stream;
    stream << std::hex << std::uppercase << std::setfill('0') << std::setw(8);
    unsigned int intValue;
    std::memcpy(&intValue, &value, sizeof(float));
    stream << intValue;
    return stream.str();
}

float HexTool::hexToFloat(const std::string& hexString) {
    unsigned int intValue;
    std::stringstream stream;
    stream << std::hex << hexString;
    stream >> intValue;
    float floatValue;
    std::memcpy(&floatValue, &intValue, sizeof(float));
    return floatValue;
}