/*
 * @Description: 
 * @Author: 
 * @Date: 2019-12-31 09:07:19
 * @Version: 
 * @LastEditTime: 2019-12-31 09:07:20
 * @LastEditors: 
 */
#ifndef __SERIAL_DEVICE_H_
#define __SERIAL_DEVICE_H_


int serialTest();

class serialDevice
{
private:
public:
    serialDevice();
    ~serialDevice();
    int init();
    bool OpenSerial(const char *pDev, int baudrate, int databits, char parity, int stopbits, int flowcontrol);
    int RecvMessage(unsigned char *pdata, int data_len);
    int SendMessage(unsigned char *pdata, int data_len);
    bool IsOpen();

private:
    int mFd;
    int ConfigSerial(int baudrate, int databits, char parity, int stopbits, int flowcontrol);

    void CloseSerial();
};

#endif