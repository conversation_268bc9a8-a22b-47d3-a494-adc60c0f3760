//
// Created by seashell on 6/25/21.
//

#include <cstring>
#include "system_info.h"
#include "common_func.h"


/**
 * 启用远程SSH
 * @param enable
 * @param local_ip
 * @param local_ssh_port
 * @param remote_ip
 * @param remote_ssh_port
 * @param remote_listen_port
 * @param remote_user
 * @param password
 * @return
 */
int enable_ssh(bool enable,
               string &local_ip,
               int local_ssh_port,
               string &remote_ip,
               int remote_ssh_port,
               int remote_listen_port,
               string &remote_user,
               string &remote_user_pwd,
               string &password)
{
    //ssh -o ServerAliveInterval=60 -f -N -R 9090:*************:22 -p 55555 yciot@***************
    //ssh -o ServerAliveInterval=15 -qfTNn -R [::]:2222:localhost:22 gw@***************
    //ssh -D *************:8080 seashell@*************
    string cmdStr;
    string result;

    if (enable){
        char cmd[260] = {0};
        snprintf(cmd,260,"sshpass -p %s ssh -o ServerAliveInterval=60 -f -N -R %d:%s:%d -p %d %s@%s",
                 remote_user_pwd.c_str(),
                 remote_listen_port,
                 local_ip.c_str(),
                 local_ssh_port,
                 remote_ssh_port,
                 remote_user.c_str(),
                 remote_ip.c_str());
        cmdStr = cmd;
        return  systemX(cmdStr,result);
    }else{
        cmdStr = "killall -9 ssh";
        return systemX(cmdStr,result);
    }

}
/**
 * @description: telnet设置
 * @param {type} 
 * @return: 
 */
int EnableTelnet(int val)
{
    FILE *stream;
    int pid = 0;
    unsigned char buf[16];
    vector<string>result;
    if (executeSysCmd("pgrep telnetd", result) != 0){
        pid = atoi(result[0].c_str());
    }

    LOG(INFO) << "telnetd pid:" << pid;
    if (0 == val)
    {
        //telnetd closed
        if (pid <= 0)
        {
            LOG(ERROR) << "telnetd cannot be found";
            return RET_FAIL;
        }
        else
        {
            string cmd;
            cmd = "kill -s 9 " + to_string(pid);
            LOG(INFO) << cmd;
            system(cmd.c_str());
        }
    }
    else if (1 == val)
    {
        //telnet opened
        if (pid > 0)
            //LOG_WARNING << "telnetd is running, do not reopen";;
            LOG(INFO) << "telnetd is running, do not reopen" <<endl;
        else
            system("telnetd &");
    }

    return RET_OK;
}

/**获取内存使用率
 * @description: 
 * @param {type} 
 * @return: 
 */
float GetMemoryUsage()
{
    unsigned int len, totalMem, usedMem, freeMem;
    float memUsage;
    FILE *stream;
    unsigned char buf[16];
    stream = popen("free | grep Mem | awk '{print "
                   "$2"
                   "}'",
                   "r");
    if (stream == NULL)
        return RET_FAIL;
    len = fread(buf, sizeof(unsigned char), sizeof(buf), stream);
    if (len < 0)
        return 0;
    pclose(stream);
    totalMem = atoi((char *)buf);

    stream = popen("free | grep Mem | awk '{print "
                   "$3"
                   "}'",
                   "r");
    if (stream == NULL)
        return 0;
    len = fread(buf, sizeof(unsigned char), sizeof(buf), stream);
    if (len < 0)
        return 0;
    pclose(stream);
    usedMem = atoi((char *)buf);

    freeMem = totalMem - usedMem;
    memUsage = (float)(usedMem * 100.0f) / (float)totalMem;
    // LOG(INFO) << "totalMem: " << totalMem << " usedMem: " << usedMem << " freeMem: " << freeMem << " usageMem: " << memUsage;
    // LOG(INFO) << "内存使用率: " << memUsage;
    return memUsage;
}

/**获取CPU使用率
 * @description: 
 * @param {type} 
 * @return: 
 */
string GetCpuUsage()
{
    string s;
    FILE *stream;
    char buf[16];
    memset(buf, 0, sizeof(buf));
    stream = popen("top -bn 1 | grep "
                   "CPU:"
                   "| awk 'NR==1{print $2}'",
                   "r");
    // stream = popen("mpstat | awk 'NR==4{print $3}'", "r");
    if (stream == NULL)
        return s;
    int len = fread(buf, sizeof(unsigned char), sizeof(buf), stream);
    if (len < 0)
        return s;

    pclose(stream);
    s = buf;
    s = s.substr(0, s.length() - 2); //trim LF and '%'
    return s;
}

/**
 * @description: 获取CPU内部温度 
 * @param T= [(tsensor_temp_code - 125)/806] * 165 - 40
 * @return: 
 */
float GetCpuTemp(void)
{
    float fTemperature = 0;
    /*unsigned int regVal;

    readHisiReg(ADDR_REG_TEMP_BASE | 0x009C, &regVal);
    // printf("Addr9C:%x\n", regVal);
    if (RET_FAIL == readHisiReg(ADDR_REG_TEMP_BASE | 0x00A4, &regVal))
    {
        LOG(ERROR) << "get cpu temperature failed";
        return 0.0f;
    }
    // printf("温度记录值:%x\n", regVal);
    float val = regVal & 0x3ff;
    fTemperature = (((val - 125) / 806) * 165) - 40;
    // LOG(INFO) << "CPU内部温度2 " << (((val2 - 125) / 806) * 165) - 40;*/
    return fTemperature;
}
