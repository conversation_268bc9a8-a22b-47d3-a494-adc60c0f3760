#include "http_method.h"


/**
 * 接收数据的回调
 * @param contents
 * @param size
 * @param nmemb
 * @param s
 * @return
 */
size_t curl_write_callback_std_string(void *contents, size_t size, size_t nmemb, std::string *s)
{
    size_t newLength = size * nmemb;
    s->append((char *)contents, newLength);
    return newLength;
}

/**
 * 写文件
 * @param ptr
 * @param size
 * @param nmemb
 * @param stream
 * @return
 */
size_t write_data(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
    size_t written = fwrite(ptr, size, nmemb, stream);
    return written;
}
/**
 * 发送http请求
 * @param url
 * @param post_data
 * @param header
 * @param replyData
 * @return
 */
int HttpPost(const std::string &url, const char *post_data, std::string &replyData, const std::string &header, int timeout)
{
    std::string responseJson("");
    CURL *curl;
    CURLcode res = CURLE_OK;

    curl = curl_easy_init();
    if (curl)
    {
        curl_slist *plist = curl_slist_append(NULL, header.c_str());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, plist);
        curl_easy_setopt(curl, CURLOPT_POST, 1);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, strlen(post_data));
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &responseJson);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, curl_write_callback_std_string);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout);

        //
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        //
        res = curl_easy_perform(curl);
        curl_slist_free_all(plist);
    }
    replyData = responseJson;

    curl_easy_cleanup(curl);

    return res;
}

/**
 *
 * @param url
 * @param get_data
 * @return
 */
int HttpGet(std::string url, std::string *get_data, int timeout)
{
    std::string responseJson;
    CURL *curl;
    CURLcode res;
    curl = curl_easy_init();
    if (curl)
    {
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &responseJson);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, curl_write_callback_std_string);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout);
        res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);
    }
    *get_data = responseJson;
    return res;
}

/**
 *
 * @param url
 * @param path
 * @return
 */
int HttpDownloadFile(const std::string &url, const char *path)
{
    CURL *curl;
    CURLcode res;
    FILE *fp;
    if ((fp = fopen(path, "w+")) == NULL)
    {
        LOG(ERROR) << "open download file url failed, path:" << path;
        return RET_FAIL;
    }

    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Accept: */*");
    curl_slist_append(headers, "User-agent: YC-IOT");
    curl = curl_easy_init();
    if (curl)
    {
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, fp);
        res = curl_easy_perform(curl);
        if (res != 0)
        {
            curl_slist_free_all(headers);
            //清理
            curl_easy_cleanup(curl);
        }
        fclose(fp);
        return RET_OK;
    }
    fclose(fp);
    return RET_FAIL;
}
