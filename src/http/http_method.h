/*
 * @Description: In User Settings Edit
 * @Author: your dev_name
 * @Date: 2019-09-06 08:27:51
 * @LastEditTime: 2019-09-10 08:56:08
 * @LastEditors: Please set LastEditors
 */

#ifndef __HTTP_CLASS_H_
#define __HTTP_CLASS_H_

#include "common_func.h"


enum httpMethodType
{
    httpPost = 0,
    httpGet,
    httpDownloadFile
};

/**
 * 发送http请求
 * @param url       请求url
 * @param post_data 请求数据
 * @param header    请求http头
 * @param replyData 应答数据
 * @return
 */
int HttpPost(const std::string &url, const char *post_data, std::string &replyData, const std::string &header, int timeout);
int HttpGet(std::string url, std::string *get_data, int timeout = 60);
int HttpDownloadFile(const std::string &url, const char *path);

#endif
