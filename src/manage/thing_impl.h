//
// Created by seashell on 11/3/21.
//

#ifndef GATEWAY_IOT_THING_IMPL_H
#define GATEWAY_IOT_THING_IMPL_H

#include "thing_interface.h"
#include <json/value.h>
#include <string>
#include "mosquitto.h"
#include "device_manager.h"
#include "message_dispatch.h"

class ThingImpl : public ThingInterface{

public:

    ThingImpl() = default;
    ~ThingImpl() = default;


    int Send(string &topic, string &payload) override;

    /**
     *
     * @param mid
     * @param topic
     * @return
     */
    int SubscribeTopic(int &mid, string &topic) override;




    /**
     * 查询设备秘钥接口，用户需保存设备的秘钥，物模型查询秘钥用于解密IOT数据，需外部实现该接口
     * @param productKey    产品的key
     * @param deviceName    设备的id
     * @return
     */
    string GetDevSecret(string &productKey, string deviceName) override;



    /**
     * 收到IOT平台的消息接口，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    int ReceiveIoTMessage(string &topic, Json::Value &payload) override;



    /***
     * 设置IOT连接管理对象实例
     * @param handler
     */
    void setIotMqttHandler(IoTMqtt *handler);

    /***
     * 设置设备管理对象实例
     * @param handler
     */
    void setDeviceManagerHandler(DeviceManager *handler);


    /***
     * 设置分发对象实例
     * @param handler
     */
    void setMessageDispatchHandler(MessageDispatch *handler);

private:
    IoTMqtt *mIoTMqttHandler = nullptr;
    DeviceManager *mDeviceManger = nullptr;
    MessageDispatch *mMessageDispatch = nullptr;
};


#endif //GATEWAY_IOT_THING_IMPL_H
