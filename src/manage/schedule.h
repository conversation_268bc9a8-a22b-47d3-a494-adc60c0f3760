//
// Created by seashell on 1/5/22.
//

#ifndef GATEWAY_IOT_SCHEDULE_H
#define GATEWAY_IOT_SCHEDULE_H

#include <map>
#include <string>
#include "message_dispatch.h"
using namespace std;


class ScheduledTime
{
public:
    string deviceName;
    int period;
    int currentTick;
    bool single;
};

class Schedule {


private:
    map<string , ScheduledTime> mScheduleMap;
    MessageDispatch *messageDispatch;
    void Run();
public:
    void AddScheduled(string &deviceName, int period, bool single);
    void RemoveScheduled(string &deviceName);

    Schedule(MessageDispatch * dispatch);
    ~Schedule() = default;
};


#endif //GATEWAY_IOT_SCHEDULE_H
