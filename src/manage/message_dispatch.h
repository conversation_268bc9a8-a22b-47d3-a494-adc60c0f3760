/**
 *  消息分发类
 *  分发所有消息，包括设备上行和平台下行。
 */

#ifndef GATEWAY_IOT_MESSAGE_DISPATCH_H
#define GATEWAY_IOT_MESSAGE_DISPATCH_H


#include <thing_message.h>
#include "device.h"
#include "thing_server.h"
#include "device_manager.h"
#include "message_dispatch_interface.h"

class MessageDispatch {

#if 0
public:
    ThingDevice *mGateway;

    static MessageDispatch& getInstance(){
        static MessageDispatch instance;
        return instance;
    }

    MessageDispatch(const MessageDispatch& other) = delete;
    MessageDispatch& operator=(const MessageDispatch& other) = delete;

protected:
    MessageDispatch() = default;
    ~MessageDispatch() = default;

#else

public:
    MessageDispatch(MessageDispatchInterface *impl);
    ~MessageDispatch() = default;
#endif

    std::mutex mutex_;
    std::condition_variable condVar;

public:
    /**
     * 分发消息
     */
    void DispatchMessage();

    /**
     * 分发iot数据
     */
    void DispatchIotMessage();

    /**
     * 分发传感器数据
     */
    void DispatchSensorMessage();

    /**
     * 压入IOT消息
     * @param topic
     * @param value
     * @return
     */
    int PushIotMessageToQueue(string &topic, Json::Value &value);

    /**
     * 压入传感器数据
     * @param strBuf
     * @return
     */
    int PushSensorMessage(string &strBuf);

    /**
     * 定时任务消息入栈
     * @param topic
     * @param value
     * @return
     */
    int PushScheduledMessageToQueue(string deviceName);

    /**
     * 分发定时任务消息
     */
    void DispatchScheduledMessage();
private:

    ThingQueue<ThingMessage> iotMsgQueue;
    ThingQueue<string> sensorMsgQueue;
    ThingQueue<string> scheduledQueue;
    MessageDispatchInterface *mInterface;

    int AnalyzeSensorMessage(string &message, string &data, DeviceParam &device);

};


#endif //GATEWAY_IOT_MESSAGE_DISPATCH_H
