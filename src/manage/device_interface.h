//
// Created by seashell on 12/2/21.
//

#ifndef DEVICE_INTERFACE_H
#define DEVICE_INTERFACE_H



#include <ns_mqtt.h>
#include "thing_server.h"
class Device;

class DeviceInterface{

#if 0
public:
    static DeviceInterface& getInstance(){
        static DeviceInterface instance;
        return instance;
    }

    DeviceInterface(const DeviceInterface& other) = delete;
    DeviceInterface& operator=(const DeviceInterface& other) = delete;

protected:
    DeviceInterface() = default;
    virtual ~DeviceInterface() = default;

#else

public:
    DeviceInterface() = default;
    virtual ~DeviceInterface() = default;


#endif



public:

    /**
     * 发送消息到设备
     * @param base64Data
     * @return
     */
    virtual int SendMessageToSensor(Device *device, string &base64Data) = 0;


    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    virtual int ReportDevEvent(ThingDevice *device, string event_name, Json::Value &data) = 0;



    /**
     * 发送设备数据，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    virtual int ReportPropEvent(ThingDevice *device, Json::Value &data) = 0;


    /**
     * 发送设备版本
     * @param topic
     * @param payload
     * @return
     */
    virtual int ReportDevVersion(ThingDevice *device, string &version) = 0;

    /**
     * 发送设备ota progress
     * @param topic
     * @param payload
     * @return
     */
    virtual int ReportDevOTAProgress(ThingDevice *device, string &step, string &desc) = 0;

    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    virtual int ResponseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value) = 0;


    /**
     * 计划任务
     * @param deviceName
     * @param period
     * @param single
     * @return
     */
    virtual int ScheduleWork(string deviceName, int period, bool single) = 0;
};


#endif //DEVICE_INTERFACE_H
