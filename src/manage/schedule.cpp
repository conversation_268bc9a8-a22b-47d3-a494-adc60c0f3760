//
// Created by seashell on 1/5/22.
//

#include "schedule.h"
#include <thread>

Schedule::Schedule(MessageDispatch * dispatch)
{
    messageDispatch = dispatch;
    std::thread t1(&Schedule::Run, this);
    t1.detach();

}

void Schedule::Run()
{
    for (;;)
    {
        for (auto &item : mScheduleMap)
        {
            item.second.currentTick --;
            if (item.second.currentTick <= 0)
            {
                messageDispatch->PushScheduledMessageToQueue(item.first);
                item.second.currentTick = item.second.period;
                if (item.second.single == true)
                {
                    mScheduleMap.erase(item.first);
                }
            }
        }
        sleep(1);
    }
}

void Schedule::AddScheduled(string &deviceName, int period, bool single)
{
    ScheduledTime sch;
    sch.currentTick = period;
    sch.deviceName = deviceName;
    sch.period = period;
    sch.single = single;
    mScheduleMap[deviceName] = sch;
}

void Schedule::RemoveScheduled(string &deviceName)
{
    mScheduleMap.erase(deviceName);
}
