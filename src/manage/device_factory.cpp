#include "device_factory.h"
#include "gateway_dev.h"
#include "soil_ph_dev.h"
#include "soil_temperature_dev.h"
#include "weather_dev.h"
#include "radar_level_dev.h"
#include "manhole_cover_dev.h"
#include "hydrant_dev.h"
#include "rainfall_dev.h"
#include "wind_direction_dev.h"
#include "wind_speed_dev.h"
#include "radar_light_dev.h"
#include "transparent_dev.h"
#include "displacement_dev.h"

#include "infrared_dev.h"
#include "lamp_switch_dev.h"
#include "liquid_level_dev.h"
#include "liquid_level_underground_dev.h"
#include "magnetism_dev.h"
#include "sl_alarm_dev.h"
#include "smoke_dev.h"
#include "sos_dev.h"
#include "th_dev.h"
#include "water_immersion_dev.h"
#include "water_ph_dev.h"
#include "water_pressure_dev.h"

#include "water_meter_dev.h"
#include "electricity_meter_dev.h"
#include "three_phase_electricity_meter_dev.h"
#include "solenoidvalve_dev.h"
#include "fx_theftproof_dev.h"
/**
 * 简单工厂，创建设备并返回
 * @param node
 * @return
 */
Device *DeviceFactory::CreateDevInstance(DeviceParam &node) {
    LOG(INFO) << "DeviceFactory::CreateDevInstance(), dev_type:" << node.dev_type;

    Device *device = nullptr;
    switch (node.dev_type)
    {
        case DEV_GATEWAY:
            device = (Device *)new GatewayDev(node);
            break;
        case DEV_WIND_SPEED:
            device = (Device *)new WindSpeedDev(node);
            break;
        case DEV_WIND_DIREC:
            device = (Device *)new WindDirectionDev(node);
            break;
        case DEV_SOIL_PH:
            device = (Device *)new SoilPHDev(node);
            break;
        case DEV_SOIL_TH:
            device = (Device *)new SoilTemperatureDev(node);
            break;
        case DEV_LEVEL:
            device = (Device *)new LiquidLevelDev(node);
            break;
        case DEV_MANHOLE_COVER:
            device = (Device *)new ManholeCoverDev(node);
            break;
        case DEV_WATER_IMMERSION:
            device = (Device *)new WaterImmersionDev(node);
            break;
        case DEV_RAINFALL:
            device = (Device *)new RainfallDev(node);
            break;
        case DEV_WATER_PRESSURE:
            device = (Device *)new WaterPressureDev(node);
            break;
        case DEV_WEATHER_STATION:
            device = (Device *)new WeatherDev(node);
            break;
        case DEV_WATER_PH:
            device = (Device *)new WaterPhDev(node);
            break;
        case DEV_LEVEL_UNDERGROUND:
            device = (Device *)new LiquidLevelUndergroundDev(node);
            break;
        case DEV_SOS:
            device = (Device *)new SOSDev(node);
            break;
        case DEV_DISPLACEMENT:
            device = (Device *)new DisplacementDdev(node);
            break;
        case DEV_LAMP_SWITCH:
            device = (Device *)new LAMPSwitchDev(node);
            break;
        case DEV_SL_ALARM:
            device = (Device *)new SLAlarmDev(node);
            break;
        case DEV_TH:
            device = (Device *)new THDev(node);
            break;
        case DEV_SMOKE:
            device = (Device *)new SmokeDev(node);
            break;
        case DEV_INFRARED:
            device = (Device *)new InfraredDev(node);
            break;
        case DEV_MAGNETISM:
            device = (Device *)new MagnetismDev(node);
            break;
        case DEV_HYDRANT:
            device = (Device *)new HydrantDev(node);
            break;
        case DEV_RADAR_LIGHT:
            device = (Device *)new RadarLightDev(node);
            break;
        case  DEV_TRANSPARENT:
            device = (Device *)new TransparentDev(node);
            break;

        case DEV_WATER_METER:
            device = (Device *)new WaterMeterDev(node);
            break;
        case DEV_ELECTRICITY_METER:
            device = (Device *)new ElectricityMeterDev(node);
            break;
        case DEV_THREE_ELECTRICITY_METER:
            device = (Device *)new ThreePhaseElectricityMeterDev(node);
            break;
        case DEV_SOLENOID_VALVE:
            device = (Device *)new SolenoidValveDev(node);
            break;
        case DEV_FX_THEFTPROOF:
            device = (Device *)new FXTheftproofDev(node);
            break;
        default:
        {
            LOG(ERROR) << "*********** not support dev_type, can not create instance ***********\n";
            break;
        }
    }
    return device;
}
