//
// Created by seashell on 12/4/21.
//

#ifndef GATEWAY_IOT_IOT_CORE_H
#define GATEWAY_IOT_IOT_CORE_H

#include "iot_mqtt_interface.h"
#include "message_dispatch_interface.h"
#include "ns_mqtt_interface.h"
#include "device_manager_interface.h"
#include "thing_interface.h"
#include "gateway_config.h"
#include "device_manager.h"
#include "message_dispatch.h"
#include <iot_mqtt.h>
#include <ns_mqtt.h>
#include "device_interface.h"
#include "device_helper.h"
#include "schedule.h"

class IoTCore : IoTMqttInterface,
        MessageDispatchInterface,
        ThingInterface,
        NSMqttInterface,
        DeviceManagerInterface,
        DeviceInterface{

private:
    /**
     * 能力集实例，设备能力集相关的操作
     */
    ThingServer *mThingServer;

    /**
     * 设备管理实例
     */
    DeviceManager *mDeviceManager;

    /**
     * IOT平台的MQTT
     */
    IoTMqtt *mIoTMqtt ;

    /**
     * 内置NS的mqtt
     */
    NSMqtt *mNSMqtt ;

    /**
     * 数据分发
     */
    MessageDispatch *mMessageDispatch;

    /**
     * 设备助手
     */
    DeviceHelper *mDeviceHelper;

    /**
     * 计划任务
     */
    Schedule *mSch;
public:
    IoTCore();
    ~IoTCore();
    int init();
    void dispatch();


    /**
     * 发送能力集封装的数据到IOT平台
     * @param topic
     * @param payload
     * @return
     */
    int Send(string &topic, string &payload) override;



    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    int SubscribeTopic(int &mid, string &topic) override;



    /**
     * 查询设备秘钥接口，用户需保存设备的秘钥，物模型查询秘钥用于解密IOT数据，需外部实现该接口
     * @param productKey    产品的key
     * @param deviceName    设备的id
     * @return
     */
    string GetDevSecret(string &productKey, string deviceName) override;



    /**
     * 收到IOT平台的消息接口，需外部实现该接口
     * @param topic
     * @param payload
     * @return
     */
    int ReceiveIoTMessage(string &topic, Json::Value &payload) override;


    /**
     *
     * IOT MQTT连接成功后的回调
     */
    void onIoTMqttConnectStateChanged(bool isConnect) override;


    /**
     * 收到IoT平台下发的消息
     * @param topic
     * @param payload
     * @return
     */
    int receiveMessageFromIoTServer(string &topic, string &payload) override;


    /**
     * 设备上报到网关的消息，消息加入处理队列
     * @param strBuf
     * @return
     */
    int pushSensorMessage(string &strBuf) override;


    /**
     * NS连接成功回调
     * @return
     */
    int onNSMqttConnected() override;

    /**
     * 通过设备的productkey和devicename获取设备实例
     * @param product_key
     * @param device_name
     * @return
     */
    Device *GetDeviceWithProductKeyAndName(string &product_key, string device_name) override;

    /**
     * 动态初始化一个设备,子设备，非网关设备。
     * 网关收到
     * @param deviceInfo
     * @return
     */
    Device *GetSubDeviceInstance(DeviceParam &deviceInfo) override;


    /**
     * 获取设备实例通过deviceName。
     * 网关收到
     * @param deviceInfo
     * @return
     */
    Device *GetSubDeviceInstance(string &deviceName) override;

    /**
    * 网关设备注册
    * @param gatewayDevice
    * @param callback
    */
    int GatewayDevRegister(ThingDevice *gatewayDevice, string &secret) override;

    /**
     * 子设备注册到平台，自动注册
     * @param gatewayDevice
     * @param subDevice
     * @return
     */
    int DevRegister(ThingDevice *gatewayDevice, ThingDevice *subDevice, RegisterResponse &result) override;


    /**
     *
     * 子设备绑定到网关
     * @param gatewayDevice
     * @param device
     * @return
     */
    int DevBind(ThingDevice *gatewayDevice , ThingDevice *device) override;


    /**
     * 子设备上线
     * @param gatewayDevice
     * @param device
     * @return
     */
    int DevOnline(ThingDevice *gatewayDevice , ThingDevice *device) override;



     /**
      * 应答平台的请求
      * @param device
      * @param msgId
      * @param code
      * @param topic
      * @param value
      * @return
      */
    int ResponseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value) override;



    /**
    * 计划任务
    * @param deviceName
    * @param period
    * @param single
    * @return
    */
    int ScheduleWork(string deviceName, int period, bool single)override;


    /**
     * 上报设备的属性到iot平台接口实现。
     * @param topic
     * @param payload
     * @return
     */
    int ReportPropEvent(ThingDevice *device, Json::Value &data) override;


    /**
     * 上报设备的事件到IOT平台接口实现。
     * @param topic
     * @param payload
     * @return
     */
    int ReportDevEvent(ThingDevice *device, string event_name, Json::Value &data) override;


    /**
     * 发送设备版本
     * @param topic
     * @param payload
     * @return
     */
    int ReportDevVersion(ThingDevice *device, string &version) override;

    /**
     * 发送设备ota progress
     * @param topic
     * @param payload
     * @return
     */
    int ReportDevOTAProgress(ThingDevice *device, string &step, string &desc) override;

    /**
     * 设备订阅主题
     * @param gatewayDevice
     * @param device
     * @return
     */
    int DevSubscribeTopic(vector<string> &topicList) override;

    /**
     * 发送消息到设备
     * @param base64Data
     * @return
     */
    int SendMessageToSensor(Device *device, string &base64Data) override;


};


#endif //GATEWAY_IOT_IOT_CORE_H
