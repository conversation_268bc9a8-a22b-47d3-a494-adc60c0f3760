#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H


#include <thing_message.h>
#include "json/json.h"
#include "device.h"
#include "device_persistence.h"
#include "device_factory.h"
#include "device_manager_interface.h"


template<typename T>
class Singleton
{
public:
    static T& GetInstance() {
        static T t;
        return t;
    }

    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;
protected:
    Singleton() = default;
    ~Singleton() = default;
};

class DeviceManager : public DevicePersistence{

#if 0
public:

    static DeviceManager& getInstance(){
        static DeviceManager instance;
        return instance;
    }

    DeviceManager(const DeviceManager& other) = delete;
    DeviceManager& operator=(const DeviceManager& other) = delete;

protected:
    DeviceManager() = default;
    ~DeviceManager();

#else


public:
    DeviceManager(DeviceManagerInterface *impl);
    ~DeviceManager() ;


#endif


private:

    /**
     * 存储设备的map
     */
    map<string, Device *> mDevicesMap;

    /**
     * 设备工厂
     */
    DeviceFactory mDeviceFactory;

    /**
     * 网关实例
     */
    ThingDevice *mGateway = nullptr;

    /**
     * 接口
     */
    DeviceManagerInterface *mInterface;
public:


    /**
     * 注册和激活网关
     * @return
     */
    int ActivateGatewayDevice();

    /**
     * 获取设备对象实例，根据产品key和设备id
     * @param product_key
     * @param device_name
     * @return
     */
    Device * GetDeviceWithProductKeyAndName(string &product_key, string device_name);

    /**
     * 获取网关实例
     * @return
     */
    Device * GetGateway();


    /**
     * 获取设备实例
     * @return
     */
    Device * GetDeviceInstance(string &deviceName);

    /**
     * 当Mqtt连接成功回调
     */
    void OnMqttConnectStateChanged(bool isConnect);

    /**
     * 删除设备id
     * @param device_eui
     */
    void DelDeviceById(string &device_eui);


    /**
     * 初始化子设备
     * @param device
     * @return
     */
    Device *GetSubDeviceInstance(DeviceParam &device);


    /**
     * 销毁设备实例
     * @param device_eui
     * @return
     */
    int DestroyDevInstance(string device_eui);


};

#endif