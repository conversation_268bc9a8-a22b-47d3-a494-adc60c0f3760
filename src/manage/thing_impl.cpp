//
// Created by seashell on 11/3/21.
//

#include <iot_mqtt.h>
#include <thing_packet.h>
#include <thing_device.h>
#include "thing_impl.h"
#include "device_manager.h"


/**
 * 发送数据接口，最终实现
 * @param topic
 * @param payload
 * @return
 */
int ThingImpl::Send(string &topic, string &payload) {
    int mid = 0;
    int ret = mIoTMqttHandler->publish(&mid, topic.c_str(), payload.c_str(), payload.length(), 1, false);
    if (ret != RET_OK)
    {
        LOG(ERROR) << "send message failed errno:" << ret;
        return RET_FAIL;
    }

    return RET_OK;
}


/**
 * MQTT订阅消息主题
 * @param mid
 * @param topic
 * @return
 */
int ThingImpl::SubscribeTopic(int &mid, string &topic)
{
    return mIoTMqttHandler->subscribe(&mid, topic.c_str());
}



/**
 * 查询设备秘钥接口，用户需保存设备的秘钥，物模型查询秘钥用于解密IOT数据，需外部实现该接口
 * @param productKey    产品的key
 * @param deviceName    设备的id
 * @return
 */
string ThingImpl::GetDevSecret(string &productKey, string deviceName) {
    LOG(ERROR) << "###productKey:" << productKey;
    LOG(ERROR) << "###deviceName:" << deviceName;
    Device *device = mDeviceManger->GetDeviceWithProductKeyAndName(productKey, deviceName);
    if (device != nullptr){
        return device->device_secret;
    }else{
        LOG(ERROR) << "device not found";
        return "";
    }

}



/**
 * 收到IOT平台的消息接口，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int ThingImpl::ReceiveIoTMessage(string &topic, Json::Value &payload) {
    mMessageDispatch->PushIotMessageToQueue(topic, payload);
    return 0;
}




/***
 * 设置IOT连接管理对象实例
 * @param handler
 */
void ThingImpl::setIotMqttHandler(IoTMqtt *handler)
{
    mIoTMqttHandler = handler;
}

/***
 * 设置设备管理对象实例
 * @param handler
 */
void ThingImpl::setDeviceManagerHandler(DeviceManager *handler)
{
    mDeviceManger = handler;
}


/***
 * 设置分发对象实例
 * @param handler
 */
void ThingImpl::setMessageDispatchHandler(MessageDispatch *handler)
{
    mMessageDispatch = handler;
}

