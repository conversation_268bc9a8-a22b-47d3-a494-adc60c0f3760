#include "device_interface.h"

#if 0
/**
 * 订阅设备主题接口，需外部实现该接口
 * @param mid       MQTT消息发送id，MQTT返回
 * @param topic     MQTT主题
 * @return
 */
int DeviceInterface::responseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value) {
    return mThingServer->responseToPlatform(device, msgId, code, topic, value);

}


/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int DeviceInterface::reportPropEvent(ThingDevice *device, Json::Value &data) {
    return mThingServer->reportPropEvent(device, data);
}


/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int DeviceInterface::reportDevEvent(ThingDevice *device, string event_name, Json::Value &data) {
    return mThingServer->reportDevEvent(device, event_name, data);
}

/**
 * 发送消息到设备
 * @param base64Data
 * @return
 */
int DeviceInterface::sendMessageToSensor(Device *device, string &base64Data) {

    device->is_register = true;
    Json::Value txData;
    txData["confirmed"] = true;
    txData["fPort"] = device->f_port;
    txData["data"] = base64Data;

    string topic = "application/";
    topic += device->app_id;
    topic += "/device/";
    topic += device->device_eui;
    topic += "/command/down";

    LOG(INFO) << "topic:" << topic;
    Json::FastWriter fWriter;
    std::string payload = fWriter.write(txData);

    int mid;
    return mNSMqttHandler->publish(&mid,topic.c_str(),payload.c_str(),payload.length());
}
#endif

