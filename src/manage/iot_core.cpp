//
// Created by seashell on 12/4/21.
//


#include "iot_core.h"
#include "gateway_dev.h"

/**
 * 初始化
 */
int IoTCore::init() {


    ///能力集实例,设置能力集接口实现
    //mThingServer = new ThingServer(this);
    ///IoT服务器的信息，包括服务器地址，服务器端口号，服务器API的url，以及软件版本
    mThingServer->setServerInfo(GatewayConfig::GetHttpServerAddress(),
                                GatewayConfig::GetHttpServerPort(),
                                GatewayConfig::GetHttpServerUrl(),
                                GatewayConfig::GetFirmwareVersion());


    ///设备管理实例,初始化网关设备，注册网关
    //mDeviceManager = new DeviceManager(this);
    if (mDeviceManager->ActivateGatewayDevice() != RET_OK ){
        LOG(ERROR) << "gateway register failed.";
        return -1;
    }


    ///连接IOT平台的mqtt实例,需要使用网关的信息生成clientId username password,并连接IOT服务器
    string productKey = mDeviceManager->GetGateway()->product_key;
    string deviceName = mDeviceManager->GetGateway()->device_name;
    string deviceSecret = mDeviceManager->GetGateway()->device_secret;
    string clientId = mThingServer->getIoTMqttClientId(productKey, deviceName);
    string userName = mThingServer->getIoTMqttUserName(productKey, deviceName);
    string password = mThingServer->getIoTMqttPassword(userName,deviceSecret);


    //mIoTMqtt = new IoTMqtt(this);
    mIoTMqtt->connect(clientId,
                    userName,
                    password,
                      GatewayConfig::GetMqttServerAddress(),
                      GatewayConfig::GetMqttPort(),
                      GatewayConfig::GetMqttKeepalive());



    ///连接内置NS平台的mqtt实例
    //mNSMqtt =  new NSMqtt(this);
    mNSMqtt->connect(NS_MQTT_USERNAME, NS_MQTT_PASSWORD);


    ///数据分发实例
    //mMessageDispatch = new MessageDispatch(this);
    return 0;
}

/**
  * 发送能力集数据到IOT平台，
  * @param topic
  * @param payload
  * @return
  */
int IoTCore::Send(string &topic, string &payload)
{
    int mid = 0;
    std::cout << "topic" << topic <<endl;
    int ret = mIoTMqtt->publish(&mid, topic.c_str(), payload.c_str(), payload.length(), 1, false);
    if (ret != RET_OK)
    {
        LOG(ERROR) << "send message failed errno:" << ret;
        return RET_FAIL;
    }

    return RET_OK;
}



/**
 * 订阅设备主题接口，提供给thing_action使用
 * @param mid       MQTT消息发送id，MQTT返回
 * @param topic     MQTT主题
 * @return
 */
int IoTCore::SubscribeTopic(int &mid, string &topic)
{
    return mIoTMqtt->subscribe(&mid, topic.c_str());
}



/**
 * 查询设备秘钥接口，用户需保存设备的秘钥，物模型查询秘钥用于解密IOT数据，需外部实现该接口
 * @param productKey    产品的key
 * @param deviceName    设备的id
 * @return
 */
string IoTCore::GetDevSecret(string &productKey, string deviceName)
{
    Device *device = mDeviceManager->GetDeviceWithProductKeyAndName(productKey, deviceName);
    if (device != nullptr){
        return device->device_secret;
    }
    return "";
}



/**
 * 收到IOT平台的消息（已经经过能力集模型处理解密后的数据），把消息压入到分发队列，准备分发给设备
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::ReceiveIoTMessage(string &topic, Json::Value &payload)
{
    return mMessageDispatch->PushIotMessageToQueue(topic, payload);
}


/**
 *
 * IOT MQTT连接成功后的回调
 */
void IoTCore::onIoTMqttConnectStateChanged(bool isConnect)
{
    mDeviceManager->OnMqttConnectStateChanged(isConnect);
}

/**
 * 收到IoT平台消息的回调
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::receiveMessageFromIoTServer(string &topic, string &payload)
{
    return mThingServer->receiveMessageFromIoTServer(topic, payload);
}


/**
 * 消息加入队列
 * @param strBuf
 * @return
 */
int IoTCore::pushSensorMessage(string &strBuf)
{
    return mMessageDispatch->PushSensorMessage(strBuf);
}

/**
 * NS连接成功回调
 * @return
 */
int IoTCore::onNSMqttConnected()
{
    LOG(INFO) << "MQTT Connect LoRa NetworkServer success";
    int mid = 1;

#if !USE_AD_SENSOR
    string sub = "application/+/device/+/event/up";
    return mNSMqtt->subscribe(&mid, sub.c_str(), 1);
#else
    string sub = "application/+/node/+/rx";
    return mNSMqtt->subscribe(&mid, sub.c_str(), 1);
#endif
}


/**
 * 通过设备的productkey和devicename获取设备实例
 * @param product_key
 * @param device_name
 * @return
 */
Device *IoTCore::GetDeviceWithProductKeyAndName(string &product_key, string device_name)
{
    return mDeviceManager->GetDeviceWithProductKeyAndName(product_key, device_name);
}

/**
 * 动态初始化一个设备,子设备，非网关设备。
 * 网关收到
 * @param deviceInfo    设备参数
 * @return
 */
Device *IoTCore::GetSubDeviceInstance(DeviceParam &deviceInfo)
{
    return mDeviceManager->GetSubDeviceInstance(deviceInfo);
}

/**
 * 获取设备实例通过deviceName。
 * 网关收到
 * @param deviceInfo    设备名称
 * @return
 */
Device *IoTCore::GetSubDeviceInstance(string &deviceName)
{
    return mDeviceManager->GetDeviceInstance(deviceName);
}


/**
* 网关设备注册
* @param gatewayDevice  网关
* @param callback       子设备
*/
int IoTCore::GatewayDevRegister(ThingDevice *gatewayDevice, string &secret)
{
    return mThingServer->GatewayDevRegister(gatewayDevice->product_key,
                                            gatewayDevice->product_secret,
                                            gatewayDevice->device_name,
                                            secret);
}

/**
 * 子设备注册到平台，自动注册
 * @param gatewayDevice 网关
 * @param subDevice     子设备
 * @return
 */
int IoTCore::DevRegister(ThingDevice *gatewayDevice, ThingDevice *subDevice, RegisterResponse &result)
{
    return mThingServer->DevRegister(gatewayDevice->product_key,
                                     gatewayDevice->device_name,
                                     gatewayDevice->device_secret,
                                     subDevice->product_key,
                                     subDevice->device_name,
                                     result);
}


/**
 *
 * 子设备绑定到网关
 * @param gatewayDevice 网关
 * @param device        子设备
 * @return
 */
int IoTCore::DevBind(ThingDevice *gatewayDevice , ThingDevice *device)
{
    return mThingServer->DevBind(gatewayDevice->product_key,
                                 gatewayDevice->device_name,
                                 gatewayDevice->device_secret,
                                 device->product_key,
                                 device->device_name);
}


/**
 * 子设备上线
 * @param gatewayDevice  网关
 * @param device         子设备
 * @return
 */
int IoTCore::DevOnline(ThingDevice *gatewayDevice , ThingDevice *device)
{
    return mThingServer->DevOnline(gatewayDevice->product_key,
                                   gatewayDevice->device_name,
                                   gatewayDevice->device_secret,
                                   device->product_key,
                                   device->device_name);
}


/**
 * 消息分发
 */
void IoTCore::dispatch() {

    mMessageDispatch->DispatchMessage();
}


/**
 * 订阅设备主题接口，需外部实现该接口
 * @param mid       MQTT消息发送id，MQTT返回
 * @param topic     MQTT主题
 * @return
 */
int IoTCore::ResponseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value)
{
    return mThingServer->ResponseToPlatform(device->device_secret, msgId, code, topic, value);

}

/**
* 计划任务
* @param deviceName
* @param period
* @param single
* @return
*/
int IoTCore::ScheduleWork(string deviceName, int period, bool single)
{
    mSch->AddScheduled(deviceName, period, single);
    return 0;
}



/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::ReportPropEvent(ThingDevice *device, Json::Value &data)
{
    return mThingServer->ReportPropEvent(device->product_key, device->device_name, device->device_secret, data);
}


/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::ReportDevEvent(ThingDevice *device, string event_name, Json::Value &data)
{
    return mThingServer->ReportDevEvent(device->product_key, device->device_name, device->device_secret, event_name,
                                        data);
}

/**
 * 发送设备版本
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::ReportDevVersion(ThingDevice *device, string &version)
{
    return mThingServer->ReportVersion(device->product_key, device->device_name, device->device_secret, version);
}

/**
 * 发送设备ota progress
 * @param topic
 * @param payload
 * @return
 */
int IoTCore::ReportDevOTAProgress(ThingDevice *device, string &step, string &desc)
{
    return mThingServer->ReportOTAProgress(device->product_key, device->device_name, device->device_secret, step, desc);
}


/**
 * 订阅主题，提供给设备管理类deviceMangager使用的接口
 * @param topicList
 * @return
 */
int IoTCore::DevSubscribeTopic(vector<string> &topicList) {

    return mThingServer->DevSubscribeTopic(topicList);
}

/**
 * 发送消息到设备
 * @param base64Data
 * @return
 */
int IoTCore::SendMessageToSensor(Device *device, string &base64Data)
{

    device->is_register = true;
    Json::Value txData;
    txData["confirmed"] = true;
    txData["fPort"] = device->f_port;
    txData["data"] = base64Data;

    string topic = "application/";
    topic += device->app_id;
    topic += "/device/";
    topic += device->device_eui;
    topic += "/command/down";

    LOG(INFO) << "topic:" << topic;
    Json::FastWriter fWriter;
    std::string payload = fWriter.write(txData);

    int mid;
    return mNSMqtt->publish(&mid,topic.c_str(),payload.c_str(),payload.length());
}



IoTCore::~IoTCore() {

    delete mThingServer;
    delete mDeviceManager;
    delete mIoTMqtt ;
    delete mNSMqtt ;
    delete mMessageDispatch;

    mThingServer = nullptr;
    mDeviceManager = nullptr;
    mIoTMqtt = nullptr ;
    mNSMqtt  = nullptr;
    mMessageDispatch = nullptr;
}

IoTCore::IoTCore() {
    mThingServer = new ThingServer(this);
    mDeviceManager = new DeviceManager(this);
    mIoTMqtt = new IoTMqtt(this);
    mNSMqtt =  new NSMqtt(this);
    mMessageDispatch = new MessageDispatch(this);
    mDeviceHelper = &DeviceHelper::getInstance();
    mDeviceHelper->SetDeviceInterface(this);
    mSch = new Schedule(mMessageDispatch);
}


