/**
 * auth：huaxiong
 * 设备管理类
 * NOTE：如果网络断开重连，重连后需要重新上线子设备和订阅主题。
 */


#include <stdexcept>
#include <thing_server.h>
#include "device_manager.h"
#include "common_func.h"
#include "json/json.h"
#include "device_factory.h"
#include "gateway_config.h"

/**
 * 析构
 */
DeviceManager::~DeviceManager() {

    delete mGateway;
    mGateway = nullptr;
}



/**
 * 实例化设备--》 订阅主题 --》（保证注册网关、认证连接先行） --》注册设备 --》绑定 --》上线---》同步、升级
 * @return
 *      gateway.device_name = "8cdKBy4T7XG421Wu";
        gateway.device_secret = "d9406mUb1G2UVU63";
        gateway.product_key = "FS4Ftn1DBFy39zS6";
 */
int DeviceManager::ActivateGatewayDevice()
{
    DeviceParam gatewayInfo;
    gatewayInfo.is_gateway = true;
    gatewayInfo.dev_type = DEV_GATEWAY;
    gatewayInfo.device_eui = GatewayConfig::GetUUID();
    gatewayInfo.device_name = gatewayInfo.device_eui;
    gatewayInfo.product_key = GatewayConfig::GetProductKeyWithDevType(to_string(DEV_GATEWAY));
    gatewayInfo.product_secret = GatewayConfig::GetProductSecretWithDevType(to_string(DEV_GATEWAY));
    gatewayInfo.company_id = "yuchen";
    gatewayInfo.app_id = "1";
    gatewayInfo.f_port = 100;
    selectDevice(gatewayInfo.device_name, gatewayInfo);

    //实例化设备类，创建设备对象
    mGateway = mDeviceFactory.CreateDevInstance(gatewayInfo);
    mDevicesMap[gatewayInfo.device_eui] = (Device *)mGateway;

    //向平台注册设备
    if (mGateway->is_register == false)
    {
        string dev_secret;
        if (mInterface->GatewayDevRegister(mGateway, dev_secret) != RET_OK){
            return RET_FAIL;
        }
        gatewayInfo.is_register = true;
        gatewayInfo.is_bind = true;
        gatewayInfo.device_secret = dev_secret;
        mGateway->is_register = gatewayInfo.is_register;
        mGateway->is_bind = gatewayInfo.is_bind;
        mGateway->device_secret = gatewayInfo.device_secret;

        addDevice(gatewayInfo);
        LOG(INFO) << "------ gateway register successfully. -----";
    }

    return 0;

}




/**
 * deviceInfo.device_name = "8cdKBy4T7XG421Wu";
   deviceInfo.device_secret = "9nkdLI23EnKl86dH";
   deviceInfo.product_key = "4FItghD46Mmn1agh";
 * 动态初始化一个设备,子设备，非网关设备。
 * 网关收到
 * @param device
 * @return
 */
Device *DeviceManager::GetSubDeviceInstance(DeviceParam &device)
{

    ///如果设备实例已经存在，直接返回实例
    Device *devInstance = mDevicesMap[device.device_eui];
    if (devInstance)
    {
        device.device_name = devInstance->device_name;
        device.device_secret = devInstance->device_secret;
        device.product_secret = devInstance->product_secret;
        device.is_register = devInstance->is_register;
        device.is_online = devInstance->is_online;
        device.is_bind = devInstance->is_bind;
        device.is_gateway = devInstance->is_gateway;
        if (devInstance->is_online == true && devInstance->is_register == true && devInstance->is_bind == true)
        {
            return devInstance;
        }
    }
    else
    {
        ///设备实例不存在，则查询数据库是否存在该设备，有则初始化
        bool isExist = selectDevice(device.device_eui, device);
        if (!isExist)
        {
            device.device_name = device.device_eui;
            device.product_key = GatewayConfig::GetProductKeyWithDevType(to_string(device.dev_type));
            device.product_secret = GatewayConfig::GetProductSecretWithDevType(to_string(device.dev_type));
        }


        /// 实例化设备类，创建设备对象
        devInstance = mDeviceFactory.CreateDevInstance(device);
        if (devInstance)
        {
            mDevicesMap[device.device_eui] = (Device *)devInstance;
        }
        else
        {
            return nullptr;
        }
    }

    ///如果未注册，则向平台注册设备
    RegisterResponse response;
    if (device.is_register == false)
    {
        if (mInterface->DevRegister(mGateway, (ThingDevice *) devInstance, response) != RET_OK)
        {
            return nullptr;
        }
        else
        {
            device.is_register = true;
            device.device_secret = response.secret;
            devInstance->is_register = device.is_register;
            devInstance->device_secret = device.device_secret;
            addDevice(device);
        }
    }

    ///如果未绑定，则向平台绑定网关
    if (device.is_bind == false)
    {
        if (mInterface->DevBind(mGateway, (ThingDevice *) devInstance) != RET_OK)
        {
            return nullptr;
        }
        else
        {
            devInstance->is_bind = true;
            device.is_bind = true;
            updateDevice(device);
        }
    }

    ///上线
    if (device.is_online == false)
    {
        ///订阅主题
        vector<string> topicVec;
        devInstance->GetTopic(topicVec);
        mInterface->DevSubscribeTopic(topicVec);

        if (mInterface->DevOnline(mGateway, (ThingDevice *) devInstance) != RET_OK)
        {
            return nullptr;
        }
        else
        {
            device.is_online = true;
            devInstance->is_online = true;
        }

    }

    return devInstance;
}




/**
 * 销毁设备实例
 * @param device_eui
 * @return
 */
int DeviceManager::DestroyDevInstance(string device_eui)
{
    LOG(INFO) << "destory devices:" << device_eui;
    ThingDevice *p = mDevicesMap[device_eui];
    delete (p);
    DelDeviceById(device_eui);
    return 0;
}

/**
 * 根据device_eui删除保存在DevMap的成员id
 * @param device_eui 需要删除的deviceID
 */
void DeviceManager::DelDeviceById(string &device_eui)
{
    mDevicesMap.erase(device_eui);
}


/**
 * 通过设备的productkey和devicename获取设备实例
 * @param product_key
 * @param device_name
 * @return
 */
Device *DeviceManager::GetDeviceWithProductKeyAndName(string &product_key, string device_name)
{

    for (const auto& item : mDevicesMap)
    {

        if (item.second != nullptr &&  item.second->product_key == product_key &&  item.second->device_name == device_name)
        {
            return item.second;
        }
    }
    return nullptr;
}



Device *DeviceManager::GetGateway() {
    return (Device *)mGateway;
}

/**
 * 获取设备实例
 * @return
 */
Device * DeviceManager::GetDeviceInstance(string &deviceName)
{
    return mDevicesMap[deviceName];
}

/**
 * 注意，根据能力集
 * MQTT连接成功后（包括重连）需要上线设备和订阅主题
 */
void DeviceManager::OnMqttConnectStateChanged(bool isConnect)
{
    if (isConnect){

#if 1
        for (const auto& item : mDevicesMap)
        {
            if (item.second == nullptr){
                continue;
            }
            ///子设备上线，网关不需要
            if (item.second->is_gateway == true) {
                ///订阅主题
                vector<string> topicVec;
                item.second->GetTopic(topicVec);
                mInterface->DevSubscribeTopic(topicVec);
            }
        }
#else
        //子设备上线,上线时自动订阅主题
        for (const auto& item : mDevicesMap)
        {
            ///订阅主题
            vector<string> topicVec;
            item.second->getTopic(topicVec);
            mInterface->devSubscribeTopic(topicVec);
            ///子设备上线，网关不需要
            if (item.second->is_gateway == false) {
                mInterface->devOnline(mGateway, item.second);
                if (mInterface->devOnline(mGateway, (ThingDevice *)item.second) == RET_OK)
                {
                    item.second->is_online = true;
                }
            }
        }
#endif
    } else{
        //子设备下线
        for (const auto& item : mDevicesMap)
        {
            if (item.second != nullptr)
            {
                item.second->is_online = false;
            }
        }
    }


}


DeviceManager::DeviceManager(DeviceManagerInterface *impl) {
    mInterface = impl;
}
