#ifndef DEVICE_MANAGER_INTERFACE_H
#define DEVICE_MANAGER_INTERFACE_H

#include <thing_server.h>

class DeviceManagerInterface {

public:
    /**
    * 网关设备注册
    * @param gatewayDevice
    * @param callback
    */
    virtual int GatewayDevRegister(ThingDevice *gatewayDevice, string &secret) = 0;

    /**
     * 子设备注册到平台，自动注册
     * @param gatewayDevice
     * @param subDevice
     * @return
     */
    virtual int DevRegister(ThingDevice *gatewayDevice, ThingDevice *subDevice, RegisterResponse &result) = 0;


    /**
     *
     * 子设备绑定到网关
     * @param gatewayDevice
     * @param device
     * @return
     */
    virtual int DevBind(ThingDevice *gatewayDevice , ThingDevice *device) = 0;


    /**
     * 子设备上线
     * @param gatewayDevice
     * @param device
     * @return
     */
    virtual int DevOnline(ThingDevice *gatewayDevice , ThingDevice *device) = 0;


    /**
     * 设备订阅主题
     * @param gatewayDevice
     * @param device
     * @return
     */
    virtual int DevSubscribeTopic(vector<string> &topicList) = 0;


    /**
     * 析构
     */
    virtual ~DeviceManagerInterface() = default;
};


#endif //DEVICE_MANAGER_INTERFACE_H
