#include <thing_method.h>
#include "message_dispatch.h"
#include "gateway_config.h"


/**
 * IoT平台消息入栈
 * @param topic
 * @param value
 * @return
 */
int MessageDispatch::PushIotMessageToQueue(string &topic, Json::Value &value) {
    ThingMessage message;
    message.payload = value;
    message.topic = topic;
    iotMsgQueue.Push(message);
    condVar.notify_all();
    return 0;
}


/**
 * 分发平台和Web下发的指令，GET/SET
 */
void MessageDispatch::DispatchIotMessage()
{

    ThingMessage message;
    for (;;)
    {
        if (iotMsgQueue.TryPop(message))
        {
            string device_name;
            string productKey;
            GetProductKeyAndDeviceNameFromTopic(message.topic, productKey, device_name);
            Device *node = mInterface->GetDeviceWithProductKeyAndName(productKey, device_name);
            if (node) {
                node->OnRxPlatformMessage(message.topic, message.payload, 0);
            } else {
                LOG(ERROR) << "no such device";
            }
        } else {
            break;
        }
    }
}



/**
 * 消息加入队列
 * @param strBuf
 * @return
 */
int MessageDispatch::PushSensorMessage(string &strBuf) {
    sensorMsgQueue.Push(strBuf);
    condVar.notify_all();
    return 0;
}


/**
 * 分发设备数据
 */
void MessageDispatch::DispatchSensorMessage()
{

    string message;
    for (;;)
    {
        if (sensorMsgQueue.TryPop(message))
        {
            DeviceParam deviceParam;
            string data;
            if (AnalyzeSensorMessage(message, data, deviceParam) != 0)
            {
                continue;
            }

            Device *device = mInterface->GetSubDeviceInstance(deviceParam);
            if (device)
            {
                if (device->use_base64_to_hex) {
                    device->OnRxSensorMessage(deviceParam.origin_data);
                } else {
                    device->OnRxSensorMessage(data);
                }
            }
            else
            {
                LOG(ERROR) << "device not exist, device_eui(devName):" << deviceParam.device_eui;
            }

        }
        else
        {
            break;
        }
    }

}

/**
 * {
 * "applicationID":"1",
 * "applicationName":"000000000000106a",
 * "deviceName":"tester",
 * "devEUI":"ffffff1000010eb3",
 * "txInfo":{"frequency":481700000,"dr":1},
 * "adr":false,
 * "fCnt":0,
 * "fPort":5,
 * "data":"AAAAAAAA//8="
 * }
 * analyze message form iot server
 * @param message
 * @param data
 * @param device
 * @return
 */
int MessageDispatch::AnalyzeSensorMessage(string &message, string &data, DeviceParam &device) {

    // LOG(ERROR) << "ns server message : "<< message;
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(message, root))
    {
        LOG(ERROR) << "dispatchSensorMessage error";
        return -1;
    }

    data = root["data"].asString();

    if (data.empty())
    {
        LOG(ERROR) << "rev null message";
        return -1;
    }



    data = Base64Decode(data);


    char buff[2048] = {0};
    memcpy(buff,data.c_str(),data.length());

    device.device_eui = root["devEUI"].asString();
    device.app_id = root["applicationID"].asString();
    device.app_name = root["applicationName"].asString();
    device.alias = root["name"].asString();
    device.f_port = root["fPort"].asInt();
    device.product_key = root["applicationName"].asString();
    device.origin_data = root["data"].asString();

#if !USE_AD_SENSOR
    device.app_eui = root["appEUI"].asString();
    device.dev_type = GatewayConfig::GetDevTypeWithProductKey(device.product_key);
#else

    device.app_eui = root["applicationName"].asString();
    if (device.app_eui == "000000000000301a")
        device.product_key = "S1zzlIerX0LpI50B";
    else if (device.app_eui == "000000000000218a")
        device.product_key = "JpmTex3xYMv1X0AT";
    else if (device.app_eui == "000000000000108b")
        device.product_key = "h6U4k1UlNNYayl5m";
    else if (device.app_eui == "000000000000222a")
        device.product_key = "ANd4cMD8vfEt8OFV";
    else if (device.app_eui == "000000000000202a")
        device.product_key = "DlT05C082blR8yVa";
    else if (device.app_eui == "000000000000207a")
        device.product_key = "4sb2vBmPJ7t7ANxH";
    else if (device.app_eui == "000000000000205a")
        device.product_key = "clFhXa9kJ0GKeZqU";
    else if (device.app_eui == "000000000000307a")
        device.product_key = "Y7M1tqeb2thQBR64";
    else if (device.app_eui == "000000000000204a")
        device.product_key = "FwPnP5iBqx9pWj6v";

    else if (device.app_eui == "000000000000206a")
        device.product_key = "PEkon319sb7V72Z8";
    else if (device.app_eui == "000000000000203a")
        device.product_key = "5vn3a7NtwJNed8CL";
    else if (device.app_eui == "000000000000303a")
        device.product_key = "RcvH0eO5vWjekXNh";
    else if (device.app_eui == "000000000000102c")
        device.product_key = "AQF2qXZPvnTtL8yA";
    else if (device.app_eui == "000000000000304c")
        device.product_key = "Fb2mYyXmPRqJbP1X";
    else if (device.app_eui == "000000000000208a")
        device.product_key = "5RczFvFljRepi3zh";

    else if (device.app_eui == "000000000000305a")
        device.product_key = "oCYnBF67x9KEjQIS";
    else if (device.app_eui == "000000000000209a")
        device.product_key = "m2CyX1ygxWSbcivH";
    else if (device.app_eui == "000000000000212a")
        device.product_key = "QrwddMUmmfycC1uU";
    else if (device.app_eui == "000000000000210a")
        device.product_key = "NbhjLj9ESs4Rsjgh";
    else if (device.app_eui == "000000000000113c")
        device.product_key = "qczb28byvObiHNyv";
    else{
        LOG(ERROR) << "Ungnized device type";
        LOG(ERROR) << "ns server message: "<< jsonTostring(root);
        return -1;
    }
    device.dev_type = GatewayConfig::getDevTypeWithProductKey(device.product_key);

#endif


#if 0
    DeviceType deviceType = DEV_UNKNOWN;
    if (device.app_name == "soil_ph")
        deviceType = DEV_SOIL_PH;
    else if (device.app_name == "radar_light")
        deviceType = DEV_RADAR_LIGHT;
    else if (device.app_name == "soil_th")
        deviceType = DEV_SOIL_TH;
    else if (device.app_name == "weather_station")
        deviceType = DEV_WEATHER_STATION;
    else if (device.app_name == "wind_speed")
        deviceType = DEV_WIND_SPEED;
    else if (device.app_name == "wind_direction")
        deviceType = DEV_WIND_DIREC;
    else if (device.app_name == "rainfall")
        deviceType = DEV_RAINFALL;
    else if (device.app_name == "manhole_cover")
        deviceType = DEV_MANHOLE_COVER;
    else if (device.app_name == "hydrant")
        deviceType = DEV_HYDRANT;
    else
        return -1;

    device.dev_type = deviceType;
#endif

    return 0;

}


/**
 * dispatch message inlucde sensor and aiot message.
 */
void MessageDispatch::DispatchMessage()
{

    try
    {
        std::unique_lock<std::mutex> lk(mutex_);
        auto now = std::chrono::system_clock::now();
        condVar.wait_until(lk,now + std::chrono::seconds (1));

        /// 分发iot下行控制命令队列
        DispatchIotMessage();

        /// 分发sensor上行消息
        DispatchSensorMessage();

        /// 执行计划任务
        DispatchScheduledMessage();
    }
    catch (exception &e)
    {
        LOG(INFO) << "\n\n\n\n\n---------------------捕捉到异常---------------------------" << endl;
        LOG(INFO) << "Error:" << e.what() << endl;
        LOG(INFO) << "---------------------捕捉到异常---------------------------\n\n\n\n\n" << endl;
    }

}


MessageDispatch::MessageDispatch(MessageDispatchInterface *impl)
{
    mInterface = impl;
}


/**
 * 定时任务消息入栈
 * @param topic
 * @param value
 * @return
 */
int MessageDispatch::PushScheduledMessageToQueue(string deviceName)
{
    scheduledQueue.Push(deviceName);
    condVar.notify_all();
    return 0;
}


/**
 * 分发定时任务消息
 */
void MessageDispatch::DispatchScheduledMessage()
{

    for (;;)
    {
        string deviceName;
        if (scheduledQueue.TryPop(deviceName))
        {
            Device *node = mInterface->GetSubDeviceInstance(deviceName);
            if (node)
            {
                node->ScheduledTasks();
            }
            else
            {
                LOG(ERROR) << "No such device, deviceName: " << deviceName;
            }
        }
        else
        {
            break;
        }
    }
}
