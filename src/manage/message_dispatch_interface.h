#ifndef GATEWAY_IOT_MESSAGE_DISPATCH_INTERFACE_H
#define GATEWAY_IOT_MESSAGE_DISPATCH_INTERFACE_H


#include <device.h>


/**
 * 消息分发的接口
 */
class MessageDispatchInterface {

public:

    /**
     * 通过设备的productkey和devicename获取设备实例
     * @param product_key
     * @param device_name
     * @return
     */
    virtual Device *GetDeviceWithProductKeyAndName(string &product_key, string device_name) = 0;

    /**
     * 动态初始化一个设备,子设备，非网关设备。
     * 网关收到
     * @param deviceInfo
     * @return
     */
    virtual Device *GetSubDeviceInstance(DeviceParam &deviceInfo) = 0;

    /**
     * 获取设备实例
     * 网关收到
     * @param deviceInfo
     * @return
     */
    virtual Device *GetSubDeviceInstance(string &deviceName) = 0;

    /**
     * 析构
     */
    virtual ~MessageDispatchInterface() = default;
};


#endif //GATEWAY_IOT_MESSAGE_DISPATCH_INTERFACE_H
