//
// Created by seashell on 11/8/21.
//
#ifndef GATEWAY_IOT_NS_MQTT_H
#define GATEWAY_IOT_NS_MQTT_H

#include "common_func.h"
#include "ns_mqtt_interface.h"

class NSMqtt {

#if 0
public:

    static NSMqtt& getInstance(){
        static NSMqtt instance;
        return instance;
    }

    NSMqtt(const NSMqtt& other) = delete;
    NSMqtt& operator=(const NSMqtt& other) = delete;

protected:
    NSMqtt() = default;
    ~NSMqtt() = default;
#else
public:
    NSMqtt(NSMqttInterface *impl);
    ~NSMqtt() = default;

#endif

private:
    string mClientId;
    string mUserName;
    string mPassword;
    struct mosquitto *m_mosq = nullptr;

    static NSMqttInterface *mInterface;
public:
    //NSMqtt() = default;
    //NSMqttfault;


    static void onConnectCallback(struct mosquitto *mosq, void *userdata, int rc);
    static void onDisconnectCallback(struct mosquitto *mosq, void *userdata, int rc);
    static void onPublishCallback(struct mosquitto *mosq, void *userdata, int mid);
    static void onSubscribeCallback(struct mosquitto *mosq, void *userdata, int mid, int qos_count, const int *granted_qos);
    static void onUnsubscribeCallback(struct mosquitto *mosq, void *userdata, int mid);
    static void printfMqttLog(struct mosquitto *mosq, void *userdata, int level, const char *str);
    static void receiveNetworkServerMessage(struct mosquitto *mosq, void *userdata, const struct mosquitto_message *message);


    /**
     * 网络初始化
     * @param clientId
     * @param userName
     * @param password
     * @return
     */
    void workThread();

    /**
     * 网络初始化
     * @param clientId
     * @param userName
     * @param password
     * @return
     */
    int connect(string userName, string password);


    /**
     * MQTT断开连接
     * @return
     */
    int disconnect(void);

    /**
     * MQTT发布主题消息
     * @param mid
     * @param topic
     * @param payload
     * @param payloadlen
     * @param qos
     * @param retain
     * @return
     */
    int publish(int *mid, const void *topic, const void *payload = NULL, int payloadlen = 0, int qos = 1,
                bool retain = false);

    /**
     * MQTT订阅主题消息
     * @param mid
     * @param sub
     * @param qos
     * @return
     */
    int subscribe(int *mid, const char *sub, int qos = 1);

    /**
     * MQTT解订主题消息
     * @param mid
     * @param sub
     * @return
     */
    int unsubscribe(int *mid, const char *sub);


};



#endif //GATEWAY_IOT_NS_MQTT_H
