//
// Created by seashell on 12/4/21.
//

#ifndef GATEWAY_IOT_IOT_MQTT_INTERFACE_H
#define GATEWAY_IOT_IOT_MQTT_INTERFACE_H
#include <string>
using namespace std;

class IoTMqttInterface {

public:
    virtual ~IoTMqttInterface() = default;

    /**
     *
     * MQTT连接成功后的回调
     */
    virtual void onIoTMqttConnectStateChanged(bool isConnect) = 0;

    /**
     * 收到IoT平台消息的回调
     * @param topic
     * @param payload
     * @return
     */
    virtual int receiveMessageFromIoTServer(string &topic, string &payload) = 0;
};


#endif //GATEWAY_IOT_IOT_MQTT_INTERFACE_H
