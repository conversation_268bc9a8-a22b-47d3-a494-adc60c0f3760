#ifndef __MQTTCLASS_H
#define __MQTTCLASS_H

#include "common_func.h"
#include "openssl/aes.h"
#include "openssl/pem.h"
#include "openssl/bio.h"
#include "openssl/evp.h"
#include <openssl/err.h>
#include <openssl/buffer.h>
#include <openssl/hmac.h>
#include <openssl/rand.h>
#include "mosquitto.h"
#include "gateway_config.h"
#include "thing_server.h"
#include "iot_mqtt_interface.h"

class IoTMqtt {

#if 0
public:

    static IoTMqtt& getInstance(){
        static IoTMqtt instance;
        return instance;
    }

    IoTMqtt(const IoTMqtt& other) = delete;
    IoTMqtt& operator=(const IoTMqtt& other) = delete;

protected:
    IoTMqtt() = default;
    ~IoTMqtt() = default;
#else
public:
    IoTMqtt(IoTMqttInterface *impl);
    ~IoTMqtt() = default;
#endif

private:
    string mClientId;
    string mUserName;
    string mPassword;
    string mAddress;
    int mPort;
    int mKeepalive;


public:
    static IoTMqttInterface *mInterface;
    ThingServer *mThingServer = nullptr;

    struct mosquitto *m_mosq = nullptr;
    static void onConnectCallback(struct mosquitto *mosq, void *userdata, int rc);
    static void onDisconnectCallback(struct mosquitto *mosq, void *userdata, int rc);
    static void onPublishCallback(struct mosquitto *mosq, void *userdata, int mid);
    static void onSubscribeCallback(struct mosquitto *mosq, void *userdata, int mid, int qos_count, const int *granted_qos);
    static void onUnsubscribeCallback(struct mosquitto *mosq, void *userdata, int mid);
    static void printfMqttLog(struct mosquitto *mosq, void *userdata, int level, const char *str);
    static void receive(struct mosquitto *mosquitto, void *userdata, const struct mosquitto_message *message);


    /**
     * 网络初始化
     * @param clientId
     * @param userName
     * @param password
     * @return
     */
    int connect(string &clientId,
                string &userName,
                string &password,
                string address,
                int port,
                int keepalive);


    /**
     * 网络初始化
     * @param clientId
     * @param userName
     * @param password
     * @return
     */
    void workThread();

    /**
     * MQTT断开连接
     * @return
     */
    int disconnect(void);



    /**
     * MQTT订阅主题消息
     * @param mid
     * @param sub
     * @param qos
     * @return
     */
    int subscribe(int *mid, const char *sub, int qos = 1);

    /**
     * MQTT解订主题消息
     * @param mid
     * @param sub
     * @return
     */
    int unsubscribe(int *mid, const char *sub);


    /**
     * MQTT发布主题消息
     * @param mid
     * @param topic
     * @param payload
     * @param payloadlen
     * @param qos
     * @param retain
     * @return
     */
    int publish(int *mid, const void *topic, const void *payload = NULL, int payloadlen = 0, int qos = 1,
                       bool retain = false);


};

#endif
