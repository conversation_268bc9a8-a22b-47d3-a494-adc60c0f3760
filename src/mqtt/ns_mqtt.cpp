//
// Created by seashell on 11/8/21.
//

#include "ns_mqtt.h"
#include <thread>
#include <device_manager.h>
#include <message_dispatch.h>
#include "mosquitto.h"


NSMqttInterface *NSMqtt::mInterface = nullptr;
/**
 * MQTT日志打印回调函数
 * @param mosq
 * @param userdata
 * @param level
 * @param str
 */
void NSMqtt::printfMqttLog(struct mosquitto *mosq, void *userdata, int level, const char *str)
{
#if 0
    LOG(INFO) << "MQTT LOG:" << str;
#endif
}



/**
 * 网络初始化
 * @param clientId
 * @param userName
 * @param password
 * @return
 */


int NSMqtt::connect(string userName, string password)
{
    mClientId = "lorawan-gateway-client";
    mUserName = userName;
    mPassword = password;
    LOG(INFO) << "NSMqtt::connect(), userName:" << userName << ",password:" << password;

    std::thread t1(&NSMqtt::workThread, this);
    t1.detach();
    return 0;
}


/*
    string clientId = "gateway_mqtt";
    string name = "wangw";
    string password = "ww1030";
 */
void NSMqtt::workThread()
{
    LOG(INFO) << "NSMqtt::workThread().";
    /* mqtt相关callback */
    m_mosq = mosquitto_new(mClientId.c_str(), false , NULL);
    mosquitto_log_callback_set(m_mosq, printfMqttLog);
    mosquitto_connect_callback_set(m_mosq, onConnectCallback);
    mosquitto_disconnect_callback_set(m_mosq, onDisconnectCallback);
    mosquitto_publish_callback_set(m_mosq, onPublishCallback);
    mosquitto_subscribe_callback_set(m_mosq, onSubscribeCallback);
    mosquitto_unsubscribe_callback_set(m_mosq, onUnsubscribeCallback);
    mosquitto_message_callback_set(m_mosq, receiveNetworkServerMessage);

    /* 设置用户名密码 */
    mosquitto_username_pw_set(m_mosq, mUserName.c_str(), mPassword.c_str());
    //mosquitto_connect(m_mosq, "localhost", 1883, 5);
#if !USE_AD_SENSOR
    mosquitto_connect(m_mosq, "*************", 1883, 50);
    // mosquitto_connect(m_mosq, "*************", 1883, 50);
#else
    mosquitto_connect(m_mosq, "**************", 1883, 50);
#endif
    mosquitto_loop_forever(m_mosq,-1,1);
    mosquitto_destroy(m_mosq);
    mosquitto_lib_cleanup();


}





/**
 * MQTT断开连接
 * @return
 */
int NSMqtt::disconnect(void)
{
    LOG(ERROR) << "disconnect ...";
    int ret = mosquitto_disconnect(m_mosq);
    if (ret == RET_OK){
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "reconnect failed errno:" << ret;
        return RET_FAIL;
    }
}

/**
 * MQTT发布主题消息
 * @param mid
 * @param topic
 * @param payload
 * @param payloadlen
 * @param qos
 * @param retain
 * @return
 */
int NSMqtt::publish(int *mid, const void *topic, const void *payload, int payloadlen, int qos, bool retain)
{
    int ret = mosquitto_publish(m_mosq, mid, (char *)topic, payloadlen, payload, qos, retain);
    if (ret == RET_OK) {
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "publish failed errno:" << ret;
        return RET_FAIL;
    }
}




/**
 * MQTT订阅主题消息
 * @param mid
 * @param sub
 * @param qos
 * @return
 */
int NSMqtt::subscribe(int *mid, const char *sub, int qos)
{
    int ret = mosquitto_subscribe(m_mosq, mid, sub, qos);
    if (ret == RET_OK)
    {
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "subscribe topic fail,errno:" << ret;
        return RET_FAIL;
    }
}



/**
 * MQTT解订主题消息
 * @param mid
 * @param sub
 * @return
 */
int NSMqtt::unsubscribe(int *mid, const char *sub)
{
    int ret;
    if (ret = mosquitto_unsubscribe(m_mosq, mid, sub), ret == RET_OK)
        return RET_OK;
    else
    {
        LOG(ERROR) << "unsubscribe failed errno:" << ret;
        return RET_FAIL;
    }
}



/**
 * MQTT网络连接回调函数
 * @param mosq
 * @param userdata
 * @param rc
 */
void NSMqtt::onConnectCallback(struct mosquitto *mosq, void *userdata, int rc)
{
    if (rc == 0)
    {
        mInterface->onNSMqttConnected();
        LOG(INFO) << "MQTT Connect Network SUCCESS,mosq: " << mosq << " rc: " << rc;
    }

}



/**
 * MQTT网络断开连接回调函数
 * @param mosq
 * @param userdata
 * @param rc
 */
void NSMqtt::onDisconnectCallback(struct mosquitto *mosq, void *userdata, int rc)
{
    LOG(ERROR) << "MQTT Disconnect Network" << rc<<endl;
}

/**
 * MQTT发布主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 */
void NSMqtt::onPublishCallback(struct mosquitto *mosq, void *userdata, int mid)
{
    LOG(INFO) << "MQTT发布主题消息回调函数 mid:" << mid<<endl;
}


/**
 * MQTT订阅主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 * @param qos_count
 * @param granted_qos
 */
void NSMqtt::onSubscribeCallback(struct mosquitto *mosq, void *userdata, int mid, int qos_count, const int *granted_qos)
{
    LOG(INFO) << "MQTT订阅主题消息回调函数";
}



/**
 * MQTT解除订阅主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 */
void NSMqtt::onUnsubscribeCallback(struct mosquitto *mosq, void *userdata, int mid)
{
    LOG(INFO) << "MQTT解除订阅主题消息回调函数";
}




/**
 * 接收MQTT消息
 * @param mosq          mosq
 * @param userdata      用户数据
 * @param message       消息
 */
void NSMqtt::receiveNetworkServerMessage(struct mosquitto *mosq, void *userdata, const struct mosquitto_message *message)
{
    if (message == NULL){
        LOG(ERROR) << "receive null message from ns";
        return;
    }
    Json::Value root;
    LOG(INFO) << "消息长度:" <<  message->payloadlen;
    LOG(INFO) << "主题是:"<< message->topic;
    LOG(INFO) << "主题是:"<< (char *)message->payload;

    string payload = (const char *) message->payload;
    mInterface->pushSensorMessage(payload);

}

NSMqtt::NSMqtt(NSMqttInterface *impl) {
    //mInterface = impl;
    mInterface = impl;
}

