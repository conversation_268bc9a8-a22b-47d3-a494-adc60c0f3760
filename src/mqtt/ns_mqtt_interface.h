//
// Created by seashell on 12/4/21.
//

#ifndef GATEWAY_IOT_NS_MQTT_INTERFACE_H
#define GATEWAY_IOT_NS_MQTT_INTERFACE_H



/**
 * NS MQTT的接口
 */
class NSMqttInterface {

public:
    /**
     * 消息加入队列
     * @param strBuf
     * @return
     */
    virtual int pushSensorMessage(string &strBuf) = 0;


    /**
     * NS连接成功回调
     * @return
     */
    virtual int onNSMqttConnected() = 0;
};


#endif //GATEWAY_IOT_NS_MQTT_INTERFACE_H
