#include "iot_mqtt.h"
#include <message_dispatch.h>
#include <thing_server.h>
#include <thread>
#include "device_manager.h"

IoTMqttInterface *IoTMqtt::mInterface = nullptr;
/**
 * MQTT日志打印回调函数
 * @param mosq
 * @param userdata
 * @param level
 * @param str
 */
void IoTMqtt::printfMqttLog(struct mosquitto *mosq, void *userdata, int level, const char *str)
{
#if 0
    LOG(INFO) << "MQTT LOG:" << str;
#endif
}


int IoTMqtt::connect(string &clientId,
                     string &userName,
                     string &password,
                     string address,
                     int port,
                     int keepalive
                            ) {

    mClientId = clientId;
    mUserName = userName;
    mPassword = password;
    mAddress = address;
    mPort = port;
    mKeepalive = keepalive;

    std::thread t1(&IoTMqtt::workThread, this);
    t1.detach();

    LOG(INFO) << "START MQTT CLIENT.";
    return 0;
}
/**
 * 网络初始化
 * @param clientId
 * @param userName
 * @param password
 * @return
 */
void IoTMqtt::workThread()
{

    /* mqtt相关callback */
    m_mosq = mosquitto_new(mClientId.c_str(), true , NULL);
    mosquitto_log_callback_set(m_mosq, printfMqttLog);
    mosquitto_connect_callback_set(m_mosq, onConnectCallback);
    mosquitto_disconnect_callback_set(m_mosq, onDisconnectCallback);
    mosquitto_publish_callback_set(m_mosq, onPublishCallback);
    mosquitto_subscribe_callback_set(m_mosq, onSubscribeCallback);
    mosquitto_unsubscribe_callback_set(m_mosq, onUnsubscribeCallback);
    mosquitto_message_callback_set(m_mosq, receive);

    /* 设置用户名密码 */
    mosquitto_username_pw_set(m_mosq, mUserName.c_str(), mPassword.c_str());

#if 0
    /* 连接 */
    ret = connectAsync(
            getMqttServerAddress().c_str(),
            getMqttPort(),
            getMqttKeepalive());

    //开启MQTT线程
    if (RET_OK == ret)
        ret = mosquitto_loop_start(m_mosq);
#else
    //int res = mosquitto_tls_set(m_mosq,"mqtt.fj-yuchen.com.crt",NULL,NULL,NULL,NULL);
    //res = mosquitto_tls_opts_set(m_mosq,0,NULL,NULL);
    LOG(INFO) << "--------------keepalive---------:" << mKeepalive <<endl;
    mosquitto_connect(m_mosq, mAddress.c_str(), mPort, mKeepalive);
    mosquitto_loop_forever(m_mosq,-1,1);
    mosquitto_destroy(m_mosq);
    mosquitto_lib_cleanup();

#endif


}



/**
 * MQTT断开连接
 * @return
 */
int IoTMqtt::disconnect(void)
{
    LOG(INFO) << "iot mqtt disconnect.";
    return mosquitto_disconnect(IoTMqtt::m_mosq);
}

/**
 * MQTT发布主题消息
 * @param mid
 * @param topic
 * @param payload
 * @param payloadlen
 * @param qos
 * @param retain
 * @return
 */
int IoTMqtt::publish(int *mid, const void *topic, const void *payload, int payloadlen, int qos, bool retain)
{
    int ret = mosquitto_publish(IoTMqtt::m_mosq, mid, (char *)topic, payloadlen, payload, qos, retain);
    if (ret == RET_OK) {
        LOG(INFO) << "MQTT发布主题消息 mid:" << *mid;
        LOG(INFO) << (char *)topic;
        LOG(INFO) << (char *)payload;
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "publish failed errno:" << ret;
        return RET_FAIL;
    }
}


/**
 * MQTT订阅主题消息
 * @param mid
 * @param sub
 * @param qos
 * @return
 */
int IoTMqtt::subscribe(int *mid, const char *sub, int qos)
{
    int ret = mosquitto_subscribe(IoTMqtt::m_mosq, mid, sub, qos);
    if (ret == RET_OK)
    {
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "subscribe topic fail,errno:" << ret;
        return RET_FAIL;
    }
}



/**
 * MQTT解订主题消息
 * @param mid
 * @param sub
 * @return
 */
int IoTMqtt::unsubscribe(int *mid, const char *sub)
{
    int ret = mosquitto_unsubscribe(IoTMqtt::m_mosq, mid, sub);
    if (ret == RET_OK)
    {
        return RET_OK;
    }
    else
    {
        LOG(ERROR) << "unsubscribe failed errno:" << ret;
        return RET_FAIL;
    }
}





/**
 * MQTT网络连接回调函数
 * @param mosq
 * @param userdata
 * @param rc
 */
void IoTMqtt::onConnectCallback(struct mosquitto *mosq, void *userdata, int rc)
{
    if (rc == 0)
    {
        LOG(INFO) << "MQTT Connect Network SUCCESS,mosq: " << mosq << " rc: " << rc;
        //通知gateway ，订阅主题与上线
        //DeviceManager::getInstance().onMqttConnected();
        mInterface->onIoTMqttConnectStateChanged(true);
    }

}



/**
 * MQTT网络断开连接回调函数
 * @param mosq
 * @param userdata
 * @param rc
 */
void IoTMqtt::onDisconnectCallback(struct mosquitto *mosq, void *userdata, int rc)
{
    LOG(ERROR) << "MQTT Disconnect Network" << rc<<endl;
    LOG(ERROR) << "mqtt errno" << errno <<endl;
    mInterface->onIoTMqttConnectStateChanged(false);
}

/**
 * MQTT发布主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 */
void IoTMqtt::onPublishCallback(struct mosquitto *mosq, void *userdata, int mid)
{
    LOG(INFO) << "MQTT发布主题消息回调函数 mid:" << mid;
}


/**
 * MQTT订阅主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 * @param qos_count
 * @param granted_qos
 */
void IoTMqtt::onSubscribeCallback(struct mosquitto *mosq, void *userdata, int mid, int qos_count, const int *granted_qos)
{
    LOG(INFO) << "MQTT订阅主题消息回调函数";
}



/**
 * MQTT解除订阅主题消息回调函数
 * @param mosq
 * @param userdata
 * @param mid
 */
void IoTMqtt::onUnsubscribeCallback(struct mosquitto *mosq, void *userdata, int mid)
{
    LOG(INFO) << "MQTT解除订阅主题消息回调函数";
}





/**
 * 接收MQTT消息
 * @param mosquitto     mosquitto
 * @param userdata      用户数据
 * @param message       消息
 */
void IoTMqtt::receive(struct mosquitto *mosquitto, void *userdata, const struct mosquitto_message *message)
{
    if (message == NULL){
        LOG(ERROR) << "receive null message from iot platform";
        return;
    }
    string topic = message->topic;
    string payload = (const char *)message->payload;

    LOG(INFO) << "Rx Topic:" << topic;
    LOG(INFO) << "Rx payload:" << payload;

    //解析能力集数据
    //ThingServer::getInstance().receiveMessageFromIoTServer(topic, payload);
    mInterface->receiveMessageFromIoTServer(topic, payload);
}

IoTMqtt::IoTMqtt(IoTMqttInterface *impl) {
    //mInterface = impl;
    mInterface = impl;
}




