#include "iot_mqtt.h"
#include "device_manager.h"
#include "glog/logging.h"
#include "iot_core.h"

void (writer)(const char* data, int size) {
    LOG(ERROR) << data;
}

/**
 * main
 */
int main(void)
{
    // 初始化数据库
    DevicePersistence::initDB();

    // 日志库初始化
    FLAGS_log_dir = "../log";
    FLAGS_colorlogtostderr = false;
    FLAGS_max_log_size = 100;

#ifdef DEBUG
    google::SetStderrLogging(google::INFO);
#else
    google::SetStderrLogging(google::INFO);
#endif

    // 初始化日志
    google::InitGoogleLogging("gateway-iot");
    // 捕捉信号
    google::InstallFailureSignalHandler();
    // 打印错误信息
    google::InstallFailureWriter(&writer);


    // 载入配置信息,配置信息包含服务器地址信息，产品key和产品秘钥等
    GatewayConfig::LoadGlobalConfig();


    IoTCore ioTCore;
    if (ioTCore.init() != 0)
    {
        exit(-1);
    }

    ///主线程,循环分发数据
    for (;;)
    {
        ioTCore.dispatch();
    }

    return 0;
}

#if 0

#include <electricity_meter_dev.h>
#include <three_phase_electricity_meter_dev.h>
#include <water_meter_dev.h>
#include <magnetism_dev.h>
#include "hydrant_dev.h"
#include "manhole_cover_dev.h"

typedef struct {
    //消息头
    MessageHeader header;

    //设备状态 0正常
    unsigned char state;

    //设备电量
    // bit7-bit5:version
    // bit4-bit0:level(0-31)
    unsigned char electricQuantity;

    //湿度
    short humidity;
    //温度
    short temperature;
    //crc
    unsigned char crc;
}__attribute__((packed))  SoilTHRxSt;



int main777()
{
    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "VSgEAQYAXwAAAAAh";
    data = "pVoQEgFQ6gQynCIAAX9OsFWq";
    data = "hAAjcQAACQABIEI=";//84
    data = "AXViAwAABAAA";
    data = "AwAB";
    data = "AwAA";
    data = "gAABAxkDBBgKHkseAQIBAAAAAAAAAAAAgQ==";
    data = "gAABAREAAAAaAAAAAAADAIE=";
    data = "gAABAREAAAAXAVQAEAAEAIE=";
    data = "ZmVmZWZlZmU2ODk5NDcyNTAwMDAwMTY4OTExODMzMzIzMzMzNDQzNDMzMzMzMzMzMzMzMzMzMzMzMzMzNDQzNDMzMzMzMzMzMzMzMzZhMTY=";
    data = Base64Decode(data);

    ThreeElectricityMeterRxHead head = {0};
    memcpy((void *)&head,data.c_str(),sizeof(ThreeElectricityMeterRxHead));


    //1 packet
    if (head.dataFlag == 858993203)
    {

        ThreeElectricityMeterRxSt13 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt13));
        float totalElectricity = bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333) * 0.01;
        int v1 = bcd_decimal_code(st.jianElectricity) - bcd_decimal_code(0x33333333);
        int v2 = bcd_decimal_code(st.fengElectricity) - bcd_decimal_code(0x33333333);
        int v3 = bcd_decimal_code(st.pingElectricity) - bcd_decimal_code(0x33333333);
        int v4 = bcd_decimal_code(st.guElectricity) - bcd_decimal_code(0x33333333);

        //Json::Value v;
        //v["totalElectricity"] = totalElectricity;
        //return DeviceHelper::getInstance().ReportPropEvent(this, v);
        std::cout << "totalElectricity:" << totalElectricity<<endl;
    }


#if 0
    ManholeCoverRxSt st = {0};
    memcpy((void *)&st, data.c_str(), sizeof(ManholeCoverRxSt));
    if (st.msgType == 0x3 && st.frameLength == 25){
        //ManholeCoverRxAckSt st = {0};
        //memcpy((void *)&st,data.c_str(),sizeof(ManholeCoverRxAckSt));
    } else if (st.frameLength == 17) {
        float temperature = st.report.temperature;
        int tiltangle = st.report.tiltangle;
        int move_state =  st.report.state & 0xf0;
        int battery_state = st.report.state & 0x0f;


        std::cout << "temperature:" << temperature<<endl;
        std::cout << "tiltangle:" << tiltangle <<endl;
        std::cout << "move_state:" << move_state <<endl;
        std::cout << "battery_state:" << battery_state <<endl;
    } else{
        LOG(ERROR) << "ERROR DATA" <<endl;
    }



    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "VSgEAQYAXwAAAAAh";
    data = "pVoQEgFQ6gQynCIAAX9OsFWq";
    data = "hAAjcQAACQABIEI=";//84
    data = "AXViAwAABAAA";
    data = "AwAB";
    data = "AwAA";
    data = Base64Decode(data);
    MagnetismRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(MagnetismRxSt));

    int electricQuantity;
    bool state;
    bool remove;
    if (st.channel == 0x01 && data.length() >= sizeof(MagnetismRxCycleSt)){

        MagnetismRxCycleSt st1 = {0};
        memcpy((void *)&st1,data.c_str(),sizeof(MagnetismRxCycleSt));
        electricQuantity = st1.state1;
        state = st1.state2;
        remove = st1.state3;

    } else if (st.channel == 0x03){
        state = st.state;
    } else if (st.channel == 0x04){
        remove = st.state;
    }


    state = st.state;

    std::cout << "ele:" << electricQuantity<<endl;
    std::cout << "state:" << state <<endl;
    std::cout << "remove:" << remove <<endl;
#endif

#if 0



    WaterMeterDevRxSt4 st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WaterMeterDevRxSt4));

    if(st.preamble == 0x84){

        int q2 = 0;
        memcpy((void *)&q2, st.totalQuantity2,3);

        int q1 = 0;
        memcpy((void *)&q1, st.totalQuantity1,2);
        float AccumulatedWaterConsumption = bcd_decimal_code(q2) + 0.0001 * bcd_decimal_code(q1);
        int state = st.st0 & 0x1e;
        std::cout << AccumulatedWaterConsumption <<std::endl;
        std::cout << state <<std::endl;
        std::cout << "Hello" <<std::endl;

    }
    else if(st.preamble == 0x81){

        WaterMeterDevRxSt1 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(WaterMeterDevRxSt1));
        float electricity = bcd_decimal_code(st.voltage2) + 0.01 * bcd_decimal_code(st.voltage1);
        std::cout << electricity <<std::endl;
        std::cout << "Hello" <<std::endl;
    }
#endif

#if 0
    map<string, Device *> mDevicesMap;
    Device *dd = mDevicesMap["cacbb80100003a28"];
    for (const auto& item : mDevicesMap) {
        LOG(ERROR) << "------1------";
        if (item.second == nullptr) {
            LOG(ERROR) << "##################################################333";
            LOG(ERROR) << "first devEUI" << item.first;
        } else {
            LOG(ERROR) << "item key:" << item.second->product_key;
            LOG(ERROR) << "item name:" << item.second->device_name;
        }
    }


    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "VSgEAQYAXwAAAAAh";
    data = "VSgEAQYAXwCDAACi";
    data = base64Decode(data);


    char buff[2048] = {0};
    memcpy(buff,data.c_str(),data.length());

    SoilTHRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SoilTHRxSt));
    int humidity = ntohs(st.humidity)/10.0;
    int temperature = ntohs(st.temperature)/10.0;
    int electricQuantity = st.electricQuantity  & 0x1f;

        std::cout << "Hello" <<std::endl;
    std::cout << "humidity" << humidity <<std::endl;
    std::cout << "temperature" << temperature<<std::endl;
    std::cout << "electricQuantity" << electricQuantity <<std::endl;

#endif

#if 0

    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "pVoQEgFQ6gQykyIAAYAPpFWq";
    data = base64Decode(data);



    SoilTHRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SoilTHRxSt));
    int humidity = ntohs(st.humidity)/10.0;
    int temperature = ntohs(st.temperature)/10.0;
    int electricQuantity = st.electricQuantity  & 0x1f;


    HydrantSt mData;
    HydrantRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(HydrantRxSt));
    int pressure = htons(st.pressure);
    std::cout << pressure *0.001 << "MPa" <<std::endl;
#endif



}




int main3()
{

    u8 hex[1024] = {0};
    u32 length = 0;
    stringToHexArray("fefefefe689947250000016891183332333344343333333333333333333344343333333333336a16",(u8 *)hex, length);

    //string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    //data = "/v7+/miZRyUAAAFokRgzMjMzRDQzMzMzMzMzMzMzRDQzMzMzMzNqFg==";
    //string data = string(reinterpret_cast<const char *>(hex));
    //data = Base64Decode(data);


    //char buff[2048] = {0};
    //memcpy(buff,data.c_str(),data.length());
    //SingleElectricityMeterSt *mData = (SingleElectricityMeterSt*)&buff;

    ThreeElectricityMeterRxHead head = {0};
    memcpy((void *)&head,hex,sizeof(ThreeElectricityMeterRxHead));


    //1 packet
    if (head.dataFlag == 858993203)
    {

        ThreeElectricityMeterRxSt13 st = {0};
        memcpy((void *)&st,hex,sizeof(ThreeElectricityMeterRxSt13));
        float totalElectricity = bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333) ;
        int v1 = bcd_decimal_code(st.jianElectricity) - bcd_decimal_code(0x33333333);
        int v2 = bcd_decimal_code(st.fengElectricity) - bcd_decimal_code(0x33333333);
        int v3 = bcd_decimal_code(st.pingElectricity) - bcd_decimal_code(0x33333333);
        int v4 = bcd_decimal_code(st.guElectricity) - bcd_decimal_code(0x33333333);

        //Json::Value v;
        //v["totalElectricity"] = totalElectricity;
        //return DeviceHelper::getInstance().ReportPropEvent(this, v);
        std::cout << "totalElectricity:" << totalElectricity<<endl;
    }



#if 0
    //kWh
    mData.totalElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.forwardElectricity = (bcd_decimal_code(st.forwardElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.reverseElectricity = (bcd_decimal_code(st.reverseElectricity) - bcd_decimal_code(0x33333333)) * 0.01;


    //V
    mData.voltage = (bcd_decimal_code(st.voltage) - bcd_decimal_code(0x3333)) * 0.1;

    //a
    int _current = 0;
    memcpy((void *)&_current, st.current,3);
    int cc  = (bcd_decimal_code(_current) - bcd_decimal_code(0x333333)) ;

    //w
    int activePower;
    memcpy((void *)&activePower, st.activePower,3);
    mData.activePower = (bcd_decimal_code(activePower)- bcd_decimal_code(0x333333)) * 0.0001;
    mData.frequency = (bcd_decimal_code(st.frequency) - bcd_decimal_code(0x3333)) * 0.01;
    mData.powerFactor = (bcd_decimal_code(st.powerFactor) - bcd_decimal_code(0x3333)) * 0.001;
    mData.state = st.state;
#endif
    std::cout << "Hello" <<std::endl;

}

int main55()
{

    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "/v7+/mhVGTQAAABokR00Q5M3NDMzMzQzMzMzMzMzlFZMMzNzMzMzgzw8M50W";
    data = "/v7+/mgUVEgAAABokR00Q5M3irZKM4q2SjMzMzMzt1WWljNqZjQzg0o8M5MW";
    data = "/v7+/mgUVEgAAABokR00Q5M3irZKM4q2SjMzMzMzt1WWljNqZjQzg0o8M5MW";
    data = Base64Decode(data);


    //char buff[2048] = {0};
    //memcpy(buff,data.c_str(),data.length());
    //SingleElectricityMeterSt *mData = (SingleElectricityMeterSt*)&buff;

    SingleElectricityMeterSt mData;
    SingleElectricityMeterRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SingleElectricityMeterRxSt));

    int dataFlag = bcd_decimal_code(st.dataFlag) - bcd_decimal_code(0x33333333);

    //kWh
    mData.totalElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.forwardElectricity = (bcd_decimal_code(st.forwardElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.reverseElectricity = (bcd_decimal_code(st.reverseElectricity) - bcd_decimal_code(0x33333333)) * 0.01;


    //V
    mData.voltage = (bcd_decimal_code(st.voltage) - bcd_decimal_code(0x3333)) * 0.1;

    //a
    int _current = 0;
    memcpy((void *)&_current, st.current,3);
    mData.current  = (bcd_decimal_code(_current) - bcd_decimal_code(0x333333))  * 0.001;

    //w
    int activePower = 0;
    memcpy((void *)&activePower, st.activePower,3);
    mData.activePower = (bcd_decimal_code(activePower)- bcd_decimal_code(0x333333)) * 0.0001;
    mData.frequency = (bcd_decimal_code(st.frequency) - bcd_decimal_code(0x3333)) * 0.01;
    mData.powerFactor = (bcd_decimal_code(st.powerFactor) - bcd_decimal_code(0x3333)) * 0.001;
    mData.state = st.state;
    std::cout << "Hello" <<std::endl;

}




/**
 * -------------------------------
 * 20240410
 * -------------------------------
 * @return
 */

typedef struct {

    //device info
    int devInfo;

    //temperature
    int temperature;

    //压力值
    float pressure;

    // report cycle
    int reportCycle;

    // detect cycle
    int detectCycle;

    // jiaozhun zhi
    int calibrationValue;

}WaterPressureSt;


int mainggg()
{

    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "/v7+/mhVGTQAAABokR00Q5M3NDMzMzQzMzMzMzMzlFZMMzNzMzMzgzw8M50W";
    data = "/v7+/mgUVEgAAABokR00Q5M3irZKM4q2SjMzMzMzt1WWljNqZjQzg0o8M5MW";
    data = "AH8HBAEsBwAAAAA=";
    data = "AH8DAw/NCDxnfmY=";
    data = Base64Decode(data);


    //char buff[2048] = {0};
    //memcpy(buff,data.c_str(),data.length());
    //SingleElectricityMeterSt *mData = (SingleElectricityMeterSt*)&buff;

    WaterPressureSt mData;

    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        int tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            memcpy((void *)&mData.devInfo, (const void *)&sensorData[i], 2);
            int vold = mData.devInfo & 0x1f;
            i += 2;
        }
            //
        else if (tag == 0x04 ){
            memcpy((void *)&mData.temperature, (const void *)&sensorData[i], 2);
            mData.temperature = ntohs(mData.temperature);
            i += 2;
        }
            //
        else if (tag == 0x07 ){

            memcpy((void *)&mData.pressure, (const void *)&sensorData[i], 4);
            mData.pressure = ntohl(mData.pressure);
            i += 4;
        }
            //
        else if (tag == 0x81 ){
            //device config
            int length = sensorData[i];
            i += 1;

            if (length == 2){
                memcpy((void *)&mData.reportCycle, (const void *)&sensorData[i], 2);
                mData.reportCycle = ntohs(mData.reportCycle);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&mData.reportCycle, (const void *)&sensorData[i], 2);
                mData.reportCycle = ntohs(mData.reportCycle);
                i += 2;
                memcpy((void *)&mData.detectCycle, (const void *)&sensorData[i], 2);
                mData.detectCycle = ntohs(mData.detectCycle);
                i += 2;
            }
            else if (length == 8){
                memcpy((void *)&mData.reportCycle, (const void *)&sensorData[i], 2);
                mData.reportCycle = ntohs(mData.reportCycle);
                i += 2;
                memcpy((void *)&mData.detectCycle, (const void *)&sensorData[i], 2);
                mData.detectCycle = ntohs(mData.detectCycle);
                i += 2;
                memcpy((void *)&mData.calibrationValue, (const void *)&sensorData[i], 4);
                mData.calibrationValue = ntohl(mData.calibrationValue);
                i += 4;
            }
        }
    }

}


typedef struct {

    //0x7e
    unsigned char head;

    //protocol version
    unsigned char version;

    //time
    unsigned int time;

    //frame number
    unsigned short number;

    //len
    unsigned short length;

    //cmd
    unsigned char cmd;

    //is encrypt
    unsigned char isEncrypt;

    //body
    unsigned char body[0];

}__attribute__((packed)) SmokeRxSt;


struct TLV {
    uint8_t type;
    uint8_t length;
    std::vector<uint8_t> value;
};

std::vector<TLV> parseTLV(const char* data, size_t length) {
    std::vector<TLV> result;

    size_t i = 0;
    while (i < length) {
        TLV tlv;
        tlv.type = data[i++];
        tlv.length = data[i++];
        for (size_t j = 0; j < tlv.length; ++j) {
            tlv.value.push_back(data[i++]);
        }
        result.push_back(tlv);
    }

    return result;
}

//yanwu
int mainYANWU()
{

    string data = "/v7+/mgXYEgAAAFokRgzMjMzMzMzMzMzMzMzMzMzMzMzMzMzMzMAFg==";
    data = "/v7+/mhVGTQAAABokR00Q5M3NDMzMzQzMzMzMzMzlFZMMzNzMzMzgzw8M50W";
    data = "/v7+/mgUVEgAAABokR00Q5M3irZKM4q2SjMzMzMzt1WWljNqZjQzg0o8M5MW";
    data = "AH8HBAEsBwAAAAA=";
    data = "fgEAAAAAABgADQEAAgEDFAIAAAsBEiQBZEC7fg==";
    data = "fgEAAAAAAAAAIwEAAwGQBQIgEwYEAAFRgAgEAAAAHgkEAAFRgBQCAGQLATIhAfYYlX4=";
    data = "fgEAAAAAAAIAEQEAAQEBAgEDFAIAAAsBEiQBZLNofg==";//moni huojing
    data = Base64Decode(data);


    //char buff[2048] = {0};
    //memcpy(buff,data.c_str(),data.length());
    //SingleElectricityMeterSt *mData = (SingleElectricityMeterSt*)&buff;

    int i = 0;
    int bodyLen = 0;
    char sensorData[256] = {0};
    char body[256] = {0};
    SmokeRxSt *st = (SmokeRxSt *)&sensorData;
    memcpy((void *)&sensorData,data.c_str(),data.length());

    bodyLen = ntohs(st->length);
    memcpy((void *)&body,st->body,bodyLen) ;

    std::vector<TLV> tlvs = parseTLV(body, bodyLen);
    for (const auto& tlv : tlvs) {

        //
        if (tlv.type == 0x14){
            int xx = (int)*tlv.value.data();
            cout << "yanwu: "<< xx << endl;

        } else if (tlv.type == 0x0B){
            int xx = (short)*tlv.value.data();
            cout << "wendu: "<< xx << endl;
        }
        else if (tlv.type == 0x19){
            int xx = (char)*tlv.value.data();
            cout << "zijian: "<< xx << endl;
        }
        else if (tlv.type == 0x24){
            int xx = (char)*tlv.value.data();
            cout << "dianliang: "<< xx << endl;
        }
        else if (tlv.type == 0x01){
            int xx = (char)*tlv.value.data();

            if (xx == 1){
                cout << "huozai baojing"<< xx << endl;
            } else if (xx == 2){
                cout << "wendu baojing "<< xx << endl;
            }

        }
        else if (tlv.type == 0x02){
            int xx = (char)*tlv.value.data();

            if (xx == 2){
                cout << "qianya baojing "<< xx << endl;
            } else if (xx == 3){
                cout << "fangchai baojing "<< xx << endl;
            } else if (xx == 7){
                cout << "tongxun baojing "<< xx << endl;
            }
        }
    }
    return 0;

}


/*int main(){

    const unsigned char tmp[] = {0x3C,  0x97, 0x28, 0x52};
    string xx = "3B0347AE";

    float * a = (float *)tmp;
    cout << "ssss :" << a <<endl;
    float x22 = hexToFloat(xx);
    cout << "sjjfjf :" << x22 <<endl;
}*/

#include <iostream>
#include <iomanip>
#include <sstream>

std::string floatToHex(float value) {
    std::stringstream stream;
    stream << std::hex << std::uppercase << std::setfill('0') << std::setw(8);
    unsigned int intValue;
    std::memcpy(&intValue, &value, sizeof(float));
    stream << intValue;
    return stream.str();
}

float hexToFloat(const std::string& hexString) {
    unsigned int intValue;
    std::stringstream stream;
    stream << std::hex << hexString;
    stream >> intValue;
    float floatValue;
    std::memcpy(&floatValue, &intValue, sizeof(float));
    return floatValue;
}



int main() {
    // 测试浮点数转换为十六进制字符串
    float floatValue = 3.14f;
    std::string hexString = floatToHex(floatValue);
    std::cout << "Float value: " << floatValue << std::endl;
    std::cout << "Hex representation: " << hexString << std::endl;

    // 测试十六进制字符串转换为浮点数
    std::string hexInput = "3C876B5C"; // 3.14 的十六进制表示
    float convertedValue = hexToFloat(hexInput);
    std::cout << "Hex string: " << hexInput << std::endl;
    std::cout << "Converted float value: " << convertedValue << std::endl;



    unsigned char tmp[] = {0x3C, 0x97, 0x28, 0x52};
    unsigned int hello;
    memcpy(&hello,tmp,sizeof(int ));
    hello = ntohl(hello);
    std::memcpy(&floatValue, &hello, sizeof(float));
    float liquidLevelHeight;
    string hexS = string( (char *)&tmp);
    std::cout << "Hex string: " << hexS << std::endl;
    return 0;
}

#endif