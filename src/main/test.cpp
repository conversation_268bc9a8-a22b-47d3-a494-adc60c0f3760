//
// Created by seashell on 6/8/22.
//

#include <cstring>
#include <iostream>
#include <netdb.h>

using namespace std;

struct T {
    unsigned int a :1;
    unsigned int b :1;
    unsigned int c :1;
}__attribute__((packed)) ;


struct ProtocolType {
    unsigned int cmd :4;
    unsigned int version :4;
}__attribute__((packed)) ;
int m1ain()
{

    short x = 0xe202;
    cout << "x:" << x <<endl;
    x = ntohs(x);

    cout << "x:" << x <<endl;

#if 0
    int sss = 0x7f & 157;
    cout << "sss :" << sss <<endl;
    T t;
    int hello = 5;
    //t = hello;
    memcpy((void*)&t,(void*)&hello,4);
    cout << "a :" << t.a <<endl;
    cout << "b :" << t.b <<endl;
    cout << "c :" << t.c <<endl;


    T *tt;
    tt = (T*)&hello;
    cout << "a :" << tt->a <<endl;
    cout << "b :" << tt->b <<endl;
    cout << "c :" << tt->c <<endl;

    cout << "size:" << sizeof(t)<<endl;


    int xx = 0x21;
    ProtocolType *type = (ProtocolType*)&xx;
    cout << "cmd :" << type->cmd <<endl;
    cout << "version :" << type->version <<endl;

    cout << "size:" << sizeof(ProtocolType)<<endl;

#endif

}