/*
 * @Description: 
 * @Author: 
 * @Create Date: 
 * @Version: 1.0
 * @LastEditTime: 2019-10-22 08:38:03
 * @LastEditors: 
 */
#include "upgrade_config.h"
#include <fstream>
#include <cerrno>
#include <json/json.h>


/**
 * 读取升级配置文件
 * @param json
 * @return
 */
int getUpgradeConfig(string filePath, Json::Value &jValue)
{
    Json::Reader reader;
    Json::Value root;    

    std::ifstream configFile(filePath.c_str());
    
    LOG(INFO) << "upgrade config file path:" << filePath;
    
    if (!configFile.is_open())
        return RET_FAIL;

    if (reader.parse(configFile, root))
    {
        jValue = root;
    }
    configFile.close();
    return RET_OK;
}
