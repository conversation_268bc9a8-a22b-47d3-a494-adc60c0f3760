//
// Created by seashell on 6/25/21.
//

#ifndef GATEWAY_IOT_UPGRADE_H
#define GATEWAY_IOT_UPGRADE_H

#include <functional>
#include "common_func.h"
#include "mtd_device.h"


#define TYPE_UBOOT "uboot"
#define TYPE_KERNEL "kernel"
#define TYPE_STM32 "stm32"
#define TYPE_SHELL "shell"



#define UPGRADE_DOWNLOAD_FINISHED 1 //文件下载完成
#define UPGRADE_COMPLETE 2          //固件更新完成


#define UPGRADE_FW_FAILED -3        //固件升级失败
#define UPGRADE_DOWNLOAD_FAILED -1  //文件下载失败
#define UPGRADE_SIGN_FAILED -2      //签名校验失败
#define UPGRADE_PROGRESSING 0       //升级中

typedef std::function<int(string,string)> progress_fun;
int doUpgrade(Json::Value jValue , progress_fun callback);

#endif //GATEWAY_IOT_UPGRADE_H
