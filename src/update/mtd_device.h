/*
 * @Description: 
 * @Author: 
 * @Create Date: 
 * @Version: 1.0
 * @LastEditTime: 2019-10-23 11:18:37
 * @LastEditors: 
 */
#ifndef __FUNC_FLASH_H_
#define __FUNC_FLASH_H_


#include "json/json.h"
#include <string>
#include <glog/logging.h>
using namespace std;
#define FLASH_PAGE_SIZE 256
#define FLASH_BLOCK_SIZE (64 * 1024)



/**
* 升级分区
* @param dev
* @param pdata
* @param size
* @return
*/
int upgradeMtdData(char *dev, unsigned char *pdata, int size);

#endif