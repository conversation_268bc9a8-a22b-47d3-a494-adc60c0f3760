#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <linux/fs.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <mtd/mtd-user.h>
#include <errno.h>
#include <sys/reboot.h>
#include "mtd_device.h"
// parameters分区
static const char *flashDevice = "/dev/mtd4";

#define RET_FAIL -1
#define RET_OK 0

/**
 * 获取mtd信息
 * @param fd
 * @param pmtdinfo
 * @return
 */
static int mtdGetInfo(int fd, struct mtd_info_user *pmtdinfo)
{
    struct mtd_info_user mtdInfo;

    int ret, regcount;

    ret = ioctl(fd, MEMGETINFO, pmtdinfo);

    if (ret < 0)
    {
        fprintf(stderr, "Error: could not get MTD device info from MTD device, [error=%d]\n", errno);
        return RET_FAIL;
    }

    return RET_OK;
}


/**
 * 打开mtd设备
 * @param dev
 * @return
 */
static int mtdOpen(char *dev)
{
    int fd = open(dev, O_RDWR);
    return fd;
}


/**
 * 关闭mtd设备
 * @param fd
 */
static void mtdClose(int fd)
{
    close(fd);
}



/**
 * 片分区擦除
 * @param dev
 * @return
 */
static int mtdChipErase(char *dev)
{
    int i, blocks;
    int fd = -1;
    int ret;
    struct mtd_info_user mtdinfo;
    struct erase_info_user eraseinfo;

    if ((fd = mtdOpen(dev)) < 0)
    {
        fprintf(stderr, "1 error while opening mtd device [error=%d]\n", errno);
        return RET_FAIL;
    }

    if ((ret = mtdGetInfo(fd, &mtdinfo)) != RET_OK)
    {
        fprintf(stderr, "error while getting mtd device info [error=%d]\n", errno);
        return RET_FAIL;
    }

    blocks = mtdinfo.size / mtdinfo.erasesize;

    eraseinfo.start = 0;
    eraseinfo.length = mtdinfo.erasesize;
    //printf("block number->%d\n", blocks);
    for (i = 1; i <= blocks; i++)
    {
        printf("chip erase.start=%x chip erase.length=%d\n", eraseinfo.start, eraseinfo.length);
        printf("chip erasing blocks: %d/%d\r\n", i, blocks);
        if (ioctl((int)fd, MEMERASE, &eraseinfo) != 0)
        {
            fprintf(stderr, "error while erasing blocks [error=%d]\n", errno);
            return RET_FAIL;
        }
        eraseinfo.start += mtdinfo.erasesize;
    }

    close(fd);

    return RET_OK;
}

/**
 * 内存擦除(只支持块擦除且为连续块,擦除地址需要块对齐)
 * @param fd
 * @param fstart
 * @param mtdinfo
 * @return
 */
static int mtdBlockErase(int fd, int fstart, struct mtd_info_user mtdinfo)
{
    struct erase_info_user erase;

    if (fstart % mtdinfo.erasesize)
    {
        printf("erase address no align to erase size\r\n");
        return RET_FAIL;
    }

    erase.start = fstart;
    erase.length = mtdinfo.erasesize;

    if (ioctl((int)fd, MEMERASE, &erase) != RET_OK)
    {
        LOG(ERROR) << "erase address no align to erase size";
        return RET_FAIL;
    }

    return RET_OK;
}

/**
 * @description: 多块擦除
 * @param: start当前擦除起始块地址(注:地址必须与块地址对齐)
 * @param: blocks 擦除块数
 * @return: 
 */
static int mtdBlocksErase(int fd, int start, int blocks, struct mtd_info_user mtdinfo)
{
    int fstart = start;
    struct erase_info_user erase;

    for (int i = 0; i < blocks; i++)
    {
        if (RET_FAIL == mtdBlockErase(fd, fstart, mtdinfo))
            return RET_FAIL;
        fstart += mtdinfo.erasesize;
    }
    return RET_OK;

    // if (start % mtdinfo.erasesize)
    // {
    //     LOG_ERROR << "erase address no align to erase size";
    //     return RET_FAIL;
    // }

    // erase.start = start;
    // erase.length = mtdinfo.erasesize;
    // for (int i = 0; i < blocks; i++)
    // {
    //     if (ioctl((int)fd, MEMERASE, &erase) != RET_OK)
    //     {
    //         LOG_ERROR << "error while erasing blocks";
    //         return RET_FAIL;
    //     }
    //     erase.start += mtdinfo.erasesize;
    // }
}


/**
 *
 * // mtd_write
// 参数: offset:   写入文件偏移量
//      SEEK_SET: 从文件头部开始偏移offset个字节
//      SEEK_CUR: 从文件当前读写的指针位置开始，增加offset个字节的偏移量
//      SEEK_END: 文件偏移量设置为文件的大小加上偏移量字节
 *
 * @param fd
 * @param offset
 * @param pdata
 * @param wlen
 * @return
 */
int mtdWriteData(int fd, int offset, unsigned char *pdata, int wlen)
{
    int handle = -1;
    int write_cnt;

    lseek(fd, offset, SEEK_SET);

    if ((write_cnt = write(fd, pdata, wlen)) != wlen)
    {
        fprintf(stderr, "write mtd device [errorcode%d] ; write_cnt:%d\n", errno, write_cnt);
        return RET_FAIL;
    }

    return RET_OK;
}


/**
 * // 参数: offset: 写入文件偏移量，与块大小对齐
//      SEEK_SET: 从文件头部开始偏移offset个字节
//      SEEK_CUR: 从文件当前读写的指针位置开始，增加offset个字节的偏移量
//      SEEK_END: 文件偏移量设置为文件的大小加上偏移量字节
 * @param fd
 * @param offset
 * @param pdata
 * @param rlen
 * @return
 */
int mtdReadData(int fd, int offset, unsigned char *pdata, int rlen)
{
    int read_cnt;

    lseek(fd, offset, SEEK_SET);

    if ((read_cnt = read(fd, pdata, rlen)) != rlen)
    {
        fprintf(stderr, "read mtd device [errorcode%d]\n", errno);
        return RET_FAIL;
    }

    //printf("read_cnt->%d\n",read_cnt);
    return RET_OK;
}


/**
 * // mtd闪存写操作接口(内部使用)
// 参数：addr: 写入地址
//      pdata: 写入的数据
//      len: 数据长度
 * @param dev
 * @param addr
 * @param pdata
 * @param len
 * @return
 */
int __mtd_write(char *dev, int addr, unsigned char *pdata, int len)
{
    int fd = -1;
    int ret;
    struct mtd_info_user mtdinfo;
    int erasesize;
    int blockedge, offset_tail;
    int blockspan, spansize = 0; //跨块
    unsigned char pCache[FLASH_BLOCK_SIZE];

    if ((fd = mtdOpen(dev)) < 0)
    {
        return RET_FAIL;
    }

    if (RET_OK != (ret = mtdGetInfo(fd, &mtdinfo)))
    {
        return RET_FAIL;
    }
    erasesize = mtdinfo.erasesize;

    blockedge = ((addr / erasesize) ? (addr / erasesize) * erasesize : 0);
    offset_tail = addr % erasesize;

    blockspan = ((addr + len) / erasesize) * erasesize;

    if (blockedge == blockspan)
    {
        if (RET_OK != (ret = mtdReadData(fd, blockedge, pCache, FLASH_BLOCK_SIZE)))
        {
            return RET_FAIL;
        }

        // 默认擦除当前地址1个块
        if (RET_OK != (ret = mtdBlockErase(fd, blockedge, mtdinfo)))
        {
            return RET_FAIL;
        }

        memcpy(&pCache[offset_tail], pdata, len);

        if (RET_OK != (ret = mtdWriteData(fd, blockedge, pCache, FLASH_BLOCK_SIZE)))
        {
            fprintf(stderr, "write mtd device [errorcode%d]\n", errno);
            mtdClose(fd);
            return RET_FAIL;
        }
    }
    else
    {
        // step 1
        if (RET_OK != (ret = mtdReadData(fd, blockedge, pCache, FLASH_BLOCK_SIZE)))
        {
            return RET_FAIL;
        }

        // 默认擦除当前地址1个块
        if (RET_OK != (ret = mtdBlockErase(fd, blockedge, mtdinfo)))
        {
            return RET_FAIL;
        }

        memcpy(&pCache[offset_tail], pdata, erasesize - offset_tail);

        // 先回写第一块数据
        if (RET_OK != (ret = mtdWriteData(fd, blockedge, pCache, FLASH_BLOCK_SIZE)))
        {
            fprintf(stderr, "write mtd device [errorcode%d]\n", errno);
            mtdClose(fd);
            return RET_FAIL;
        }

        // step 2
        // 写跨块数据
        if (RET_OK != (ret = mtdReadData(fd, blockspan, pCache, FLASH_BLOCK_SIZE)))
        {
            return RET_FAIL;
        }

        // 默认擦除当前地址1个块
        if (RET_OK != (ret = mtdBlockErase(fd, blockspan, mtdinfo)))
        {
            return RET_FAIL;
        }

        spansize = len - (erasesize - offset_tail);
        //printf("spansize->%d\n", spansize);
        memcpy(pCache, &pdata[erasesize - offset_tail], spansize);

        // 写跨块数据
        if (RET_OK != (ret = mtdWriteData(fd, blockspan, pCache, FLASH_BLOCK_SIZE)))
        {
            fprintf(stderr, "write mtd device [errorcode%d]\n", errno);
            mtdClose(fd);
            return RET_FAIL;
        }
    }

    mtdClose(fd);

    return RET_OK;
}


/**
 * // mtd闪存读取操作接口(内部使用)
    // 参数：addr: 写入地址
    //      pdata: 写入的数据
    //      len: 数据长度
 * @param dev
 * @param addr
 * @param pdata
 * @param len
 * @return
 */
int __mtd_read(char *dev, int addr, unsigned char *pdata, int len)
{
    int fd = -1;
    int ret;

    if ((fd = mtdOpen(dev)) < 0)
    {
        return RET_FAIL;
    }

    if (RET_OK != (ret = mtdReadData(fd, addr, pdata, len)))
    {
        return RET_FAIL;
    }

    mtdClose(fd);

    return RET_OK;
}


/**
* 升级分区
* @param dev
* @param pdata
* @param size
* @return
*/
int upgradeMtdData(char *dev, unsigned char *pdata, int size)
{
    int ret;
    int blocks;
    int fd = -1;
    struct mtd_info_user mtdinfo;
    struct erase_info_user eraseinfo;

    /* 打开mtd设备 */
    fd = open(dev, O_RDWR);

    if (fd < 0)
    {
        LOG(ERROR) << "open mtd device error, dev =" << dev;
        return RET_FAIL;
    }

    /* 获取mtd设备信息 */
    if (RET_OK != (ret = mtdGetInfo(fd, &mtdinfo)))
    {
        LOG(ERROR) << "mtd_getinfo error, dev =" << dev;
        close(fd);
        return ret;
    }
    LOG(INFO) << "mtd info erasesize->" << mtdinfo.erasesize;

    /* 根据升级文件大小擦除分区块 */
    if (size > mtdinfo.size)
        return RET_FAIL;

    blocks = size / mtdinfo.erasesize;
    if (size % mtdinfo.erasesize)
        blocks++;

    LOG(INFO) << "blocks:" << blocks << " size:" << size;
    //return RET_FAIL;

    /* 擦除固件 */
    if (RET_OK != (ret = mtdBlocksErase(fd, 0, blocks, mtdinfo)))
    {
        LOG(ERROR) << "mtd_blocks_erase error, dev =" << dev;
        close(fd);
        return ret;
    }

    /* 写入固件数据 */
    if (RET_OK != (ret = mtdWriteData(fd, 0, pdata, size)))
    {
        LOG(ERROR) << "write mtd device errorcode:" << errno;
        close(fd);
        return ret;
    }

    close(fd);
    return RET_OK;
}


