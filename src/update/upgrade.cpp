//
// Created by seashell on 6/25/21.
//

#include <http_method.h>
#include "upgrade.h"
#include "upgrade_config.h"


int unzip_upgrade_file(string path);
int delete_file(string path);
int upgrade();
int upgrade_firmware(string type, string path);
int upgrade_stm32_firmware(string &stmUpdateFileName);
int upgrade_system(string type, string path);
/**
 * @description: 获取平台下发最新固件程序版本
 * @param {type}
 * @return:
 */
int doUpgrade(Json::Value jValue, progress_fun reportProgress)
{
    int ret;
    string url("");
    string sign("");
    string name("");
    string dir("gateway_ota.tar.gz");

    url = jValue["params"]["url"].asString();
    sign = jValue["params"]["md5"].asString();

    //上报状态-文件正在下载之中f
    reportProgress(to_string(0), "download firmware...");
    LOG(INFO) << "download firmware...";

    /* 下载最新固件包 */
    if (RET_OK != HttpDownloadFile(url, dir.c_str()))
    {
        LOG(INFO) << "url:" << url;
        LOG(ERROR) << "http download upgrade file failed";
        reportProgress(to_string(UPGRADE_DOWNLOAD_FAILED), "Download firmware failed.");
        return UPGRADE_DOWNLOAD_FAILED;
    }

    //上报状态-文件已经下载完成
    LOG(INFO) << "upgrade file download completed";
    reportProgress(to_string(30), "Firmware download succeeded.");
    reportProgress(to_string(40), "Firmware signature verification...");
    /* 签名校验固件包 */
    string sMD5("");
    if (RET_OK != doFile_MD5(dir, sMD5))
    {
        reportProgress(to_string(UPGRADE_SIGN_FAILED), "Firmware signature verification failed.");
        return UPGRADE_SIGN_FAILED;
    }

    LOG(INFO) << "recv sign:" << "   " << sign << "   " << "calc sign:" << "   " << sMD5;
    if (sMD5 != sign)
    {
        LOG(ERROR) << "upgrade file md5 sign different";
        reportProgress(to_string(UPGRADE_SIGN_FAILED), "Firmware signature verification failed.");
        return UPGRADE_SIGN_FAILED;
    }

    reportProgress(to_string(50), "Firmware signature verification succeeded.");
    reportProgress(to_string(80), "Unzip the upgrade firmware package...");
    /* 解压固件包 */
    if (RET_OK != unzip_upgrade_file(dir))
    {
        LOG(ERROR) << "unzip upgrade file failed";
        reportProgress(to_string(UPGRADE_FW_FAILED), "Unzip the upgrade firmware package failed");
        return UPGRADE_FW_FAILED;
    }

    /* 删除升级包 */
    string otaFileName = jValue["params"]["filename"].asString();
    delete_file(otaFileName);

    /* 删除解压后的升级包 */
    //delete_file("upgrade");

    reportProgress(to_string(100), "Firmware upgrade succeeded.");
    return UPGRADE_COMPLETE;
}


/**
 * @description: 删除文件
 * @param {type} path:文件路径
 * @return:
 */
int delete_file(string path)
{
    string cmd;
    FILE *fp;
    LOG(INFO) << "delete file path:" << path;
    if (isFolderExist(path.c_str()) || isFileExist(path.c_str()))
    {
        cmd = "rm -r " + path;
        LOG(INFO) << cmd;
        fp = popen(cmd.c_str(), "r");
        if (fp == NULL)
        {
            LOG(ERROR) << "delete file path "<< path << "'failed";
            return RET_FAIL;
        }
        LOG(INFO) << "delete file path "<< path << " succeed";
    }
    else
    {
        LOG(INFO) << path << " is no exist";
    }

    usleep(200000);
    return RET_OK;
}

/**
 * @description: 解压升级文件
 * @param: path->升级压缩文件路径
 * @return:
 */
int unzip_upgrade_file(string path)
{
    string cmd;
    vector<string>result;

    //解压升级包
    cmd = "tar -zxvf " + path + " -C /app";
    LOG(INFO) << cmd;
    int res = executeSysCmd(cmd.c_str(), result);
    if (res < 0)
    {
        LOG(ERROR) << "unzip upgrade file error" << errno;
        return RET_FAIL;
    }
    return RET_OK;
}


#if  1
/**
 * @description: 升级程序
 * @param {type}
 * @return:
 */
int upgrade()
{
    int ret;
    string path;
    string dirPath = "upgrade";
    string type;
    string fileName;
    string isUpgrade;
    Json::Value jConfig;
    Json::Value jParams;


    //解压升级包
    if (RET_OK != unzip_upgrade_file("./upgrade.tar.gz"))
        return RET_FAIL;


    LOG(INFO) << "============================= OTA UPGRADE =============================";
    //从ota配置文件读取升级信息
    if (RET_OK != getUpgradeConfig(dirPath + "/" + UPGRADE_CONFIG_FILE_NAME, jConfig))
    {
        LOG(ERROR) << "read upgrade config file error";
        return RET_FAIL;
    }

    //升级操作
    jParams = jConfig["upgrade"];
    LOG(INFO) << "jParams->" << jParams << endl;
    for (int i = 0; i < jParams.size(); i++)
    {
        type = jParams[i][UPGRADE_TYPE].asString();
        fileName = jParams[i][UPGRADE_NAME].asString();
        isUpgrade = jParams[i][UPGRADE_FLAG].asString();
        path = dirPath + "/" + fileName;
        LOG(INFO) << "type:" << type << "   "<< "fileName: " << fileName << "   "<< "isUpgrade: " << isUpgrade << "   "<< "path: " << path;
        if ("1" == isUpgrade)
        {
            if (RET_OK != upgrade_firmware(type, path))
            {
                LOG(INFO) << "upgrade " << "<" << type << ">" << " failed";
                //voicePlayAudio(AUDIO_OTA_FAILED);
                return RET_FAIL;
            }
            LOG(INFO) << "upgrade " << "<" << type << ">" << " succeed";
        }
    }

    return RET_OK;
}

#else
/**
 * @description: 升级程序
 * @param {type}
 * @return:
 */
int upgrade()
{
    int ret;
    string path;
    string dirPath = "upgrade";
    string type;
    string fileName;
    string isUpgrade;
    Json::Value jConfig;
    Json::Value jParams;


    //解压升级包
    if (RET_OK != unzip_upgrade_file("./upgrade.tar.gz"))
        return RET_FAIL;


    LOG(INFO) << "============================= OTA UPGRADE =============================";
    //从ota配置文件读取升级信息
    if (RET_OK != cfg_get_upgradeConfig(dirPath + "/" + UPGRADE_CONFIG_FILE_NAME, jConfig))
    {
        LOG(ERROR) << "read upgrade config file error";
        return RET_FAIL;
    }

    //升级操作
    jParams = jConfig["upgrade"];
    LOG(INFO) << "jParams->" << jParams << endl;
    for (int i = 0; i < jParams.size(); i++)
    {
        type = jParams[i][UPGRADE_TYPE].asString();
        fileName = jParams[i][UPGRADE_NAME].asString();
        isUpgrade = jParams[i][UPGRADE_FLAG].asString();
        path = dirPath + "/" + fileName;
        LOG(INFO) << "type:" << type << "   "<< "fileName: " << fileName << "   "<< "isUpgrade: " << isUpgrade << "   "<< "path: " << path;
        if ("1" == isUpgrade)
        {
            if (RET_OK != upgrade_firmware(type, path))
            {
                LOG(INFO) << "upgrade " << "<" << type << ">" << " failed";
                //voicePlayAudio(AUDIO_OTA_FAILED);
                return RET_FAIL;
            }
            LOG(INFO) << "upgrade " << "<" << type << ">" << " succeed";
        }
    }

    return RET_OK;
}

#endif

/**
  * 升级系统固件uboot/kernel
  * @param type
  * @param path
  * @return
  */
int upgrade_system(string type, string path)
{
    FILE *fStream;
    int ret;
    int size;
    char dev[32];
    unsigned char *pdata = NULL;

    //读取升级文件信息
    if ((fStream = fopen(path.c_str(), "rb")) == NULL)
    {
        LOG(ERROR) << "open file failed when upgraded "
                   << "[" << type << "]";
        return RET_FAIL;
    }

    if (RET_OK != (ret = fseek(fStream, 0L, SEEK_END)))
    {
        LOG(ERROR) << "fseek SEEK_END error " << ret;
        fclose(fStream);
        return RET_FAIL;
    }

    if (-1 == (size = ftell(fStream)))
    {
        LOG(ERROR) << "ftell SEEK_END error" << size;
        fclose(fStream);
        return RET_FAIL;
    }

    if (RET_OK != (ret = fseek(fStream, 0L, SEEK_SET)))
    {
        LOG(ERROR) << "fseek SEEK_SET error " << ret;
        fclose(fStream);
        return RET_FAIL;
    }

    LOG(INFO) << "[" << type << "]"
              << " file size:" << size;

    if (!type.compare(TYPE_UBOOT))
    {
        sprintf(dev, "%s", "/dev/mtd0");
    }
    else if (!type.compare(TYPE_KERNEL))
    {
        sprintf(dev, "%s", "/dev/mtd1");
    }
    else
    {
        fclose(fStream);
        return RET_FAIL;
    }

    LOG(INFO) << "upgrade partition:" << dev;

    rewind(fStream);
    //根据文件大小分配内存空间
    pdata = (unsigned char *)malloc(size);
    if (NULL == pdata)
    {
        LOG(ERROR) << "malloc space failed when upgraded "
                   << "[" << type << "]";
        fclose(fStream);
        return RET_FAIL;
    }

    int realSize = fread(pdata, 1, size, fStream);
    LOG(INFO) << "realSize:" << realSize << "   "
              << "size:" << size;
    if (realSize == size)
    {
        ret = upgradeMtdData(dev, pdata, size);
    }
    else
    {
        LOG(INFO) << "read file failed when upgraded "
                  << "[" << type << "]";
        fclose(fStream);
        free(pdata);
        return RET_FAIL;
    }

    fclose(fStream);
    free(pdata);

    return ret;
}


/**
 * @description: 升级对外接口
 * @param type->升级类型(uboot/kernel/stm32/shell)
 * @param path->升级文件路径
 * @return:
 */


int upgrade_firmware(string type, string path)
{
    int ret = RET_OK;
    string s;
    string s1("failed");
    if (!isFileExist(path.c_str()))
    {
        LOG(ERROR) << "upgrade file path is no exist";
        return RET_FAIL;
    }

    //update uboot or kernel
    if ((TYPE_UBOOT == type) || (TYPE_KERNEL == type))
    {
        ret = upgrade_system(type, path);
    }
    else if (TYPE_STM32 == type)
    {
        ret = upgrade_stm32_firmware(path);
    }
    else if (TYPE_SHELL == type)
    {
        //执行shell脚本，完成对动态库、静态库、app等文件升级
        s = doShell(path);
        if (RET_OK != stoi(s))
        {
            LOG(ERROR) << "doshell result:" << s;
            ret = RET_FAIL;
        }
    }
    else
    {
        LOG(ERROR) << "type: " << type << " no exist";
        ret = RET_FAIL;
    }

    return ret;
}

#define SINGLE_UPDATE_PACK_SIZE 100
int upgrade_stm32_firmware(string &stmUpdateFileName)
{

    //读取文件，分割下发，收到一包应答后继续发下一包。
    //reqUpdateStm32();

#if 0 //zhx 2021520
    int fd;
    fd = open(stmUpdateFileName.c_str(), O_RDONLY);
    if (fd < 0)
    {
        LOG(ERROR) << "update the stm32 firmware failed, open file error";
        return RET_FAIL;
    }

    struct stat state;
    fstat(fd, &state);
    int fileSize = state.st_size;
    LOG(INFO) << "update firmware file size:" << fileSize << endl;

    /* 总包数 */
    int countPack = ceil((float)fileSize / SINGLE_UPDATE_PACK_SIZE);
    LOG(INFO) << "count pack:" << countPack << endl;
    for (int i = 1; i <= countPack; i++)
    {

        char buf[SINGLE_UPDATE_PACK_SIZE] = {0};
        int count = read(fd, buf, SINGLE_UPDATE_PACK_SIZE);
        if (count < 0)
        {
            LOG(ERROR) << "update the stm32 firmware failed, read file error";
            close(fd);
            return RET_FAIL;
        }

        for (int j = 0; j < 3; ++j)
        {
            int ret = reqUpdateStm32(countPack, i, count, buf);
            if (ret == 0)
            {
                break;
            }

            if (ret != 0 && j == 2)
            {
                LOG(ERROR) << "update the stm32 firmware failed, stm32 operation file error";
                close(fd);
                return RET_FAIL;
            }
        }
    }
    close(fd);

#endif
    return RET_OK;
}
