//
// Created by seashell on 6/6/22.
//

#include "infrared_dev.h"





/***** 主题 *****/
#define TOPIC_INFO_EVENT_POST_RES         "thing.event.body_alarm.post_res"
#define TOPIC_LOW_VOLTAGE_EVENT_POST_RES  "thing.event.low_voltage.post_res"


/***** 能力集属性 *****/

#define VOLTAGE             "battery_voltage"
#define INFRARED_STATE      "infrared_state"
#define ANTI_THEFT_STATE    "anti_theft_state"



/***** 事件 *****/
#define EVENT_BODY_ALARM            "body_alarm"            //检测到人体报警
#define EVENT_LOW_VOLTAGE           "low_voltage"           //低电压报警
#define EVENT_ANTI_THEFT_ALARM      "remove"                //防拆报警



InfraredDev::InfraredDev(DeviceParam &device) : Device(device)
{
}




InfraredDev::~InfraredDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool InfraredDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性获取
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性设置
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool InfraredDev::OnRxSensorMessage(string &data)
{
    InfraredRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(InfraredRxSt));
    mData.infrared_state = st.infrared_state;
    mData.anti_theft_state = st.anti_theft_state;
    mData.battery_voltage = st.battery_voltage/10.0;

    PostProperty();

    if(mData.infrared_state == 1){
        Json::Value v;
        v[INFRARED_STATE] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_BODY_ALARM, v);
    }

    if (mData.anti_theft_state == 1){
        Json::Value v;
        v[ANTI_THEFT_STATE] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_ANTI_THEFT_ALARM, v);
    }


    if (mData.battery_voltage <= 3.0){
        Json::Value v;
        v[VOLTAGE] = mData.battery_voltage;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_LOW_VOLTAGE, v);
    }

    return true;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int InfraredDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int InfraredDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == INFRARED_STATE){
            res[INFRARED_STATE] = mData.infrared_state;
        } else if (i.asString() == VOLTAGE){
            res[VOLTAGE] = mData.battery_voltage;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int InfraredDev::PostProperty()
{
    Json::Value v;
    v[INFRARED_STATE] = mData.infrared_state;
    v[ANTI_THEFT_STATE] = mData.anti_theft_state;
    v[VOLTAGE] = mData.battery_voltage;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
