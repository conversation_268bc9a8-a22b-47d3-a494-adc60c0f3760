//
// Created by seashell on 6/30/21.
//

#include "soil_ph_dev.h"
#include <iostream>
#include "device_interface.h"



/***** 能力集属性 *****/
#define PH    "ph"
#define VOLTAGE  "battery_voltage"

/***** 事件 *****/
#define EVENT_SOIL_PH_HIGH "soil_ph_high_event"
#define EVENT_SOIL_PH_LOW "soil_ph_low_event"
#define EVENT_SOIL_PH_LOW_VOLTAGE "soil_ph_low_voltage_event"



SoilPHDev::SoilPHDev(DeviceParam &device) : Device(device)
{
}

SoilPHDev::~SoilPHDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SoilPHDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SoilPHDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int SoilPHDev::UnpackSensorData(string &data)
{
    SoilPHRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SoilPHRxSt));
    mData.ph = ntohs(st.ph)/100.0;
    mData.electricQuantity = 31;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int SoilPHDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SoilPHDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        } else if (i.asString() == "ph"){
            res["ph"] = mData.ph;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SoilPHDev::PostProperty()
{
    Json::Value v;
    v["electricQuantity"] = mData.electricQuantity;
    v["ph"] = mData.ph;
    v["state"] = mData.state;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

/**
 * 电池低压事件上报
 * @return
 */
int SoilPHDev::PostLowPowerEvent()
{
    Json::Value v;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportDevEvent(this, EVENT_SOIL_PH_LOW_VOLTAGE, v);

}


/**
 * 上报酸碱度过低事件
 * @return
 */
int SoilPHDev::PostPhLowEvent()
{
    Json::Value v;
    v["ph"] = mData.ph;
    return DeviceHelper::getInstance().ReportDevEvent(this, EVENT_SOIL_PH_LOW, v);
}


/**
 * 上报酸碱度过高事件
 * @return
 */
int SoilPHDev::PostPhHighEvent()
{
    Json::Value v;
    v["ph"] = mData.ph;
    return DeviceHelper::getInstance().ReportDevEvent(this, EVENT_SOIL_PH_HIGH, v);
}
