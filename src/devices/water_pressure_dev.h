/**
 * 水压传感器
 */

#ifndef GATEWAY_IOT_WATER_PRESSURE_DEV_H
#define GATEWAY_IOT_WATER_PRESSURE_DEV_H

/**
 * 水压传感器
 */

/**
 * 水质酸碱度传感器
 */
#include "device.h"


typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //最高3bit为版本，低5bit为MCU电压等级作为电量指示
    unsigned char electricQuantity;

    //水压值 单位Pa
    unsigned int pressure;

    //crc
    unsigned char crc;

}__attribute__((packed))  WaterPressureRxSt;




typedef struct {

    //temperature
    int temperature;

    //压力值
    float pressure;

    //电量状态
    int electricQuantity;

    //状态
    int state;
}WaterPressureSt;


/**
 * 水压传感器
 */
class WaterPressureDev :Device{
public:
    WaterPressureDev(DeviceParam &device);
    ~WaterPressureDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);
private:

    //属性
    WaterPressureSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_WATER_PRESSURE_DEV_H
