#include "manhole_cover_dev.h"
#include <device_interface.h>

ManholeCoverDev::ManholeCoverDev(DeviceParam &device) : Device(device)
{
    // this->product_key = device.product_key;
    // this->product_secret = device.device_secret;
    // this->device_name = device.device_name;
    // this->device_secret = device.device_secret;
    // this->is_gateway = device.is_gateway;
    // this->is_bind = device.is_bind;
    // this->is_register = device.is_register;
    // this->is_online = device.is_online;

    // this->f_port = device.f_port;
    // this->app_id = device.app_id;
    // this->dev_type = device.dev_type;
    // this->device_eui = device.device_eui;

    // this->origin_data = device.origin_data;
    this->use_base64_to_hex = true;
}

ManholeCoverDev::~ManholeCoverDev()
{
}

/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool ManholeCoverDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) // 属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) // 属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
               DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}

/**
 * 设备上报
 * @param jsData
 * @return
 */
bool ManholeCoverDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    if (mData.move_state != 0)
    {
        PostAlarmEvent();
    }
    return true;
}

/**
 * 解析设备数据
 * @param data
 * @return
 */
int ManholeCoverDev::UnpackSensorData(string &data)
{
    LOG(INFO) << "data=" << data;
    std::vector<unsigned char> chars = base64_decode(data);
    std::vector<ManholeCoverDevTLV> tlvs = parse_tlv(chars);

    // ManholeCoverRxSt st = {0};
    // memcpy((void *)&st, data.c_str(), sizeof(ManholeCoverRxSt));

    char alarm_type = get_alarm_type(tlvs);
    float angle = get_angle_value(tlvs);
    mData.temperature = 0;
    mData.battery_state = 0;
    mData.move_state = 0;
    mData.tiltangle = 0;
    switch (alarm_type)
    {
    case 0x0d:
        mData.battery_state = 1;
        break;

    case 0x08:
        mData.tiltangle = angle;
        mData.move_state = 1;
        break;

    default:
        break;
    }
    return 0;
}

/**
 * 设置属性
 * @param jValue
 * @return
 */
int ManholeCoverDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int ManholeCoverDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
    }
    return RET_OK;
}

/**
 * 属性上报
 * @return
 */
int ManholeCoverDev::PostProperty()
{
    Json::Value v;
    v["battery_state"] = mData.battery_state;
    v["temperature"] = mData.temperature;
    v["tiltangle"] = mData.tiltangle;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

/**
 * 井盖异常报警
 * @return
 */
int ManholeCoverDev::PostAlarmEvent()
{
    Json::Value v;
    v["state"] = mData.move_state;
    DeviceHelper::getInstance().ReportDevEvent(this, "move_event", v);
    return RET_OK;
}

vector<unsigned char> ManholeCoverDev::base64_decode(const string &in)
{
    const string base64_chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz"
        "0123456789+/";

    vector<unsigned char> out;
    int i = 0, j = 0, in_len = in.size();
    unsigned char char_array_4[4], char_array_3[3];

    while (in_len-- && (in[i] != '='))
    {
        char_array_4[j++] = in[i++];
        if (j == 4)
        {
            for (int a = 0; a < 4; a++)
                char_array_4[a] = base64_chars.find(char_array_4[a]);

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (int b = 0; b < 3; b++)
                out.push_back(char_array_3[b]);
            j = 0;
        }
    }

    if (j)
    {
        for (int k = j; k < 4; k++)
            char_array_4[k] = 0;

        for (int k = 0; k < 4; k++)
            char_array_4[k] = base64_chars.find(char_array_4[k]);

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (int k = 0; k < j - 1; k++)
            out.push_back(char_array_3[k]);
    }

    return out;
}

string ManholeCoverDev::bytes_to_hex(const vector<unsigned char> &bytes)
{
    static const char hex_chars[] = "0123456789abcdef";
    string hex;
    hex.reserve(bytes.size() * 2);

    for (unsigned char byte : bytes)
    {
        hex.push_back(hex_chars[(byte & 0xF0) >> 4]);
        hex.push_back(hex_chars[byte & 0x0F]);
    }

    return hex;
}

vector<ManholeCoverDevTLV> ManholeCoverDev::parse_tlv(const vector<unsigned char> &data)
{
    vector<ManholeCoverDevTLV> result;
    size_t pos = 0;

    while (pos < data.size())
    {
        ManholeCoverDevTLV tlv;

        // 解析TAG
        uint8_t first_byte = data[pos++];
        tlv.tag.push_back(first_byte);

        // 处理多字节TAG (如果第5位为1，表示多字节TAG)
        // if ((first_byte & 0x1F) == 0x1F)
        // {
        while (pos < data.size())
        {
            uint8_t next_byte = data[pos++];
            tlv.tag.push_back(next_byte);
            // 如果最高位为0，表示TAG结束
            if ((next_byte & 0x80) == 0)
            {
                break;
            }
        }
        // }

        // 解析Length
        if (pos >= data.size())
        {
            throw runtime_error("Invalid TLV data: missing length");
        }

        uint8_t length_byte = data[pos++];
        if (length_byte < 0x80)
        {
            // 短格式
            tlv.length = length_byte;
        }
        else
        {
            // 长格式
            uint8_t length_of_length = length_byte & 0x7F;
            tlv.length = 0;

            for (uint8_t i = 0; i < length_of_length; ++i)
            {
                if (pos >= data.size())
                {
                    throw runtime_error("Invalid TLV data: incomplete length");
                }
                tlv.length = (tlv.length << 8) | data[pos++];
            }
        }

        // 解析Value
        if (pos + tlv.length > data.size())
        {
            throw runtime_error("Invalid TLV data: value exceeds data size");
        }

        tlv.value.assign(data.begin() + pos, data.begin() + pos + tlv.length);
        pos += tlv.length;

        result.push_back(tlv);
    }

    return result;
}

char ManholeCoverDev::get_alarm_type(const vector<ManholeCoverDevTLV> &tlvs)
{
    for (auto tlv : tlvs)
    {
        if (tlv.tag[0] == 0x20)
        {
            return tlv.value[0];
        }
    }
    return 0x00;
}

float ManholeCoverDev::get_angle_value(const vector<ManholeCoverDevTLV> &tlvs)
{
    for (auto tlv : tlvs)
    {
        if (tlv.tag[0] == 0x18)
        {
            return bytes_to_float(tlv.value);
        }
    }
    return (float)0.0;
}

float ManholeCoverDev::bytes_to_float(const vector<unsigned char> &bytes)
{
    uint64_t result = 0;

    for (int i = bytes.size() - 1; i >= 0; --i)
    {
        result = (result << 8) | bytes[i];
    }

    return ((float)result) / 100;
}