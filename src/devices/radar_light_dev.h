/**
 * 雷达感应照明灯
 */

#ifndef GATEWAY_IOT_RADAR_LIGHT_DEV_H
#define GATEWAY_IOT_RADAR_LIGHT_DEV_H

#include <cstdlib>
#include "device.h"



/**
 * 主动上报亮度
 * cmd 0x1
 */
typedef struct {
    //消息头
    MessageHeader header;
    //亮度
    unsigned char brightness;
    //crc
    unsigned char crc;
}__attribute__((packed))  RadarLightRxSt;





/**
 * 0x33 设置最低亮度
 * 0x34 设置最高亮度
 * 0x35设置自动、手动控制模式
 * 0x36 设置调光速度值
 * 0x37 设置上报周期
 * 0x51 恢复出厂设置
 */
typedef struct {
    //消息头
    MessageHeader header;
    //值
    unsigned char value;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetLightSt;



typedef struct {
    //亮度
    unsigned char brightness;

    //最小亮度
    unsigned char minBrightness;

    //最大亮度
    unsigned char maxBrightness;

    //自动或者手动模式
    unsigned char mode;

    //调光速度值
    unsigned char speed;

    //上报周期
    unsigned char period;

    //查询手动模式 控制微波雷达灯的亮度
    unsigned char manualBrightness;

    //查询软件版本
    unsigned char softVersion;

    //查询硬件版本
    unsigned char firmwareVersion;
}RadarLightSt;



/**
 * 雷达感应照明灯
 */
class RadarLightDev : public Device
{
public:
    RadarLightDev(DeviceParam &device);
    ~RadarLightDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    RadarLightSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    // 解析设备数据
    int UnpackSensorData(string &data);

    // 计划任务
    virtual int ScheduledTasks();

    //set control mode 0:auto 1:manual
    int setCtrlMode(int mode);

    //control brightness
    int controlBrightness(int brightness);

};



#endif //GATEWAY_IOT_RADAR_LIGHT_DEV_H
