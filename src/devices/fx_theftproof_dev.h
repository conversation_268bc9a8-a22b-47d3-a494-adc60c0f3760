#ifndef GATEWAY_IOT_FX_THEFTPROOF_DEV_H
#define GATEWAY_IOT_FX_THEFTPROOF_DEV_H
#include <iostream>
#include <cstring>
#include "device.h"


typedef struct {
    //消息头
    MessageHeader header;

    //设备状态 0正常
    unsigned char state;

    //设备电量
    // bit7-bit5:version
    // bit4-bit0:level(0-31)
    unsigned char electricQuantity;

    //按键状态 0 丢失，1 没有丢失
    unsigned char key_state;
    //crc
    unsigned char crc;
}__attribute__((packed))  FxRxSt;


typedef struct {
    //设备状态 0正常
    unsigned char state;

    //丢失状态 0 未丢失，1 丢失
    unsigned char lose;
} FxSt;


class FXTheftproofDev : public Device
{
public:
    FXTheftproofDev(DeviceParam &device);
    ~FXTheftproofDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    FxSt mData;
    //设置属性
    int SetProperty(Json::Value &req);
    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);
    //属性上报
    int PostProperty();
    //解析设备协议
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_FX_THEFTPROOF_DEV_H
