//
// Created by seashell on 6/6/22.
//

#include "sos_dev.h"
/***** 主题 *****/



/***** 能力集属性 *****/

#define STATE                       "state"
#define VOLTAGE                     "battery_voltage"
#define ANTI_THEFT_STATE            "anti_theft_state"

/***** 事件 *****/
#define EVENT_ALARM                 "sos"                 //检测报警
#define EVENT_LOW_VOLTAGE           "low_voltage"           //低电压报警
#define EVENT_ANTI_THEFT_ALARM      "remove"                //防拆报警


SOSDev::SOSDev(DeviceParam &device) : Device(device)
{
}




SOSDev::~SOSDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SOSDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SOSDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    if(mData.alarmType == 1 || mData.alarmType == 2){
        Json::Value v;
        v["sos_state"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_ALARM, v);
    }

    if (mData.anti_theft_state == 1){
        Json::Value v;
        v[ANTI_THEFT_STATE] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_ANTI_THEFT_ALARM, v);
    }

    if (mData.voltage_state == 1){
        Json::Value v;
        v[VOLTAGE] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, EVENT_LOW_VOLTAGE, v);
    }
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int SOSDev::UnpackSensorData(string &data)
{
    SOSRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SOSRxSt));
    mData.anti_theft_state = st.anti_theft_state;
    mData.alarmType = st.alarmType;
    mData.voltage = (st.voltage_info & 0x7f)/10.0;
    mData.voltage_state = st.voltage_info & 0x80;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int SOSDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SOSDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == STATE){
            res[STATE] = mData.alarmType;
        }
        else if (i.asString() == ANTI_THEFT_STATE){
            res[ANTI_THEFT_STATE] = mData.anti_theft_state;
        }else if (i.asString() == VOLTAGE){
            res[VOLTAGE] = mData.voltage;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SOSDev::PostProperty()
{
    Json::Value v;
    v[ANTI_THEFT_STATE] = mData.anti_theft_state;
    v[STATE] = mData.alarmType;
    v[VOLTAGE] = mData.voltage;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
