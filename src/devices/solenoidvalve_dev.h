/**
 * 电磁阀控制器
 */

#ifndef GATEWAY_IOT_SOLENOIDVALVE_DEV_H
#define GATEWAY_IOT_SOLENOIDVALVE_DEV_H
#include "device.h"


/**电磁阀
 *
 */
typedef struct {


    //设备状态 0正常
    unsigned char state;

    // 3 bit version
    unsigned int version;

    // 设备电量
    // 5 bit Voltage Level
    unsigned int electricQuantity;

    //阀门状态
    unsigned char valveState;

    //功率
    unsigned int power;

    //crc
    unsigned char crc;
}__attribute__((packed))  ValveRxSt;


/**
 * 电磁阀
 */
typedef struct {
    //new
    float voltage;

    //new
    float current;

    //功率
    float power;

    //设备状态
    unsigned char state;

}ValveSt;

//control command
typedef struct {

    //command
    unsigned char cmd;

    //1-7 bit id
    //0 bit state
    unsigned char state;


    //unsigned short crc;

}__attribute__((packed))  ValveControlSt;



typedef struct {


    //被应答的命令
    unsigned char res_cmd;

    //设备状态 0close 1open
    unsigned char state;

}__attribute__((packed))  ValveControlResSt;


/**
 * 电磁阀控制器
 */
class SolenoidValveDev : public Device
{
public:
    SolenoidValveDev(DeviceParam &device);
    ~SolenoidValveDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    ValveSt mData;

    string lastQuestTopic;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);

    //open
    int Open();

    //close
    int Close();
};



#endif //GATEWAY_IOT_SOLENOIDVALVE_DEV_H
