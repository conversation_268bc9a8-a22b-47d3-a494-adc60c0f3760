/**
 * 设备辅助类，提供与平台交互的接口
 *
 */
#ifndef GATEWAY_IOT_DEVICE_HELPER_H
#define GATEWAY_IOT_DEVICE_HELPER_H
#include "device_interface.h"

class DeviceHelper {

public:
    static DeviceHelper& getInstance(){
        static DeviceHelper instance;
        return instance;
    }

    DeviceHelper(const DeviceHelper& other) = delete;
    DeviceHelper& operator=(const DeviceHelper& other) = delete;

protected:
    DeviceHelper() = default;
    ~DeviceHelper() = default;

public:


    /**
     * 发送消息到物理设备
     * @param base64Data
     * @return
     */
    int SendMessageToSensor(Device *device, string &base64Data);


    /**
     * 上报设备事件
     * @param topic
     * @param payload
     * @return
     */
    int ReportDevEvent(ThingDevice *device, string event_name, Json::Value &data);



    /**
     * 上报设备属性
     * @param topic     主题
     * @param payload   载荷
     * @return
     */
    int ReportPropEvent(ThingDevice *device, Json::Value &data);


    /**
     * 上报设备版本
     * @param topic     主题
     * @param payload   载荷
     * @return
     */
    int ReportDevVersion(ThingDevice *device, string &version);


    /**
     * 发送设备数据，需外部实现该接口
     * @param topic     主题
     * @param payload   载荷
     * @return
     */
    int ReportOTAProgress(ThingDevice *device, string &step, string &desc);

    /**
     * 订阅设备主题接口，需外部实现该接口
     * @param mid       MQTT消息发送id，MQTT返回
     * @param topic     MQTT主题
     * @return
     */
    int ResponseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value);


    /**
     * 设置设备接口实例
     * @param impl
     */
    void SetDeviceInterface(DeviceInterface *impl);


    /**
     * 计划任务
     * @param deviceName
     * @param period
     * @param single 是否一次性
     */
    void ScheduleWork(string deviceName, int period, bool single);
private:
    DeviceInterface *mInterface;
};


#endif //GATEWAY_IOT_DEVICE_HELPER_H
