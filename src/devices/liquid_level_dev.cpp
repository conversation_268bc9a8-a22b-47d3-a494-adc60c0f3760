//
// Created by seashell on 6/6/22.
//

#include "liquid_level_dev.h"

/***** 能力集属性 *****/
#define LIQUIDLEVELHEIGHT    "liquidLevelHeight"
#define ELECTRICQUANTITY    "electricQuantity"
#define ADCVALUE    "adcValue"
/***** 事件 *****/
#define TOPIC_E_LEVEL_ALARM "liquid_alarm"
#define TOPIC_E_LOW_POWER   "low_voltage"


LiquidLevelDev::LiquidLevelDev(DeviceParam &device) : Device(device)
{
}




LiquidLevelDev::~LiquidLevelDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool LiquidLevelDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool LiquidLevelDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int LiquidLevelDev::UnpackSensorData(string &data)
{

    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        int tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            short devInfo = 0;
            memcpy((void *)&devInfo, (const void *)&sensorData[i], 2);
            mData.electricQuantity  = devInfo & 0x1f;
            i += 2;
        }
        //
        else if (tag == 0x03 ){
            memcpy((void *)&mData.adcValue, (const void *)&sensorData[i], 2);
            mData.adcValue = ntohs(mData.adcValue);
            i += 2;
        }
        else if (tag == 0x08 ){

            unsigned int tmpValue;
            memcpy((void *)&tmpValue, (const void *)&sensorData[i], 4);
            tmpValue = ntohl(tmpValue);
            std::memcpy(&mData.liquidLevelHeight,&tmpValue,sizeof(float ));
            i += 4;
        }
        //
        else if (tag == 0x81 ){
            //device config
            int length = sensorData[i];
            i += 1;

            int reportCycle = 0;
            int detectCycle = 0;
            int calibrationValue = 0;
            if (length == 2){

                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
            }
            else if (length == 8){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
                memcpy((void *)&calibrationValue, (const void *)&sensorData[i], 4);
                calibrationValue = ntohl(calibrationValue);
                i += 4;
            }
        }
    }
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int LiquidLevelDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int LiquidLevelDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == LIQUIDLEVELHEIGHT){
            res[LIQUIDLEVELHEIGHT] = mData.liquidLevelHeight;
        } else if (key == ELECTRICQUANTITY){
            res[ELECTRICQUANTITY] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int LiquidLevelDev::PostProperty()
{
    Json::Value v;
    v[LIQUIDLEVELHEIGHT] = mData.liquidLevelHeight;
    v[ELECTRICQUANTITY] = mData.electricQuantity;
    v[ADCVALUE] = mData.adcValue;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
