//
// Created by seashell on 6/30/21.
//

#include "weather_dev.h"
#include <iostream>
#include <device_interface.h>





/***** 主题 *****/



/***** 能力集属性 *****/
#define HUMIDITY "humidity"
#define TEMPERATURE "temperature"
#define PM25 "pm25"
#define PM10 "pm10"
#define NOISE "noise"
#define LIGHT "light"
#define WIND_SPEED "windSpeed"
#define WIND_DIRECTION "windDirection"




/***** 事件 *****/




WeatherDev::WeatherDev(DeviceParam &device) : Device(device)
{

}


WeatherDev::~WeatherDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WeatherDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WeatherDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据 - 新气象站协议
 * 数据格式: Modbus RTU响应 (地址+功能码+长度+30字节数据+CRC)
 * @param data
 * @return
 */
int WeatherDev::UnpackSensorData(string &data)
{
    // 检查数据长度，至少需要3字节(地址+功能码+长度)
    if (data.length() < 3) {
        LOG(WARNING) << "Weather data too short, length: " << data.length();
        return -1;
    }

    // 提取数据长度字段(第3个字节)
    unsigned char dataLength = (unsigned char)data[2];

    // 如果数据长度为16，则忽略解析和上报
    if (dataLength == 16) {
        LOG(INFO) << "Weather data length is 16, ignoring parse and report";
        return -1; // 返回错误码，阻止后续上报
    }

    WeatherRxSt set = {0};
    memcpy((void *)&set, data.c_str(), sizeof(WeatherRxSt));

    LOG(INFO) << "Weather data length: " << (int)dataLength << ", processing...";

    // 按照新协议解析数据 (大端模式，需要转换)
    mData.humidity = ntohs(set.humidity) * 0.1;        // 寄存器0: 湿度 * 0.1 %RH
    mData.temperature = ntohs(set.temperature) * 0.1;  // 寄存器1: 温度 * 0.1 °C
    mData.pm25 = ntohs(set.pm25);                      // 寄存器4: PM2.5 * 1 ug/m³
    mData.light = ntohs(set.light);                    // 寄存器6: 光照度 * 1 Lux
    mData.noise = ntohs(set.noise) * 0.1;              // 寄存器7: 噪声 * 0.1 dB
    mData.pm10 = ntohs(set.pm10);                      // 寄存器10: PM10 * 1 ug/m³
    mData.windSpeed = ntohs(set.windSpeed) * 0.1;    // 寄存器13: 风速 * 0.1 m/s
    mData.windDirection = ntohs(set.windDirection);   // 寄存器14: 风向 * 1°

    mData.state = 0; // 设备正常状态

    LOG(INFO) << "Weather data parsed - Humidity:" << mData.humidity
              << "%, Temperature:" << mData.temperature
              << "°C, PM2.5:" << mData.pm25
              << "ug/m³, PM10:" << mData.pm10
              << "ug/m³, Light:" << mData.light
              << "Lux, Noise:" << mData.noise
              << "dB, Wind Speed:" << mData.windSpeed
              << "m/s, Wind Direction:" << mData.windDirection << "°";

    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int WeatherDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WeatherDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == HUMIDITY){
            res[HUMIDITY] = mData.humidity;
        } else if (i.asString() == TEMPERATURE){
            res[TEMPERATURE] = mData.temperature;
        } else if (i.asString() == PM10){
            res[PM10] = mData.pm10;
        } else if (i.asString() == PM25){
            res[PM25] = mData.pm25;
        } else if (i.asString() == NOISE){
            res[NOISE] = mData.noise;
        } else if (i.asString() == LIGHT){
            res[LIGHT] = mData.light;
        } else if (i.asString() == WIND_SPEED){
            res[WIND_SPEED] = mData.windSpeed;
        } else if (i.asString() == WIND_DIRECTION){
            res[WIND_DIRECTION] = mData.windDirection;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报 - 新气象站所有参数
 * @return
 */
int WeatherDev::PostProperty()
{
    Json::Value v;
    v[HUMIDITY] = mData.humidity;           // 湿度 %RH
    v[TEMPERATURE] = mData.temperature;     // 温度 °C
    v[PM25] = mData.pm25;                  // PM2.5 ug/m³
    v[PM10] = mData.pm10;                  // PM10 ug/m³
    v[NOISE] = mData.noise;                // 噪声 dB
    v[LIGHT] = mData.light;                // 光照度 Lux
    v[WIND_SPEED] = mData.windSpeed;      // 风速 m/s
    v[WIND_DIRECTION] = mData.windDirection; // 风向 °

    LOG(INFO) << "Reporting weather properties: " << v;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

/**
 * 电池低压事件上报
 * @return
 */
int WeatherDev::PostLowPowerEvent()
{
    return RET_OK;
}

