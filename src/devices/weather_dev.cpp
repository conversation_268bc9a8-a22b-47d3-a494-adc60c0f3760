//
// Created by seashell on 6/30/21.
//

#include "weather_dev.h"
#include <iostream>
#include <device_interface.h>





/***** 主题 *****/



/***** 能力集属性 *****/
#define HUMIDITY "humidity"
#define TEMPERATURE "temperature"
#define PM25 "pm25"
#define PM10 "pm10"
#define NOISE "noise"




/***** 事件 *****/




WeatherDev::WeatherDev(DeviceParam &device) : Device(device)
{

}


WeatherDev::~WeatherDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WeatherDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WeatherDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int WeatherDev::UnpackSensorData(string &data)
{
    WeatherRxSt set = {0};
    memcpy((void *)&set, data.c_str(), sizeof(WeatherRxSt));
    mData.humidity = ntohs(set.humidity) / 10.0;
    mData.temperature = ntohs(set.temperature) / 10.0;
    mData.pm25 = ntohs(set.pm25);
    mData.pm10 = ntohs(set.pm10);
    mData.noise = ntohs(set.noise) / 10.0;
    mData.electricQuantity = set.electricQuantity & 0x1f;
    //TODO
    //mData.state = set.state;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int WeatherDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WeatherDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == HUMIDITY){
            res[HUMIDITY] = mData.humidity;
        } else if (i.asString() == TEMPERATURE){
            res[TEMPERATURE] = mData.temperature;
        } else if (i.asString() == PM10){
            res[PM10] = mData.pm10;
        } else if (i.asString() == PM25){
            res[PM25] = mData.pm25;
        } else if (i.asString() == NOISE){
            res[NOISE] = mData.noise;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WeatherDev::PostProperty()
{
    Json::Value v;
    v[HUMIDITY] = mData.humidity;
    v[TEMPERATURE] = mData.temperature;
    v[PM10] = mData.pm10;
    v[PM25] = mData.pm25;
    v[NOISE] = mData.noise;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

/**
 * 电池低压事件上报
 * @return
 */
int WeatherDev::PostLowPowerEvent()
{
    return RET_OK;
}

