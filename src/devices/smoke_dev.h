/**
 * 烟雾传感器
 */
#ifndef GATEWAY_IOT_SMOKE_DEV_H
#define GATEWAY_IOT_SMOKE_DEV_H


#include "device.h"


typedef struct {

    //0x7e
    unsigned char head;

    //protocol version
    unsigned char version;

    //time
    unsigned int time;

    //frame number
    unsigned short number;

    //len
    unsigned short length;

    //cmd
    unsigned char cmd;

    //is encrypt
    unsigned char isEncrypt;

    //body
    unsigned char body[0];

}__attribute__((packed)) SmokeRxSt;


typedef struct {
    //
    int smokeConcentration;
    //
    int electricQuantity;
    //
    int temperature;
}SmokeSt;

struct TLV {
    uint8_t type;
    uint8_t length;
    std::vector<uint8_t> value;
};


/**
 * 烟雾传感器
 */
class SmokeDev :Device{
public:
    SmokeDev(DeviceParam &device);
    ~SmokeDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);
    std::vector<TLV> parseTLV(const char* data, size_t length);

private:

    //属性
    SmokeSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_SMOKE_DEV_H
