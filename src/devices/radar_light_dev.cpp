//
// Created by seashell on 9/1/21.
//

#include <thing_tool.h>
#include "radar_light_dev.h"
#include "device_interface.h"
/***** 主题 *****/


/***** 能力集属性 *****/
#define BRIGHTNESS    "brightness"
#define MAX_BRIGHTNESS "max_brightness"
#define MIN_BRIGHTNESS "min_brightness"



/***** 事件 *****/


RadarLightDev::RadarLightDev(DeviceParam &device) : Device(device)
{
}




RadarLightDev::~RadarLightDev()
{
}


/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool RadarLightDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    std::cout <<"Radar"<< jsonTostring(jValue) <<std::endl;

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool RadarLightDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int RadarLightDev::UnpackSensorData(string &data)
{
    RadarLightRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(RadarLightRxSt));
    mData.brightness = st.brightness;

    LOG(INFO) << "--------------------brightness --------------------" ;
    LOG(INFO) << mData.brightness ;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int RadarLightDev::SetProperty(Json::Value &req)
{
    std::cout << jsonTostring(req) <<std::endl;
    for (const auto key : req.getMemberNames())
    {
        if (key == BRIGHTNESS)
        {
            mData.brightness = req[BRIGHTNESS].asInt();
            controlBrightness(mData.brightness);
        }
        else
        {
            LOG(ERROR) << "property not exist,name:" << key <<endl;
            return RET_FAIL;
        }
    }
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int RadarLightDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &key : req.getMemberNames())
    {
        if (key == BRIGHTNESS){
            res[BRIGHTNESS] = mData.brightness;
        } else if (key == MAX_BRIGHTNESS){
            res[MAX_BRIGHTNESS] = mData.maxBrightness;
        }else if (key == MIN_BRIGHTNESS){
            res[MIN_BRIGHTNESS] = mData.minBrightness;
        }
        else{
            LOG(ERROR) << "property not exist,name:" << key <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int RadarLightDev::PostProperty()
{
    Json::Value v;
    v[BRIGHTNESS] = mData.brightness;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

int firstTime = true;
int num = 0;
int RadarLightDev::ScheduledTasks() {

    LOG(INFO) << "--------------------LIGHT ScheduledTasks--------------------";
    if (firstTime){
        firstTime = false;
        //setCtrlMode(1);
    }

    int tmp = num % 4;
    if (tmp == 0){
        //setCtrlMode(0);
        controlBrightness(30);
    } else if (tmp == 1){
        //setCtrlMode(1);
        controlBrightness(40);
    }else if (tmp == 2){
        //setCtrlMode(0);
        controlBrightness(50);
    }else if (tmp == 3){
        //setCtrlMode(1);
        controlBrightness(80);
    }
    else {
        //setCtrlMode(0);
        controlBrightness(50);
    }

    num++;
    return 0;
}


/**
 * 设置灯的控制模式
 * @param data
 * @return
 */
int RadarLightDev::setCtrlMode(int mode) {

    SetLightSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x20;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.value = mode;
    set.crc = calXor((u8 *) &set, sizeof(SetLightSt) - 1);
    string data = Base64Encode((char *) &set, sizeof(SetLightSt));
    DeviceHelper::getInstance().SendMessageToSensor(this, data);
    return 0;
}

/**
 * 控制灯的亮度
 * @param data
 * @return
 */
int RadarLightDev::controlBrightness(int brightness) {

    SetLightSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x20;
    set.header.cmd = 0x65;
    set.header.length = 0x1;
    set.value = brightness;
    set.crc = calXor((u8 *) &set, sizeof(SetLightSt) - 1);
    string data = Base64Encode((char *) &set, sizeof(SetLightSt));
    DeviceHelper::getInstance().SendMessageToSensor(this, data);
    return 0;
}