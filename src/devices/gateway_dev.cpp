#include <sys/reboot.h>
#include <functional>
#include <system_info.h>
#include <thing_method.h>
#include "gateway_dev.h"
#include "common_func.h"
#include "mtd_device.h"
#include "upgrade.h"
#include "device_interface.h"
#include "gateway_config.h"
using namespace std::placeholders;


#define VOLTAGE         "voltage"
#define TEMPERATURE     "temperature"
#define HUMIDITY        "humidity"
#define DISK_SIZE       "disk_size"
#define DISK_USAGE      "disk_usage"
#define MEM_SIZE        "mem_size"
#define MEM_USAGE       "mem_usage"
#define CPU_USAGE       "cpu_usage"
#define VERSION         "version"
#define IP              "ip"




GatewayDev::GatewayDev(DeviceParam &deviceInfo) : Device(deviceInfo)
{

    /*物模型相关的主题*/
    string prefixThing = "$thing/";
    //m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_PRO_SET);
    //m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_RES_PRO_POST);
    /*service*/
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_TRANSPARENT_SERVICE);


    /*注册相关的主题*/
    string prefixGateway = "$gateway/";
    m_topic.emplace_back(prefixGateway + product_key + "/" + device_name + TOPIC_RES_REG_AUTO);
    m_topic.emplace_back(prefixGateway + product_key + "/" + device_name + TOPIC_RES_BIND);
    m_topic.emplace_back(prefixGateway + product_key + "/" + device_name + TOPIC_RES_CONNECT);
    m_topic.emplace_back(prefixGateway + product_key + "/" + device_name + TOPIC_RES_DELETE);


    /*ota*/
    string prefixOTA = "$ota/update/firmware/";
    m_topic.emplace_back(prefixOTA  + product_key + "/" + device_name);

    //上线success
    mNeedCheckOTA = true;
    DeviceHelper::getInstance().ScheduleWork(device_name, 30, false);

}



GatewayDev::~GatewayDev(void)
{

}


/**
 * 事件上报
 * @param jsData
 * @return
 */
bool GatewayDev::OnRxSensorMessage(string &data)
{
    return true;
}

/**
 * 设备消息执行体函数
 * @param strTopic
 * @param jValue
 * @param isLocalReq
 * @return
 */
bool GatewayDev::OnRxPlatformMessage(string topic, Json::Value jValue, bool isLocalReq)
{
    int result = 0;
    string subDeviceId;

    message_id = jValue[J_ID].asString();
    Json::Value res = Json::nullValue;
    Json::Value req = jValue[J_PARAMS];

    //std::cout << jsonTostring(jValue) <<std::endl;

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET))
    {
        result = SetProperty(req);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_RES_REG_AUTO))
    {

        LOG(INFO) << "receiveMessageFromIoTServer devices register message";
        /* 收到注册子设备的应答 */
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_RES_BIND))
    {
        LOG(INFO) << "receiveMessageFromIoTServer devices bind message";
        /* 收到绑定子设备的应答 */
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_RES_CONNECT))
    {

        LOG(INFO) << "receiveMessageFromIoTServer devices online message";
        /* 收到上线子设备的应答 */
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_TRANSPARENT_SERVICE))
    {
        //upgrade(jValue);
        result = TransparentCommand(jValue, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_OTA_UPDATE_FIRMWARE))
    {
        Upgrade(jValue);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_RES_DELETE))
    {
        LOG(INFO) << "receiveMessageFromIoTServer topic:" << TOPIC_RES_DELETE;
    }
    else if (IS_TOPIC_MESSAGE(topic,"/event/property/post_res"))
    {

    }
    else
    {
        LOG(ERROR) << "Not support topic " << topic << " action";
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, topic.append("_res"), res);
    }


    return false;
}

/**
 * 设置属性
 * @param req
 * @return
 */
int GatewayDev::SetProperty(Json::Value &req)
{
    std::cout << jsonTostring(req) <<std::endl;
    for (const auto key : req.getMemberNames())
    {
        if (key == "high_temperature_threshold")
        {
            mData.high_temperature_threshold = req["high_temperature_threshold"].asFloat();
        }
        else
        {
            LOG(ERROR) << "property not exist,name:" << key <<endl;
            return RET_FAIL;
        }
    }
    return RET_OK;

}





int GatewayDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == VOLTAGE)
        {
            res[VOLTAGE] = mData.voltage;
        }
        else if (i.asString() == TEMPERATURE)
        {
            res[TEMPERATURE] = mData.temperature;
        }
        else
        {
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



int GatewayDev::PostProperty()
{
    Json::Value v;
    //v[VOLTAGE] = mData.voltage;
    v[TEMPERATURE] = 25.0;
    v[HUMIDITY] = mData.humidity;
    v[DISK_SIZE] = mData.disk_size;
    v[DISK_USAGE] = mData.disk_usage;
    v[MEM_SIZE] = mData.mem_size;
    v[MEM_USAGE] = mData.mem_usage;
    v[CPU_USAGE] = mData.cpu_usage;
    v[VERSION] = mData.version;
    v[IP] = mData.ip;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}



/**
 * @description: 
 * @param {type} 
 * @return: 
 */
int GatewayDev::ScheduledTasks()
{
    GetBoardTemperatureHumidity(mData.temperature, mData.humidity);

    mData.voltage = (float )getPoeVoltage();

    get_cpu_usage(mData.cpu_usage);

    get_memory_usage(mData.mem_size,mData.mem_usage);

    get_hard_drive_usage(mData.disk_size,mData.disk_usage);

    get_hostname_ip(mData.ip);

    string version = GatewayConfig::GetFirmwareVersion();
    if (version.length() < VERSION_LENGTH){
        memcpy(mData.version, version.c_str(),version.length());
    }

#if 0
    printf("temperature:%.2f\n",mData.temperature);
    printf("humidity:%.2f%%\n",mData.humidity);
    printf("voltage:%.2f%%\n",mData.voltage);
    printf("Mem size:%.2fG, usage:%.2f%%\n",mData.mem_size,mData.mem_usage);
    printf("CPU usage:%.2f%%\n",mData.cpu_usage);
    printf("Mem size:%.2fG, usage:%.2f%%\n",mData.mem_size,mData.mem_usage);
    printf("Disk size:%.2fG, usage:%.2f%%\n",mData.disk_size,mData.disk_usage);
    printf("ip:%s\n",mData.ip);
    printf("version:%s\n",GatewayConfig::getFirmwareVersion().c_str());
#endif

    PostProperty();

    return 0;
}



/**
 * 状态变更事件上报
 * @return
 */
int GatewayDev::PostPowerOffEvent(string e)
{
    Json::Value v;
    v["poe_voltage"] = 0;
    v["dc_voltage"] = 0;
    return DeviceHelper::getInstance().ReportDevEvent(this, e, v);
}



/**
 * {
  "command":"sshpass -p \"123456\" ssh -o ServerAliveInterval=15 -qTfNn -R '[::]:2222:localhost:22' gw@***************"
}
 * 透传指令
 * @param command
 * @return
 */
int GatewayDev::TransparentCommand(Json::Value value, Json::Value &resJson)
{
    string id = value["id"].asString();
    string cmd;
    if (value["params"].isMember("command"))
    {
        cmd = value["params"]["command"].asString();
    }

    if (cmd.empty()){
        return 1;
    }

    string result;
    int res = systemX(cmd,result, false);
    resJson["message"] = result;

    if (res == 0)
    {
        resJson["result"] = 0;
    }
    else
    {
        resJson["result"] = 1;
    }

    return res;
}



int GatewayDev::Upgrade(Json::Value ota)
{
    LOG(INFO) << "===============================收到平台升级响应===============================";
    std::cout << jsonTostring(ota) << std::endl;
    /* 处理平台下发最新固件程序版本 */
    progress_fun fun=std::bind(&GatewayDev::ReportUpgradeProgress, this, _1, _2);
    int ret = doUpgrade(ota, fun);
    if (UPGRADE_COMPLETE != ret)
    {
        return false;
    }

    string newVersion = ota["params"]["version"].asString();
    LOG(INFO) << "new version:" << newVersion;
    ReportDeviceVersion(newVersion);
    /* 重启设备 */
    LOG(INFO) << "reboot devices";
    sleep(10);
    reboot(RB_AUTOBOOT);
    return true;
}


/**
 * 设备上报固件版本号
 * @param version
 * @return
 */
int GatewayDev::ReportDeviceVersion(string version)
{
    return DeviceHelper::getInstance().ReportDevVersion(this, version);
}


/**
 * @description: 设备上报升级状态
 * @param: progress　升级进度
 * @param: desc 进度描述信息
 * @return: 
 */
int GatewayDev::ReportUpgradeProgress(string status, string desc)
{
    return DeviceHelper::getInstance().ReportOTAProgress(this, status, desc);
}






