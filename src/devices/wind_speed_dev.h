/**
 * 风速传感器
 */

#ifndef GATEWAY_IOT_WIND_SPEED_DEV_H
#define GATEWAY_IOT_WIND_SPEED_DEV_H
#include "device.h"


typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //风向
    unsigned short speed;

    //crc
    unsigned char crc;
}__attribute__((packed))  WindSpeedRxSt;


/**
 * smoke property
 */
typedef struct {
    //电池电压，上报的数据除以10
    int electricQuantity;
    //风向
    float speed;
    //状态
    int state;
}WindSpeedSt;



/**
 * 风速传感器
 */
class WindSpeedDev : public Device
{
public:
    WindSpeedDev(DeviceParam &device);
    ~WindSpeedDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    WindSpeedSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_WIND_SPEED_DEV_H
