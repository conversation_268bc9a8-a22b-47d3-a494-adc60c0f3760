/**
 * 风向传感器
 */

#ifndef GATEWAY_IOT_WIND_DIRECTION_DEV_H
#define GATEWAY_IOT_WIND_DIRECTION_DEV_H



#include "device.h"


typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //风向
    unsigned short direction;
    //crc
    unsigned char crc;
}__attribute__((packed))  WindDirectionRxSt;

/**
 * smoke property
 */
typedef struct {
    //电池电压，上报的数据除以10
    int  electricQuantity;

    //风向
    unsigned short direction;

    //传感器状态
    int state;

}WindDirectionSt;


/**
 * 风向传感器
 */
class WindDirectionDev : public Device
{
public:
    WindDirectionDev(DeviceParam &device);
    ~WindDirectionDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    WindDirectionSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_WIND_DIRECTION_DEV_H
