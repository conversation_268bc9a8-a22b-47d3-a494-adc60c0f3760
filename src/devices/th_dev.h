/**
 * 温湿度传感器
 */

#ifndef GATEWAY_IOT_TH_DEV_H
#define GATEWAY_IOT_TH_DEV_H

#include "device.h"


/*typedef struct {

    //传感器类型 0x01
    unsigned char type;

    //帧类型 0x00心跳帧 0x01数据帧
    unsigned char frameType;

    //0未拆 1被拆
    unsigned char anti_theft_state;

    //当前温度状态 00正常 01高温告警 02低温过低
    unsigned char temperature_state;

    //当前温度值的10倍
    short temperature;

    //当前湿度状态 00正常 01高湿告警 02低湿过低
    unsigned char humidity_state;

    //当前湿度值的10倍
    unsigned short humidity;

    //当前电压值的10倍 最高位0表示电压正常 1表示低电压
    unsigned short voltage_info;

}__attribute__((packed)) THRxSt;*/


typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //humidity
    unsigned short humidity;

    //temperature
    unsigned short temperature;

    //crc
    unsigned char crc;
}__attribute__((packed))  THRxSt;


typedef struct {
    //温度
    int temperature;

    //湿度
    int humidity;

    //0未拆 1被拆
    int anti_theft_state;

    //电压
    float battery_voltage;

    //
    int electricQuantity;

}THSt;


/**
 * 温湿度传感器
 */
class THDev : Device{
public:
    THDev(DeviceParam &device);
    ~THDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    THSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_TH_DEV_H
