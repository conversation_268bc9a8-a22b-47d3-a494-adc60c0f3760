/**
 * 单灯控制器
 */

#ifndef GATEWAY_IOT_LAMP_SWITCH_DEV_H
#define GATEWAY_IOT_LAMP_SWITCH_DEV_H

#include "device.h"



struct ProtocolType {
    unsigned int cmd :4;
    unsigned int version :4;
}__attribute__((packed)) ;


struct LAMPState {
    unsigned int voltage_state :1;
    unsigned int current_state :1;
    unsigned int sensor_state :1;
}__attribute__((packed)) ;

typedef struct {
    //传感器类型
    unsigned char type;

    //0x21 协议类型bit0-3 01心跳 bit4-7协议版本
    unsigned char frameType;

    //控制码 0x9e
    unsigned char controlCode;

    //数据域长度0x12
    unsigned char dataLen;

    //命令码0x12
    unsigned char cmd;

    //电压 单位0.1V
    unsigned short voltage;

    //电流 mA
    unsigned int current;

    //功率 单位0.1w
    unsigned int power;

    //电量 0.01kwh
    unsigned int electrical_voltage;

    //开关状态 0关灯 1开灯
    unsigned char switch_state;

    //亮度 0-100
    unsigned char brightness;

    //设备状态
    //bit0: 电压状态 0：正常 1：过压
    //bit1:电流状态 0：正常 1：过流
    //bit2:电量传感器状态 0：正常 1：异常
    //unsigned char state;
    LAMPState device_state;

}__attribute__((packed))  LAMPRxSt;


typedef struct {
    //电压 单位0.1V
    float voltage;

    //电流 mA
    float current;

    //功率 单位0.1w
    float power;

    //电量 0.01kwh
    float electrical_voltage;

    //开关状态 0关灯 1开灯
    unsigned int switch_state;

    //亮度 0-100
    unsigned int brightness;

    //电压状态
    unsigned int voltage_state;

    //电流状态
    unsigned int current_state;

    //电量传感器状态
    unsigned int sensor_state;

} LAMPSt;


/**
 * 单灯控制器
 */
class LAMPSwitchDev : Device {
public:
    LAMPSwitchDev(DeviceParam &device);
    ~LAMPSwitchDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    LAMPSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_LAMP_SWITCH_DEV_H
