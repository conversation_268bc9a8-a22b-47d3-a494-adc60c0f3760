//
// Created by seashell on 7/11/22.
//

#include "water_meter_dev.h"

WaterMeterDev::WaterMeterDev(DeviceParam &device) : Device(device)
{
}



WaterMeterDev::~WaterMeterDev()
{

}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WaterMeterDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WaterMeterDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int WaterMeterDev::UnpackSensorData(string &data)
{

    WaterMeterDevRxSt4 st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WaterMeterDevRxSt4));

    if(st.preamble == 0x84){

        int q2 = 0;
        memcpy((void *)&q2, st.totalQuantity2,3);

        int q1 = 0;
        memcpy((void *)&q1, st.totalQuantity1,2);
        mData.AccumulatedWaterConsumption = bcd_decimal_code(q2) + 0.0001 * bcd_decimal_code(q1);
        mData.state = st.st0 & 0x1e;

    }
    else if(st.preamble == 0x81){

        WaterMeterDevRxSt1 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(WaterMeterDevRxSt1));
        mData.voltage = bcd_decimal_code(st.voltage2) + 0.01 * bcd_decimal_code(st.voltage1);

    }

    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int WaterMeterDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WaterMeterDev::GetProperty(Json::Value &req, Json::Value &res)
{

    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == "AccumulatedWaterConsumption"){
            res["AccumulatedWaterConsumption"] = mData.AccumulatedWaterConsumption;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WaterMeterDev::PostProperty()
{
    Json::Value v;
    v["AccumulatedWaterConsumption"] = mData.AccumulatedWaterConsumption;
    v["state"] = mData.state;
    v["voltage"] = mData.voltage;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
