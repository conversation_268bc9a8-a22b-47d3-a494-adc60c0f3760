/**
 * 水质传感器
 */

#ifndef GATEWAY_IOT_WATER_PH_DEV_H
#define GATEWAY_IOT_WATER_PH_DEV_H

/**
 * 水质酸碱度传感器
 */
#include "device.h"



typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //设备电量
    // bit7-bit5:version
    // bit4-bit0:level(0-31)
    unsigned char electricQuantity;

    //ph值除以10
    unsigned short ph;

    //crc
    unsigned char crc;

}__attribute__((packed))  WaterPhRxSt;





typedef struct {
    float ph;
    float electricQuantity;
}WaterPhSt;


/**
 * 水质传感器
 */
class WaterPhDev :Device{
public:
    WaterPhDev(DeviceParam &device);
    ~WaterPhDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);
private:

    //属性
    WaterPhSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_WATER_PH_DEV_H
