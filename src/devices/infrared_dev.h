/**
 * 红外传感器
 */
#ifndef GATEWAY_IOT_INFRARED_DEV_H
#define GATEWAY_IOT_INFRARED_DEV_H


#include "device.h"


typedef struct {
    //传感器类型
    unsigned char type;
    //帧类型 0x1心跳 0x2事件上报
    unsigned char frameType;
    //红外状态 0未触发 1触发
    unsigned char infrared_state;
    //是否被拆除 0未拆除 1拆除
    unsigned char anti_theft_state;
    //电池电压，上报的数据除以10
    unsigned char battery_voltage;
}__attribute__((packed))  InfraredRxSt;


typedef struct {
    //红外状态 0未触发 1触发
    int infrared_state;
    //是否被拆除 0未拆除 1拆除
    int anti_theft_state;
    //电池电压，上报的数据除以10
    float battery_voltage;
}InfraredSt;

/**
 * 红外传感器
 */
class InfraredDev : Device {

public:
    InfraredDev(DeviceParam &device);
    ~InfraredDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    InfraredSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_INFRARED_DEV_H
