#include "fx_theftproof_dev.h"
#include <iostream>
#include <cstring>
#include <device_interface.h>


FXTheftproofDev::FXTheftproofDev(DeviceParam &device) : Device(device)
{
}

FXTheftproofDev::~FXTheftproofDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool FXTheftproofDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool FXTheftproofDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    if (mData.lose == 1){
        Json::Value v;
        v["state"] = mData.lose;
        DeviceHelper::getInstance().ReportDevEvent(this, "lose_event", v);
    }

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int FXTheftproofDev::UnpackSensorData(string &data)
{
    FxRxSt st = {0};
    memcpy(&st,data.c_str(),sizeof(FxRxSt));
    if (st.key_state == 0){
        mData.lose = 1;
    } else{
        mData.lose = 0;
    }
    mData.state = st.state;

    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int FXTheftproofDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int FXTheftproofDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "lose"){
            res["lose"] = mData.lose;
        } else if (i.asString() == "state"){
            res["state"] = mData.state;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int FXTheftproofDev::PostProperty()
{
    Json::Value v;
    v["lose"] = mData.lose;
    v["state"] = mData.state;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

