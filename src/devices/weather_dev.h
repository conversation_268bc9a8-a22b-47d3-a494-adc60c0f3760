/**
 * 气象传感器
 * 检测大气温度、湿度、PM2.5、PM10、环境噪音
 */
#ifndef GATEWAY_IOT_WEATHER_DEV_H
#define GATEWAY_IOT_WEATHER_DEV_H


#include <cstdlib>
#include "device.h"

#if USE_AD_SENSOR
/**
 * property
 */
typedef struct {

    unsigned char r1[3];
    short humidity;
    short temperature;
    unsigned char r8[4];
    short pm25;
    unsigned char r14[8];
    short pm10;
    unsigned char r24[4];
    short noise;

}__attribute__((packed)) WeatherRxSt;

#else
typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    unsigned char electricQuantity;

    //湿度
    short humidity;
    //温度
    short temperature;
    //pm2.5
    short pm25;
    //pm10
    short pm10;
    //噪声
    short noise;
    //crc
    unsigned char crc;
}__attribute__((packed))  WeatherRxSt;
#endif

/**
 * property
 */
typedef struct {

    float humidity;
    float temperature;
    float pm25;
    float pm10;
    float noise;
    int electricQuantity;
    int state;

}WeatherSt;


/**
 * 气象传感器
 * 检测大气温度、湿度、PM2.5、PM10、环境噪音
 */
class WeatherDev : public Device
{
public:
    WeatherDev(DeviceParam &device);
    ~WeatherDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    WeatherSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int PostLowPowerEvent();

    //解析设备数据
    int UnpackSensorData(string &data);
};
#endif //GATEWAY_IOT_WEATHER_DEV_H
