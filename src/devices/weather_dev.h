/**
 * 气象传感器
 * 检测大气温度、湿度、PM2.5、PM10、环境噪音
 */
#ifndef GATEWAY_IOT_WEATHER_DEV_H
#define GATEWAY_IOT_WEATHER_DEV_H


#include <cstdlib>
#include "device.h"

#if USE_AD_SENSOR
/**
 * property
 */
typedef struct {

    unsigned char r1[3];
    short humidity;
    short temperature;
    unsigned char r8[4];
    short pm25;
    unsigned char r14[8];
    short pm10;
    unsigned char r24[4];
    short noise;

}__attribute__((packed)) WeatherRxSt;

#else
typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth (30字节数据区)
    unsigned char length;

    // 数据区 (30字节) - 15个寄存器，每个2字节
    short humidity;        // 寄存器0: 湿度 (0.1 %RH)
    short temperature;     // 寄存器1: 温度 (0.1 °C)
    short reserved1;       // 寄存器2: 保留
    short reserved2;       // 寄存器3: 保留
    short pm25;           // 寄存器4: PM2.5 (1 ug/m³)
    short reserved3;       // 寄存器5: 保留
    short light;          // 寄存器6: 光照度 (1 Lux)
    short noise;          // 寄存器7: 噪声 (0.1 dB)
    short reserved4;       // 寄存器8: 保留
    short reserved5;       // 寄存器9: 保留
    short pm10;           // 寄存器10: PM10 (1 ug/m³)
    short reserved6;       // 寄存器11: 保留
    short reserved7;       // 寄存器12: 保留
    short windSpeed;     // 寄存器13: 风速 (0.1 m/s)
    short windDirection; // 寄存器14: 风向 (1°)

    //crc
    unsigned char crc[2];
}__attribute__((packed))  WeatherRxSt;
#endif

/**
 * property
 */
typedef struct {

    float humidity;        // 湿度 (%RH)
    float temperature;     // 温度 (°C)
    float pm25;           // PM2.5 (ug/m³)
    float pm10;           // PM10 (ug/m³)
    float noise;          // 噪声 (dB)
    float light;          // 光照度 (Lux)
    float windSpeed;     // 风速 (m/s)
    float windDirection; // 风向 (°)
    int state;            // 设备状态

}WeatherSt;


/**
 * 气象传感器
 * 检测大气温度、湿度、PM2.5、PM10、环境噪音
 */
class WeatherDev : public Device
{
public:
    WeatherDev(DeviceParam &device);
    ~WeatherDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    WeatherSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int PostLowPowerEvent();

    //解析设备数据
    int UnpackSensorData(string &data);
};
#endif //GATEWAY_IOT_WEATHER_DEV_H
