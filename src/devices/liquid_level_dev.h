/**
 * 液位传感器
 */

#ifndef GATEWAY_IOT_WATER_LEVEL_DEV_H
#define GATEWAY_IOT_WATER_LEVEL_DEV_H

#include "device.h"



typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //最高3bit为版本，低5bit为MCU电压等级作为电量指示
    unsigned char electricQuantity;

    //液位 单位Pa,如需要液位高度，需换算
    unsigned int press;

    //crc
    unsigned char crc;

}__attribute__((packed))  LiquidLevelRxSt;




typedef struct {
    int adcValue;
    float liquidLevelHeight;
    unsigned int electricQuantity;
}LiquidLevelSt;


/**
 * 液位传感器
 */
class LiquidLevelDev :Device{
public:
    LiquidLevelDev(DeviceParam &device);
    ~LiquidLevelDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    LiquidLevelSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //服务调用
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_WATER_LEVEL_DEV_H
