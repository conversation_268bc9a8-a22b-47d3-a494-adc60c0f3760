//
// Created by seashell on 8/6/22.
//

#include "solenoidvalve_dev.h"

/**
 * 电磁阀
 */



SolenoidValveDev::SolenoidValveDev(DeviceParam &device) : Device(device)
{
    string prefixThing = "$thing/";
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + "/service/open/call");
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + "/service/close/call");
}


SolenoidValveDev::~SolenoidValveDev()
{
}





/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SolenoidValveDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else if (IS_TOPIC_MESSAGE(topic, "/service/close/call")) //close
    {
        lastQuestTopic = topic;
        Close();
        return true;
    }
    else if (IS_TOPIC_MESSAGE(topic, "/service/open/call")) //open
    {
        lastQuestTopic = topic;
        Open();
        return true;
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SolenoidValveDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}



/**
 * 解析设备数据
 * @param data
 * @return
 */
int SolenoidValveDev::UnpackSensorData(string &data)
{

    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        unsigned char tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            short devInfo = 0;
            memcpy((void *)&devInfo, (const void *)&sensorData[i], 2);
            //mData.voltage_level  = devInfo & 0x1f;
            i += 2;
        }
            //
        else if (tag == 0x09 ){
            memcpy((void *)&mData.state, (const void *)&sensorData[i], 1);
            i += 1;
        }
        else if (tag == 0x1a ){
            int length = sensorData[i];
            i += 1;

            if (length == 2){
                memcpy((void *)&mData.voltage, (const void *)&sensorData[i], 2);
                mData.voltage = ntohs(mData.voltage);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&mData.voltage, (const void *)&sensorData[i], 2);
                mData.voltage = ntohs(mData.voltage);
                i += 2;
                memcpy((void *)&mData.current, (const void *)&sensorData[i], 2);
                mData.current = ntohs(mData.current);
                i += 2;
            }

        }
        else if (tag == 0x1b ){
            int length = sensorData[i];
            i += 1;

            if (length == 4){
                memcpy((void *)&mData.power, (const void *)&sensorData[i], 4);
                mData.power = ntohl(mData.power);
                i += 4;
            } else if (length == 8){
                memcpy((void *)&mData.power, (const void *)&sensorData[i], 4);
                mData.power = ntohl(mData.power);
                i += 4;
                //reserve
                int reserve;
                memcpy((void *)&reserve, (const void *)&sensorData[i], 4);
                i += 4;
            }

        }
        else if (tag == 0xff ){

            Json::Value res;
            res["valveState"] = 0;
            ValveControlResSt st = {0};
            memcpy((void *)&st, (const void *)&sensorData[i], 2);
            i += 2;
            if (st.state == 0x0){
                string prefixThing = "$thing/";
                string topic = lastQuestTopic + "_res";
                res["valveState"] = 0;
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, -1, topic, res);

            } else if (st.state == 0x1){

                string prefixThing = "$thing/";
                string topic = lastQuestTopic + "_res";
                res["valveState"] = 1;
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, 0, topic, res);
            }

        }
        else if (tag == 0x81 ){
            //device config
            int length = sensorData[i];
            i += 1;

            int reportCycle = 0;
            int detectCycle = 0;
            int calibrationValue = 0;
            if (length == 2){

                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
            }
            else if (length == 8){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
                memcpy((void *)&calibrationValue, (const void *)&sensorData[i], 4);
                calibrationValue = ntohl(calibrationValue);
                i += 4;
            }
        }
    }




    return 0;
}





/**
 * 设置属性
 * @param jValue
 * @return
 */
int SolenoidValveDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SolenoidValveDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "voltage"){
            res["voltage"] = mData.voltage;
        } else if (i.asString() == "current"){
            res["current"] = mData.current;
        } else if (i.asString() == "power"){
            res["power"] = mData.power;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SolenoidValveDev::PostProperty()
{
    Json::Value v;
    v["voltage"] = mData.voltage;
    v["power"] = mData.power;
    v["state"] = mData.state;
    v["current"] = mData.current;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

int SolenoidValveDev::Open()
{

    ValveControlSt st = {0};
    st.cmd = 0x22;
    st.state = 1;
    //st.crc = calXor((u8 *) &st, sizeof(ValveControlSt) - 1);
    string data = Base64Encode((char *) &st, sizeof(ValveControlSt));
    DeviceHelper::getInstance().SendMessageToSensor(this, data);
    return 0;
}

int SolenoidValveDev::Close() {

    ValveControlSt st = {0};
    st.cmd = 0x22;
    st.state = 0;
    //st.crc = calXor((u8 *) &st, sizeof(ValveControlSt) - 1);
    string data = Base64Encode((char *) &st, sizeof(ValveControlSt));
    DeviceHelper::getInstance().SendMessageToSensor(this, data);
    return 0;
}
