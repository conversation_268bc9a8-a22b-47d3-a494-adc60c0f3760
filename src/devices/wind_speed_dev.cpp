//
// Created by seashell on 7/2/21.
//

#include <common_func.h>
#include <netdb.h>
#include "wind_speed_dev.h"
#include "device_interface.h"

WindSpeedDev::WindSpeedDev(DeviceParam &device) : Device(device)
{
}


WindSpeedDev::~WindSpeedDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WindSpeedDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WindSpeedDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}



/**
 * 解析设备数据
 * @param data
 * @return
 */
int WindSpeedDev::UnpackSensorData(string &data)
{

    WindSpeedRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WindSpeedRxSt));

    mData.speed = (float )(ntohs(st.speed));
    mData.electricQuantity = 31;
    mData.state = 0;
    return 0;
}




/**
 * 设置属性
 * @param jValue
 * @return
 */
int WindSpeedDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WindSpeedDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "speed"){
            res["speed"] = mData.speed;
        } else if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WindSpeedDev::PostProperty()
{
    Json::Value v;
    v["speed"] = mData.speed;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
