#include "electricity_meter_dev.h"


ElectricityMeterDev::ElectricityMeterDev(DeviceParam &device) : Device(device)
{
}

ElectricityMeterDev::~ElectricityMeterDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool ElectricityMeterDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool ElectricityMeterDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 *  // 组合有功总电量
    unsigned int totalElectricity;

    // 正向有功总电量
    unsigned int forwardElectricity;

    // 反向有功总电量
    unsigned int reverseElectricity;

    // 电压
    unsigned short voltage;

    // 电流
    unsigned char current[3];

    // 有功功率
    unsigned char activePower[3];

    // 电网频率
    unsigned short frequency;

    // 功率因数
    unsigned short powerFactor;

    // 状态位
    unsigned char state;
 */
int ElectricityMeterDev::UnpackSensorData(string &data)
{
    //SingleElectricityMeterSt mData;
    SingleElectricityMeterRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SingleElectricityMeterRxSt));

    mData.dataFlag = bcd_decimal_code(st.dataFlag) - bcd_decimal_code(0x33333333);

    //kWh
    mData.totalElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.forwardElectricity = (bcd_decimal_code(st.forwardElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
    mData.reverseElectricity = (bcd_decimal_code(st.reverseElectricity) - bcd_decimal_code(0x33333333)) * 0.01;


    //V
    mData.voltage = (bcd_decimal_code(st.voltage) - bcd_decimal_code(0x3333)) * 0.1;

    //a
    int _current = 0;
    memcpy((void *)&_current, st.current,3);
    mData.current  = (bcd_decimal_code(_current) - bcd_decimal_code(0x333333)) * 0.0001;;

    //w
    int activePower = 0;
    memcpy((void *)&activePower, st.activePower,3);
    mData.activePower = (bcd_decimal_code(activePower)- bcd_decimal_code(0x333333)) * 0.0001;
    mData.frequency = (bcd_decimal_code(st.frequency) - bcd_decimal_code(0x3333)) * 0.01;
    mData.powerFactor = (bcd_decimal_code(st.powerFactor) - bcd_decimal_code(0x3333)) * 0.001;
    mData.state = st.state;

    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int ElectricityMeterDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int ElectricityMeterDev::GetProperty(Json::Value &req, Json::Value &res)
{

    for (const auto &i : req)
    {
        if (i.asString() == "totalElectricity"){
            res["totalElectricity"] = mData.totalElectricity;
        } else if (i.asString() == "forwardElectricity"){
            res["forwardElectricity"] = mData.forwardElectricity;
        } else if (i.asString() == "reverseElectricity"){
            res["reverseElectricity"] = mData.reverseElectricity;
        } else if (i.asString() == "voltage"){
            res["voltage"] = mData.voltage;
        } else if (i.asString() == "current"){
            res["current"] = mData.current;
        } else if (i.asString() == "activePower"){
            res["activePower"] = mData.activePower;
        } else if (i.asString() == "activePower"){
            res["activePower"] = mData.activePower;
        } else if (i.asString() == "frequency"){
            res["frequency"] = mData.frequency;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int ElectricityMeterDev::PostProperty()
{

    Json::Value v;
    v["totalElectricity"] = mData.totalElectricity;
    v["forwardElectricity"] = mData.forwardElectricity;
    v["reverseElectricity"] = mData.reverseElectricity;
    v["voltage"] = mData.voltage;
    v["current"] = mData.current;
    v["activePower"] = mData.activePower;
    v["frequency"] = mData.frequency;
    v["powerFactor"] = mData.powerFactor;

    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}


