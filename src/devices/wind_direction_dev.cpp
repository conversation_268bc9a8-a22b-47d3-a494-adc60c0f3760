//
// Created by seashell on 7/2/21.
//

#include <thing_tool.h>
#include <device_interface.h>
#include "wind_direction_dev.h"




WindDirectionDev::WindDirectionDev(DeviceParam &device): Device(device)
{

}




WindDirectionDev::~WindDirectionDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WindDirectionDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WindDirectionDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int WindDirectionDev::UnpackSensorData(string &data)
{
    WindDirectionRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WindDirectionRxSt));
    mData.direction = ntohs(st.direction);
    mData.electricQuantity = 31;


    SetDevTypeSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x1;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.devType = 2;
    set.crc = calXor((u8 *) &set, sizeof(SetDevTypeSt) - 1);

    char tmp[250] = {0};
    memcpy(tmp,(void *)&set,sizeof(SetDevTypeSt));
    string base64Data = Base64Encode((char *) &set, sizeof(SetDevTypeSt));

    //string downStr = tmp;
    //string base64Data = base64Encode(downStr);
    std::cout << base64Data <<endl;
    //DeviceHelper::getInstance().sendMessageToSensor(this,base64Data);
    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int WindDirectionDev::SetProperty(Json::Value &req)
{

    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WindDirectionDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "direction"){
            res["direction"] = mData.direction;
        } else if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WindDirectionDev::PostProperty()
{
    Json::Value v;
    v["direction"] = mData.direction;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
