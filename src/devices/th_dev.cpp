//
// Created by seashell on 6/6/22.
//

#include "th_dev.h"

/***** 能力集属性 *****/
#define TEMPERATURE         "temperature"
#define ELECTRICQUANTITY    "electricQuantity"
#define HUMIDITY            "humidity"
#define ANTI_THEFT_STATE    "anti_theft_state"

/***** 事件 *****/
#define EVENT_LOW_VOLTAGE           "low_voltage"           //低电压报警
#define EVENT_ANTI_THEFT_ALARM      "remove"                //防拆报警


THDev::THDev(DeviceParam &device) : Device(device)
{
    this->product_key = device.product_key;
    this->product_secret = device.device_secret;
    this->device_name = device.device_name;
    this->device_secret = device.device_secret;
    this->is_gateway = device.is_gateway;
    this->is_bind = device.is_bind;
    this->is_register = device.is_register;
    this->is_online = device.is_online;

    this->f_port = device.f_port;
    this->app_id = device.app_id;
    this->dev_type = device.dev_type;
    this->device_eui = device.device_eui;
}




THDev::~THDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool THDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool THDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();


    return true;
}


#if 0
/**
 * 解析设备数据
 * @param data
 * @return
 */
int THDev::UnpackSensorData(string &data)
{

    THRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(THRxSt));
    mData.temperature = ntohs(st.temperature)/10.0;
    mData.humidity = ntohs(st.humidity)/10.0;

    return 0;
}
#endif
/**
 * 解析设备数据
 * @param data
 * @return
 */
int THDev::UnpackSensorData(string &data)
{

    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        int tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            short devInfo = 0;
            memcpy((void *)&devInfo, (const void *)&sensorData[i], 2);
            mData.electricQuantity  = devInfo & 0x1f;
            i += 2;
        }
            //
        else if (tag == 0x04 ){
            memcpy((void *)&mData.temperature, (const void *)&sensorData[i], 2);
            mData.temperature = ntohs(mData.temperature)/10;
            i += 2;
        }
        else if (tag == 0x05 ){
            memcpy((void *)&mData.humidity, (const void *)&sensorData[i], 1);
            mData.humidity = mData.humidity;
            i += 1;
        }
            //
        else if (tag == 0x81 ){
            //device config
            int length = sensorData[i];
            i += 1;

            int reportCycle = 0;
            int detectCycle = 0;
            int calibrationValue = 0;
            if (length == 2){

                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
            }
            else if (length == 8){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
                memcpy((void *)&calibrationValue, (const void *)&sensorData[i], 4);
                calibrationValue = ntohl(calibrationValue);
                i += 4;
            }
        }
    }

    return 0;
}

/**
 * 设置属性
 * @param jValue
 * @return
 */
int THDev::SetProperty(Json::Value &req)
{

    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int THDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == TEMPERATURE){
            res[TEMPERATURE] = mData.temperature;
        }
        else if (i.asString() == ELECTRICQUANTITY){
            res[ELECTRICQUANTITY] = mData.electricQuantity;
        } else if (i.asString() == HUMIDITY){
            res[HUMIDITY] = mData.humidity;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int THDev::PostProperty()
{
    Json::Value v;
    v[TEMPERATURE] = mData.temperature;
    v[ELECTRICQUANTITY] = mData.electricQuantity;
    v[HUMIDITY] = mData.humidity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
