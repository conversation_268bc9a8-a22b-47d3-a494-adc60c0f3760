/**
 * 雨量传感器
 */

#ifndef GATEWAY_IOT_RAINFALL_DEV_H
#define GATEWAY_IOT_RAINFALL_DEV_H

#include <cstdlib>
#include "device.h"

typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //雨量
    short rainfall;
    //crc
    unsigned char crc;
}__attribute__((packed))  RainfallRxSt;


/**
 * smoke property
 */
typedef struct {
    //电池电压，上报的数据除以10
    float electricQuantity;
    //风向
    short rainfall;

}RainfallSt;


/**
 * 雨量传感器
 */
class RainfallDev : public Device
{
public:
    RainfallDev(DeviceParam &device);
    ~RainfallDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    RainfallSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};

#endif //GATEWAY_IOT_RAINFALL_DEV_H
