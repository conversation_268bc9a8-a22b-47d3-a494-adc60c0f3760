/**
 * 水浸传感器
 */


#ifndef GATEWAY_IOT_WATER_IMMERSION_DEV_H
#define GATEWAY_IOT_WATER_IMMERSION_DEV_H


#include "device.h"

#if 0
#if USE_AD_SENSOR
typedef struct {
    //水浸状态 0正常  1水浸
    unsigned char flooding;

    unsigned char reserved;
    //第 3 个字节，最高位为 0 表示电压正常，为
    //1 表示电压低，低 7 位表示电压值，实际值
    //需除以 10，如 0x21 表示 3.3V，电压正常，
    //0x9F 表示 3.1V，电压低
    unsigned char voltage_info;


}__attribute__((packed))  WaterImmersionRxSt;
#else
typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //传感器类型 0正常 1水浸
    unsigned short flooding;

    //第 3 个字节，最高位为 0 表示电压正常，为
    //1 表示电压低，低 7 位表示电压值，实际值
    //需除以 10，如 0x21 表示 3.3V，电压正常，
    //0x9F 表示 3.1V，电压低
    //unsigned char voltage_info;

    //crc
    unsigned char crc;

}__attribute__((packed))  WaterImmersionRxSt;

#endif
#endif

typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //传感器类型 0正常 1水浸
    unsigned short flooding;

    //第 3 个字节，最高位为 0 表示电压正常，为
    //1 表示电压低，低 7 位表示电压值，实际值
    //需除以 10，如 0x21 表示 3.3V，电压正常，
    //0x9F 表示 3.1V，电压低
    //unsigned char voltage_info;

    //crc
    unsigned char crc;

}__attribute__((packed))  WaterImmersionRxSt;



typedef struct {
    unsigned int state;
    int electricQuantity;
}WaterImmersionSt;


/**
 * 水浸传感器
 */
class WaterImmersionDev :Device{
public:
    WaterImmersionDev(DeviceParam &device);
    ~WaterImmersionDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:


    //属性
    WaterImmersionSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};




#endif //GATEWAY_IOT_WATER_IMMERSION_DEV_H
