//
// Created by seashell on 6/6/22.
//

#include "liquid_level_underground_dev.h"

/***** 能力集属性 *****/
#define FLOODING_STATE          "flooding_state"
#define VOLTAGE                 "battery_voltage"

/***** 事件 *****/
#define TOPIC_E_FLOODING        "flooding"
#define TOPIC_E_LOW_VOLTAGE     "low_voltage"


LiquidLevelUndergroundDev::LiquidLevelUndergroundDev(DeviceParam &device) : Device(device)
{
}




LiquidLevelUndergroundDev::~LiquidLevelUndergroundDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool LiquidLevelUndergroundDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool LiquidLevelUndergroundDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}

#if !USE_AD_SENSOR
/**
 * 解析设备数据
 * @param data
 * @return
 */
int LiquidLevelUndergroundDev::UnpackSensorData(string &data)
{
    LiquidLevelUndergroundRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(LiquidLevelUndergroundRxSt));
    mData.flooding_state = st.flooding_state;
    //mData.battery_voltage = st.voltage/10.0;
    return 0;
}

#else
/**
 * 解析设备数据
 * @param data
 * @return
 */
int LiquidLevelUndergroundDev::UnpackSensorData(string &data)
{
    LiquidLevelUndergroundRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(LiquidLevelUndergroundRxSt));
    mData.flooding_state = st.flooding_state;
    mData.battery_voltage = 3.3;

    return 0;
}

#endif

/**
 * 设置属性
 * @param jValue
 * @return
 */
int LiquidLevelUndergroundDev::SetProperty(Json::Value &req)
{

    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int LiquidLevelUndergroundDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == FLOODING_STATE){
            res[FLOODING_STATE] = mData.flooding_state;
        } else if (key == VOLTAGE){
            res[VOLTAGE] = mData.battery_voltage;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int LiquidLevelUndergroundDev::PostProperty()
{
    Json::Value v;
    v[FLOODING_STATE] = mData.flooding_state;
    v[VOLTAGE] = mData.battery_voltage;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
