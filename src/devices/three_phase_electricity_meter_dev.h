/**
 * 三相电表
 */

#ifndef GATEWAY_IOT_THREE_PHASE_ELECTRICITY_METER_DEV_H
#define GATEWAY_IOT_THREE_PHASE_ELECTRICITY_METER_DEV_H

#include "device.h"


typedef struct {

    // 前导 FEFEFEFE
    unsigned int preamble;

    // 帧起始符 68
    unsigned char start1;

    // 地址 962624000000 (A5 A4 A3 A2 A1 A0)
    unsigned char addr[6];

    // 帧起始符 68
    unsigned char start2;

    // control 91
    unsigned char control;

    // lenght; 1D
    unsigned char length;

    // 0000FF00
    unsigned int dataFlag;
} __attribute__((packed)) ThreeElectricityMeterRxHead;


/**
 *  property
 */
typedef struct {


    ThreeElectricityMeterRxHead head;

    //总电量
    unsigned int totalElectricity;

    //尖时刻电量
    unsigned int jianElectricity;

    //峰电量
    unsigned int fengElectricity;
    //平电量
    unsigned int pingElectricity;
    //谷电量
    unsigned int guElectricity;

    unsigned char check;

    unsigned char end;

}__attribute__((packed)) ThreeElectricityMeterRxSt13;



/**
 *  property
 */
typedef struct {

    ThreeElectricityMeterRxHead head;

    //瞬时功率
    unsigned char shunshiPower[3];
    //瞬时A相功率
    unsigned char shunshiAPower[3];
    //瞬时B相功率
    unsigned char shunshiBPower[3];
    //瞬时C相功率
    unsigned char shunshiCPower[3];

    //总功率因子
    unsigned short totalPowerFactor;
    //A相功率因子
    unsigned short APowerFactor;
    //B相功率因子
    unsigned short BPowerFactor;
    //C相功率因子
    unsigned short CPowerFactor;
    //状态
    unsigned char state;

    unsigned char check;

    unsigned char end;

}__attribute__((packed)) ThreeElectricityMeterRxSt4;


/**
 *  property
 */
typedef struct {

    ThreeElectricityMeterRxHead head;

    //A相电压
    unsigned short APhaseVoltage;

    //B相电压
    unsigned short BPhaseVoltage;

    //C相dianya
    unsigned short CPhaseVoltage;

    //A
    unsigned short APhaseCurrent;

    //b
    unsigned short BPhaseCurrent;

    //c
    unsigned short CPhaseCurrent;

    unsigned char check;

    unsigned char end;

}__attribute__((packed)) ThreeElectricityMeterRxSt5;


/**
 *  property
 */
typedef struct {


    //
    float totalElectricity;

    //
    float forwardElectricity;

    //fan xiang
    float reverseElectricity;

    //瞬时总有功功率
    float totalPower;

    //瞬时A相功率
    float powerA;

    //瞬时B相功率
    float powerB;

    //瞬时C相功率
    float powerC;

    //总功率因素
    float totalPowerFactor;

    //
    float voltageA;

    float currentA;

    //
    float voltageB;

    float currentB;

    //
    float voltageC;

    float currentC;

}__attribute__((packed))  ThreeElectricityMeterSt;




/**
 * 三相电表
 */
class ThreePhaseElectricityMeterDev : public Device
{
public:
    ThreePhaseElectricityMeterDev(DeviceParam &device);
    ~ThreePhaseElectricityMeterDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    ThreeElectricityMeterSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_THREE_PHASE_ELECTRICITY_METER_DEV_H
