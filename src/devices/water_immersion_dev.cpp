//
// Created by seashell on 6/6/22.
//
// 水浸 sensor
#include "water_immersion_dev.h"

/***** 能力集属性 *****/
#define FLOODING_STATE              "state"
#define ELECTRICQUANTITY            "electricQuantity"

/***** 事件 *****/
#define TOPIC_E_FLOODING_ALARM     "flooding"
#define TOPIC_E_LOW_POWER           "low_voltage"


WaterImmersionDev::WaterImmersionDev(DeviceParam &device) : Device(device)
{
}

WaterImmersionDev::~WaterImmersionDev()
{
}

/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WaterImmersionDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WaterImmersionDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    if (mData.state == 1){
        Json::Value v;
        v[FLOODING_STATE] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_FLOODING_ALARM, v);
    }
    return true;
}

#if USE_AD_SENSOR
/**
 * 解析设备数据
 * @param data
 * @return
 */
int WaterImmersionDev::UnpackSensorData(string &data)
{
    WaterImmersionRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WaterImmersionRxSt));
    mData.battery_voltage = (st.voltage_info & 0x7f)/10.0;
    mData.flooding_state = st.flooding;
    return 0;
}

#else
/**
 * 解析设备数据
 * @param data
 * @return
 */
int WaterImmersionDev::UnpackSensorData(string &data)
{
    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        int tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            short devInfo = 0;
            memcpy((void *)&devInfo, (const void *)&sensorData[i], 2);
            mData.electricQuantity  = devInfo & 0x1f;
            i += 2;
        }
            //
        else if (tag == 0x09 ){
            memcpy((void *)&mData.state, (const void *)&sensorData[i], 1);
            mData.state = mData.state;
            i += 1;
        }
            //
        else if (tag == 0x81 ){
            //device config
            int length = sensorData[i];
            i += 1;

            int reportCycle = 0;
            int detectCycle = 0;
            int calibrationValue = 0;
            if (length == 2){

                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
            } else if (length == 4){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
            }
            else if (length == 8){
                memcpy((void *)&reportCycle, (const void *)&sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *)&detectCycle, (const void *)&sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
                memcpy((void *)&calibrationValue, (const void *)&sensorData[i], 4);
                calibrationValue = ntohl(calibrationValue);
                i += 4;
            }
        }
    }

    return 0;
}

#endif

/**
 * 设置属性
 * @param jValue
 * @return
 */
int WaterImmersionDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WaterImmersionDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == FLOODING_STATE){
            res[FLOODING_STATE] = mData.state;
        } else if (key == ELECTRICQUANTITY){
            res[ELECTRICQUANTITY] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WaterImmersionDev::PostProperty()
{
    Json::Value v;
    v[FLOODING_STATE] = mData.state;
    v[ELECTRICQUANTITY] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
