//
// Created by seashell on 1/17/22.
//

#include "transparent_dev.h"
/***** 主题 *****/



/***** 能力集属性 *****/

#define MESSAGE    "message"

/***** 事件 *****/


TransparentDev::TransparentDev(DeviceParam &device) : Device(device)
{
}


TransparentDev::~TransparentDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool TransparentDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool TransparentDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}

#if USE_AD_SENSOR
/**
 * 解析设备数据
 * @param data
 * @return
 */
int TransparentDev::UnpackSensorData(string &data)
{
    return 0;
}

#else

/**
 * 解析设备数据
 * @param data
 * @return
 */
int TransparentDev::UnpackSensorData(string &data)
{

    SetDevTypeSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x1;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.devType = 0xb;
    set.crc = calXor((u8 *) &set, sizeof(SetDevTypeSt) - 1);

    char tmp[250] = {0};
    memcpy(tmp,(void *)&set,sizeof(SetDevTypeSt));
    string base64Data = Base64Encode((char *) &set, sizeof(SetDevTypeSt));
    std::cout <<"设置气象命令:"<< base64Data <<endl;


    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x1;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.devType = 0x1;
    set.crc = calXor((u8 *) &set, sizeof(SetDevTypeSt) - 1);

    //char tmp[250] = {0};
    memset(tmp,0,250);
    memcpy(tmp,(void *)&set,sizeof(SetDevTypeSt));
    base64Data = Base64Encode((char *) &set, sizeof(SetDevTypeSt));
    std::cout <<"设置风速命令:"<< base64Data <<endl;

    //char buff[2048] = {0};
    //memcpy(buff,data.c_str(),data.length());

    //TransparentSt st;
    //memcpy((void *)&st,data.c_str(),sizeof(TransparentSt));

    memcpy(mData.buffer,data.c_str(),data.length());
    //mData.buffer = (float )(ntohs(st.speed)/10.0);
    //mData.voltage = 3.3;//(float )(st.voltage/10.0);
    return 0;
}

#endif



/**
 * 设置属性
 * @param jValue
 * @return
 */
int TransparentDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int TransparentDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == MESSAGE){
            res[MESSAGE] = string(mData.buffer);
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int TransparentDev::PostProperty()
{
    Json::Value v;
    v[MESSAGE] = string(mData.buffer);
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
