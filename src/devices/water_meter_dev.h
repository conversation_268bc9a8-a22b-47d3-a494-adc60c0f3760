/**
 * 水表
 */

#ifndef GATEWAY_IOT_WATER_METER_DEV_H
#define GATEWAY_IOT_WATER_METER_DEV_H


#include "device.h"

#if 0
#if 0
typedef struct {

    // 前导 FEFEFEFE
    unsigned int preamble;

    // 帧起始符 68
    unsigned char start1;

    // 地址 962624000000 (A5 A4 A3 A2 A1 A0)
    unsigned char addr;

    // 帧起始符 68
    unsigned char start2;

    // 控制码 91
    unsigned char control;

    // 长度 0C
    unsigned char length;

    // 数据标识 00004200
    unsigned int dataFlag;

    // 数据 累计用水量 BCD
    unsigned int AccumulatedWaterConsumption;

    //状态字
    // STO
    // D7 保留
    // D6 强制状态 0无强制 1有强制
    // D5 强磁干扰 0正常  1透支
    // D4 透支状态 0正常 1透支
    // D3 报警状态 0正常 1报警
    // D2 电池状态 0正常 1欠压
    // D1 D0阀门状态  00开 01关 11异常
    // ST1
    unsigned short state;


    // 高位字节（保留，补 0）
    // 低位位字节 电池电量
    //电压计算公式：15.37×xxH=xxxxxmV
    unsigned short extend;

    // 校验和
    unsigned char check;

    // 结束符
    unsigned char end;

}__attribute__((packed))  WaterMeterDevRxSt;
#else
typedef struct {

    // 前导 FEFEFEFE
    unsigned int preamble;

    // 帧起始符 68
    unsigned char start1;

    // 地址 962624000000 (A5 A4 A3 A2 A1 A0)
    unsigned char addr;

    // 帧起始符 68
    unsigned char start2;

    // 控制码 91
    unsigned char control;

    // 长度 0C
    unsigned char length;

    // 数据标识 00004200
    unsigned int dataFlag;

    // 数据 累计用水量 BCD
    unsigned int AccumulatedWaterConsumption;

    //状态字
    // STO
    // D7 保留
    // D6 强制状态 0无强制 1有强制
    // D5 强磁干扰 0正常  1透支
    // D4 透支状态 0正常 1透支
    // D3 报警状态 0正常 1报警
    // D2 电池状态 0正常 1欠压
    // D1 D0阀门状态  00开 01关 11异常
    // ST1
    unsigned short state;


    // 高位字节（保留，补 0）
    // 低位位字节 电池电量
    //电压计算公式：15.37×xxH=xxxxxmV
    unsigned short extend;

    // 校验和
    unsigned char check;

    // 结束符
    unsigned char end;

}__attribute__((packed))  WaterMeterDevRxSt;

#endif



typedef struct {

    // 数据 累计用水量 BCD
    unsigned int AccumulatedWaterConsumption;

    //状态字
    // STO
    // D7 保留
    // D6 强制状态 0无强制 1有强制
    // D5 强磁干扰 0正常  1透支
    // D4 透支状态 0正常 1透支
    // D3 报警状态 0正常 1报警
    // D2 电池状态 0正常 1欠压
    // D1 D0阀门状态  00开 01关 11异常
    // ST1
    unsigned short state;


    //电量
    unsigned int electricity;
}WaterMeterDevSt;

#endif

typedef struct {

    // 前导 81H
    unsigned char preamble;

    // 日冻结累积流量BCD
    unsigned char quantity[5];

    // 电池电压
    unsigned char voltage1;

    // 电池电压
    unsigned char voltage2;

    // 硬件版本
    unsigned char hardwareVersion;

    // 软件version
    unsigned char softwareVersion;

    // 校验cs
    unsigned char cs;

}__attribute__((packed))  WaterMeterDevRxSt1;



typedef struct {

    // 前导 82H
    unsigned char preamble;

    // address BCD
    unsigned char address[7];

    // lora rssi
    unsigned short rssi;

    // 校验cs
    unsigned char cs;

}__attribute__((packed))  WaterMeterDevRxSt2;


typedef struct {

    // 前导 83H
    unsigned char preamble;

    // time min hour day month year BCD
    unsigned char address[5];

    // sensor A rssi
    unsigned short rssiA;

    // sensor B rssi
    unsigned short rssiB;

    // 校验cs
    unsigned char cs;

}__attribute__((packed))  WaterMeterDevRxSt3;



typedef struct {

    // 前导 84H
    unsigned char preamble;

    // time min hour day month year BCD
    unsigned char totalQuantity1[2];
    unsigned char totalQuantity2[3];

    // lora sNR
    unsigned char snr;

    // state st0
    unsigned short st0;

    // state st1
    unsigned char st1;

    // 校验cs
    unsigned char cs;

}__attribute__((packed))  WaterMeterDevRxSt4;


typedef struct {

    // 数据 累计用水量 BCD
    float AccumulatedWaterConsumption;

    //状态字
    // STO
    // D7 保留
    // D6 强制状态 0无强制 1有强制
    // D5 强磁干扰 0正常  1透支
    // D4 透支状态 0正常 1透支
    // D3 报警状态 0正常 1报警
    // D2 电池状态 0正常 1欠压
    // D1 D0阀门状态  00开 01关 11异常
    // ST1
    unsigned short state;


    //电量
    float voltage;
}WaterMeterDevSt;

/**
 * 水表
 */
class WaterMeterDev :Device{
public:
    WaterMeterDev(DeviceParam &device);
    ~WaterMeterDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    WaterMeterDevSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_WATER_METER_DEV_H
