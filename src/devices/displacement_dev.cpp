#include "displacement_dev.h"
#include <common_func.h>
#include <netdb.h>
#include "device_interface.h"

#define VOLTAGE   "voltage"


DisplacementDdev::DisplacementDdev(DeviceParam &device) :Device(device)
{
}


DisplacementDdev::~DisplacementDdev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool DisplacementDdev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool DisplacementDdev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int DisplacementDdev::UnpackSensorData(string &data)
{
    char buff[2048] = {0};
    memcpy(buff,data.c_str(),data.length());

    DisplacementRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(DisplacementRxSt));

    mData.voltage = (float )((st.voltageState & 0x7f) / 10.0);
    mData.isDismantle = st.Dismantle;
    return 0;
}





/**
 * 设置属性
 * @param jValue
 * @return
 */
int DisplacementDdev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int DisplacementDdev::GetProperty(Json::Value &req, Json::Value &res)
{
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int DisplacementDdev::PostProperty()
{

    Json::Value v;
    v[VOLTAGE] = 3.6;// mData.voltage;
    DeviceHelper::getInstance().ReportPropEvent(this, v);

   if (mData.isDismantle)
    {
        Json::Value v;
        v["isDismantle"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "dismantle_event", v);
        std::cout << "fuck post Property3"<<endl;
    }
    std::cout << "fuck post Property4"<<endl;

    return 0;
}
