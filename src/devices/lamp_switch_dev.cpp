//
// Created by seashell on 6/6/22.
//

#include "lamp_switch_dev.h"


/***** 能力集属性 *****/
#define CURRENT         "current"
#define VOLTAGE         "voltage"
#define POWER           "power"
#define SWITCH_STATE    "switch_state"
#define BRIGHTNESS      "brightness"
#define VOLTAGE_STATE   "voltage_state"
#define CURRENT_STATE   "current_state"
#define SENSOR_STATE    "sensor_state"


/***** 事件 *****/
/***** SERVICE *****/
#define TOPIC_OPEN_LIGHT_SERVICE    "/service/open_light/call"                 //服务调用
#define TOPIC_CLOSE_LIGHT_SERVICE   "/service/close_light/call"               //服务调用

LAMPSwitchDev::LAMPSwitchDev(DeviceParam &device) : Device(device)
{
    string prefixThing = "$thing/";
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_OPEN_LIGHT_SERVICE);
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_CLOSE_LIGHT_SERVICE);
}




LAMPSwitchDev::~LAMPSwitchDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool LAMPSwitchDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_CLOSE_LIGHT_SERVICE)) //close
    {
        //TODO
        ;
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_OPEN_LIGHT_SERVICE)) //open
    {
        //TODO
        ;
    } else{
        LOG(INFO) << topic ;
        LOG(INFO) << jsonTostring(jValue);
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool LAMPSwitchDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int LAMPSwitchDev::UnpackSensorData(string &data)
{
    LAMPRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(LAMPRxSt));
    mData.voltage = ntohs(st.voltage)/10.0;
    mData.brightness = st.brightness;
    mData.current = ntohl(st.current);
    mData.power = ntohl(st.power)/10.0;
    mData.switch_state = st.switch_state;
    mData.current_state = st.device_state.current_state;
    mData.voltage_state = st.device_state.voltage_state;
    mData.sensor_state = st.device_state.sensor_state;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int LAMPSwitchDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int LAMPSwitchDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == CURRENT){
            res[CURRENT] = mData.current;
        } else if (key == VOLTAGE){
            res[VOLTAGE] = mData.voltage;
        }  else if (key == POWER){
            res[POWER] = mData.power;
        }  else if (key == SWITCH_STATE){
            res[SWITCH_STATE] = mData.switch_state;
        }  else if (key == BRIGHTNESS){
            res[BRIGHTNESS] = mData.brightness;
        }  else if (key == VOLTAGE_STATE){
            res[VOLTAGE_STATE] = mData.voltage_state;
        }  else if (key == CURRENT_STATE){
            res[CURRENT_STATE] = mData.current_state;
        }  else if (key == SENSOR_STATE){
            res[SENSOR_STATE] = mData.sensor_state;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int LAMPSwitchDev::PostProperty()
{
    Json::Value v;
    v[CURRENT] = mData.current;
    v[VOLTAGE] = mData.voltage;
    v[POWER] = mData.power;
    v[SWITCH_STATE] = mData.switch_state;
    v[BRIGHTNESS] = mData.brightness;
    v[VOLTAGE_STATE] = mData.voltage_state;
    v[CURRENT_STATE] = mData.current_state;
    v[SENSOR_STATE] = mData.sensor_state;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
