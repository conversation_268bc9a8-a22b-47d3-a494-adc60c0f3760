/**
 * 单相电表
 *
 */
#ifndef GATEWAY_IOT_ELECTRICITY_METER_DEV_H
#define GATEWAY_IOT_ELECTRICITY_METER_DEV_H

#include "device.h"


/**
 *  单相电表数据示例
 *  FE FE FE FE 68 13 54 48  00 00 00 68 91 1D 34 43
 *  93 37 33 33 33 33 33 33  33 33 33 33 33 33 69 55
 *  33 33 33 33 33 33 33 83  33 43 33 21 16
 */
typedef struct {

    // 前导 FEFEFEFE
    unsigned int preamble;

    // 帧起始符 68
    unsigned char start1;

    // 地址 962624000000 (A5 A4 A3 A2 A1 A0)
    unsigned char addr[6];

    // 帧起始符 68
    unsigned char start2;

    // 控制码 91
    unsigned char control;

    // 长度 1D
    unsigned char length;

    // 数据标识 4601001
    unsigned int dataFlag;

    // 组合有功总电量
    unsigned int totalElectricity;

    // 正向有功总电量
    unsigned int forwardElectricity;

    // 反向有功总电量
    unsigned int reverseElectricity;

    // 电压
    unsigned short voltage;

    // 电流
    unsigned char current[3];

    // 有功功率
    unsigned char activePower[3];

    // 电网频率
    unsigned short frequency;

    // 功率因数
    unsigned short powerFactor;

    // 状态位
    unsigned char state;

    // 校验和
    unsigned char check;

    // 结束符
    unsigned char end;

}__attribute__((packed)) SingleElectricityMeterRxSt;




/**
 *  property
 */
typedef struct {

    int dataFlag;

    float totalElectricity;

    float forwardElectricity;

    float reverseElectricity;

    float voltage;

    float current;

    float activePower;

    float frequency;

    float powerFactor;

    unsigned char state;

}SingleElectricityMeterSt;


/**
 * 单相电表
 */
class ElectricityMeterDev : public Device
{
public:
    ElectricityMeterDev(DeviceParam &device);
    ~ElectricityMeterDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    SingleElectricityMeterSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //上报低电压事件
    int PostLowPowerEvent();

    //上报报警事件
    int PostAlarmEvent();
    // 解析设备数据
    int UnpackSensorData(string &data);
};



#endif //GATEWAY_IOT_ELECTRICITY_METER_DEV_H
