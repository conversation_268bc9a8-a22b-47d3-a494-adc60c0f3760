//
// Created by seashell on 6/30/21.
//

#include "soil_temperature_dev.h"
#include <iostream>
#include <device_interface.h>




SoilTemperatureDev::SoilTemperatureDev(DeviceParam &device) : Device(device)
{
}


SoilTemperatureDev::~SoilTemperatureDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SoilTemperatureDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        LOG(ERROR) << "OnRxPlatformMessage topic:" << topic;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SoilTemperatureDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int SoilTemperatureDev::UnpackSensorData(string &data)
{

    SoilTHRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SoilTHRxSt));
    mData.humidity = ntohs(st.humidity)/10.0;
    mData.temperature = ntohs(st.temperature)/10.0;
    mData.electricQuantity = 31;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int SoilTemperatureDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SoilTemperatureDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "temperature"){
            res["temperature"] = mData.temperature;
        } else if (i.asString() == "humidity"){
            res["humidity"] = mData.humidity;
        }else if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }
        else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SoilTemperatureDev::PostProperty()
{
    Json::Value v;
    v["temperature"] = mData.temperature;
    v["humidity"] = mData.humidity;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

