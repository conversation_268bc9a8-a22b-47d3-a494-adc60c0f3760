//
// Created by seashell on 6/30/21.
//

#ifndef GATEWAY_IOT_SOIL_PH_DEV_H
#define GATEWAY_IOT_SOIL_PH_DEV_H


#include <cstdlib>


#include "device.h"

#if USE_AD_SENSOR
/**
 * property
 */
typedef struct {
    //传感器类型
    unsigned char type;
    //帧类型
    unsigned char frameType;
    //电池电压，上报的数据除以10
    unsigned char voltage;
    //酸碱度
    short ph;
}__attribute__((packed)) SoilPHRxSt;

#else
typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //酸碱度
    short ph;
    //crc
    unsigned char crc;
}__attribute__((packed))  SoilPHRxSt;
#endif

typedef struct {
    float electricQuantity;
    float ph;
    int state;
    //float voltage_threshold;
    //float ph_threshold;
}SoilPHSt;

class SoilPHDev : public Device
{
public:
    SoilPHDev(DeviceParam &device);
    ~SoilPHDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    SoilPHSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int PostLowPowerEvent();

    //上报酸碱度过低事件
    int PostPhLowEvent();

    //上报酸碱度过高事件
    int PostPhHighEvent();

    //解析设备数据
    int UnpackSensorData(string &data);
};

#endif //GATEWAY_IOT_SOIL_PH_DEV_H
