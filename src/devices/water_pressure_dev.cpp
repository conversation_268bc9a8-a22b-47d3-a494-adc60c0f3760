//
// Created by seashell on 6/6/22.
//
// tongke
#include "water_pressure_dev.h"




WaterPressureDev::WaterPressureDev(DeviceParam &device) : Device(device)
{
}


WaterPressureDev::~WaterPressureDev()
{
}


/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WaterPressureDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WaterPressureDev::OnRxSensorMessage(string &data)
{
    LOG(INFO) << "data=" << data;
    UnpackSensorData(data);
    PostProperty();

#if 0
    if (mData.press_value < mData.press_threshold_min ){
        Json::Value v;
        v["press_alarm_status"] = 1;
        v["limit_value"] = mData.press_threshold_min;
        v["press_value"] = mData.press_value;
        DeviceHelper::getInstance().reportDevEvent(this, TOPIC_E_PRESS_ALARM, v);
    }

    if (mData.press_value > mData.press_threshold_max ){
        Json::Value v;
        v["press_alarm_status"] = 0;
        v["limit_value"] = mData.press_threshold_max;
        v["press_value"] = mData.press_value;
        DeviceHelper::getInstance().reportDevEvent(this, TOPIC_E_PRESS_ALARM, v);
    }
#endif
    return true;
}

/**
 * 解析设备数据
 * @param data
 * @return
 */
int WaterPressureDev::UnpackSensorData(string &data)
{
    int i = 0;
    char sensorData[256] = {0};
    memcpy((void *)&sensorData,data.c_str(),data.length());

    while (i < data.length()) {
        // 读取标签
        int tag = sensorData[i];
        i += 1;

        // 读取值
        if (tag == 0x00 ){
            short devInfo = 0;
            memcpy((void *)&devInfo, (const void *)&sensorData[i], 2);
            mData.electricQuantity  = devInfo & 0x1f;
            i += 2;
        }
            //
        else if (tag == 0x04 ){
            memcpy((void *)&mData.temperature, (const void *)&sensorData[i], 2);
            mData.temperature = ntohs(mData.temperature);
            i += 2;
        }
            //
        else if (tag == 0x07 ){

            memcpy((void *)&mData.pressure, (const void *)&sensorData[i], 4);
            mData.pressure = ntohl(mData.pressure);
            i += 4;
        }
            //
        else if (tag == 0x81 ) {
            //device config
            int length = sensorData[i];
            i += 1;

            int reportCycle = 0;
            int detectCycle = 0;
            int calibrationValue = 0;
            if (length == 2) {

                memcpy((void *) &reportCycle, (const void *) &sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
            } else if (length == 4) {
                memcpy((void *) &reportCycle, (const void *) &sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *) &detectCycle, (const void *) &sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
            } else if (length == 8) {
                memcpy((void *) &reportCycle, (const void *) &sensorData[i], 2);
                reportCycle = ntohs(reportCycle);
                i += 2;
                memcpy((void *) &detectCycle, (const void *) &sensorData[i], 2);
                detectCycle = ntohs(detectCycle);
                i += 2;
                memcpy((void *) &calibrationValue, (const void *) &sensorData[i], 4);
                calibrationValue = ntohl(calibrationValue);
                i += 4;
            }
        }
    }

    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int WaterPressureDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WaterPressureDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == "pressure"){
            res["pressure"] = mData.pressure;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WaterPressureDev::PostProperty() {
    Json::Value v;
    v["pressure"] = mData.pressure;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
