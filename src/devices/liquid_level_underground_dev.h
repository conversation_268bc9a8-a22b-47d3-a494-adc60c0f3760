/**
 * 井下液位传感器
 */

#ifndef GATEWAY_IOT_LIQUID_LEVEL_UNDERGROUND_DEV_H
#define GATEWAY_IOT_LIQUID_LEVEL_UNDERGROUND_DEV_H


#include "device.h"


#if USE_AD_SENSOR

typedef struct {

    //传感器类型
    unsigned char type;

    //协议版本
    unsigned char version;

    //帧类型 0x80心跳 0x85上报
    unsigned char frameType;

    //帧序号
    unsigned char frameId;

    //水浸状态 0无水浸 1-255有水浸
    unsigned char flooding_state;

    //电池电压 需除以10
    unsigned char voltage;

    //水浸持续时间，两次上报周期内，浸水时长 单位：秒
    unsigned int time;
}__attribute__((packed))  LiquidLevelUndergroundRxSt;


#else
typedef struct {

    //消息头
    MessageHeader header;

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    unsigned char state;

    //数据
    unsigned short flooding_state;

    //第 3 个字节，最高位为 0 表示电压正常，为
    //1 表示电压低，低 7 位表示电压值，实际值
    //需除以 10，如 0x21 表示 3.3V，电压正常，
    //0x9F 表示 3.1V，电压低
    //unsigned char voltage_info;


    //crc
    unsigned char crc;

}__attribute__((packed))  LiquidLevelUndergroundRxSt;
#endif

typedef struct {
    unsigned int flooding_state;
    float battery_voltage;
}LiquidLevelUndergroundSt;


/**
 * 井下液位传感器
 */
class LiquidLevelUndergroundDev : Device{
public:
    LiquidLevelUndergroundDev(DeviceParam &device);
    ~LiquidLevelUndergroundDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    LiquidLevelUndergroundSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_LIQUID_LEVEL_UNDERGROUND_DEV_H
