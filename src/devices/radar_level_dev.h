/**
 * 雷达液位监测器
 */

#ifndef GATEWAY_IOT_RADAR_LEVEL_DEV_H
#define GATEWAY_IOT_RADAR_LEVEL_DEV_H

#include <cstdlib>
#include "device.h"


#if USE_AD_SENSOR
/**
 * property
 */
typedef struct {
    //传感器类型
    unsigned char type;
    //帧类型
    unsigned char frameType;
    //电池电压，上报的数据除以10
    unsigned char voltage;
    /**/
    short water_level;
}__attribute__((packed)) RadarLevelRxSt;
#else
typedef struct {
    //消息头
    MessageHeader header;

    //设备状态 0正常
    unsigned char state;
    //雷达液位
    short water_level;
    //crc
    unsigned char crc;
}__attribute__((packed))  RadarLevelRxSt;
#endif

typedef struct {
    //电压
    float battery_voltage;
    //液位
    float water_level;

}RadarLevelSt;


/**
 * 雷达液位监测器
 */
class RadarLevelDev : public Device
{
public:
    RadarLevelDev(DeviceParam &device);
    ~RadarLevelDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    RadarLevelSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int postLowPowerEvent();

    //解压传感器数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_RADAR_LEVEL_DEV_H
