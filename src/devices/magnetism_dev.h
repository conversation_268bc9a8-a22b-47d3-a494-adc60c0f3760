/**
 * 门磁传感器
 */

#ifndef GATEWAY_IOT_MAGNETISM_DEV_H
#define GATEWAY_IOT_MAGNETISM_DEV_H

#include "device.h"



typedef struct {

    //通道号 06 ff
    unsigned char channel;

    //帧类型
    // 0x0  门磁检测
    // 0x01 协议版本
    // 0x03 设置上报周期
    // 0x08 设备序列号
    // 0x09 硬件版本号
    // 0x0a 固件版本
    // 0x0f 类型为CLASS A
    unsigned char type;

    //数据示例 00 01
    unsigned char state;
}__attribute__((packed)) MagnetismRxSt;

typedef struct {

    //通道号 01 03 04
    unsigned char channel1;

    //类型
    unsigned char type1;

    //数据示例 00 01
    unsigned char state1;

    //通道号 01 03 04
    unsigned char channel2;

    //类型
    unsigned char type2;

    //数据示例 00 01
    unsigned char state2;


    //通道号 01 03 04
    unsigned char channel3;

    //类型
    unsigned char type3;

    //数据示例 00 01
    unsigned char state3;
}__attribute__((packed)) MagnetismRxCycleSt;


typedef struct {

    //门磁状态 0未触发 1触发
    int state;

    int electricQuantity;

    int remove;

}MagnetismSt;






/**
 * 门磁传感器
 */
class MagnetismDev : Device{
public:
    MagnetismDev(DeviceParam &device);
    ~MagnetismDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    MagnetismSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_MAGNETISM_DEV_H
