//
// Created by seashell on 6/6/22.
//

#include "water_ph_dev.h"


WaterPhDev::WaterPhDev(DeviceParam &device) : Device(device)
{

}




WaterPhDev::~WaterPhDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool WaterPhDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool WaterPhDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int WaterPhDev::UnpackSensorData(string &data)
{
    WaterPhRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(WaterPhRxSt));
    mData.electricQuantity = st.electricQuantity  & 0x1f;
    mData.ph = st.ph;

    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int WaterPhDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int WaterPhDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        string key = i.asString();
        if (key == "ph"){
            res["ph"] = mData.ph;
        }  else if (key == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int WaterPhDev::PostProperty()
{
    Json::Value v;
    v["ph"] = mData.ph;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
