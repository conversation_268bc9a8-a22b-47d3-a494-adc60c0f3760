//
// Created by seashell on 7/2/21.
//

#include <device_interface.h>
#include "rainfall_dev.h"


RainfallDev::RainfallDev(DeviceParam &device) : Device(device)
{
}

RainfallDev::~RainfallDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool RainfallDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool RainfallDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int RainfallDev::UnpackSensorData(string &data)
{
    RainfallRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(RainfallRxSt));
    mData.rainfall = ntohs(st.rainfall)/10.0;
    mData.electricQuantity = 31;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int RainfallDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int RainfallDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "rainfall"){
            res["rainfall"] = mData.rainfall;
        } else if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int RainfallDev::PostProperty()
{
    Json::Value v;
    v["rainfall"] = mData.rainfall;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
