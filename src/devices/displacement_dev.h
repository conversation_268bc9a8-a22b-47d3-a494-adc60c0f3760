#ifndef GATEWAY_IOT_DISPLACEMENT_DEV_H
#define GATEWAY_IOT_DISPLACEMENT_DEV_H
#include "device.h"


/**
 * 设备原生数据
 */
typedef struct {
    //1 dismantle 0 safe
    unsigned char Dismantle;
    //1 move 0 static
    unsigned char gravityState;
    //1 charge 0 no charge
    unsigned char chargeState;
    //bit 1 1 normal 0 low voltage
    unsigned char voltageState;
}__attribute__((packed))  DisplacementRxSt;


/**
 * smoke property
 */
typedef struct {
    //电池电压，上报的数据除以10
    float voltage;

    //是否被拆除
    bool isDismantle;

}DisplacementSt;


/**
 * 位移传感器
 *
 */
class DisplacementDdev : public Device
{
public:
    DisplacementDdev(DeviceParam &device);
    ~DisplacementDdev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    DisplacementSt mData;

    /**
     * 设置属性
     * @param req
     * @return
     */
    int SetProperty(Json::Value &req);

    /**
     * 获取属性
     * @param req
     * @param res
     * @return
     */
    int GetProperty(Json::Value &req, Json::Value &res);

    /**
     * 属性上报
     * @return
     */
    int PostProperty();

    /**
     * 解析数据
     * @param data
     * @return
     */
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_DISPLACEMENT_DEV_H
