/**
 * 一键报警传感器
 */

#ifndef GATEWAY_IOT_SOS_DEV_H
#define GATEWAY_IOT_SOS_DEV_H

#include "device.h"

typedef struct {

    //传感器类型 0x01
    unsigned char type;

    //帧类型 0x00心跳帧 0x01数据帧
    unsigned char frameType;

    //告警类型 0x00没有告警  0x01有告警 0x02有告警延时上报
    unsigned char alarmType;

    //0未拆 1被拆
    unsigned char anti_theft_state;

    //帧类型 0x0传感器在线
    unsigned char voltage_info;
}__attribute__((packed)) SOSRxSt;


typedef struct {

    //告警类型 0x00没有告警  0x01有告警 0x02有告警延时上报
    unsigned char alarmType;

    //0未拆 1被拆
    unsigned char anti_theft_state;

    //电压
    float voltage;

    int voltage_state;
}SOSSt;


/**
 * 一键报警传感器
 */
class SOSDev : Device{
public:
    SOSDev(DeviceParam &device);
    ~SOSDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    SOSSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_SOS_DEV_H
