//
// Created by seashell on 12/6/21.
//

#include "device_helper.h"



/**
 * 发送消息到设备
 * @param base64Data
 * @return
 */
int DeviceHelper::SendMessageToSensor(Device *device, string &base64Data)
{
    return mInterface->SendMessageToSensor(device, base64Data);
}


/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int DeviceHelper::ReportDevEvent(ThingDevice *device, string event_name, Json::Value &data)
{
    return mInterface->ReportDevEvent(device, event_name, data);
}



/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int DeviceHelper::ReportPropEvent(ThingDevice *device, Json::Value &data)
{
    return mInterface->ReportPropEvent(device, data);
}


/**
 * 发送设备数据，需外部实现该接口
 * @param topic
 * @param payload
 * @return
 */
int DeviceHelper::ReportDevVersion(ThingDevice *device, string &version)
{
    return mInterface->ReportDevVersion(device, version);
}


/**
 * 上报设备升级进度
 * @param device
 * @param step
 * @param desc
 * @return
 */
int DeviceHelper::ReportOTAProgress(ThingDevice *device, string &step, string &desc)
{
    return mInterface->ReportDevOTAProgress(device, step, desc);
}

/**
 * 订阅设备主题接口，需外部实现该接口
 * @param mid       MQTT消息发送id，MQTT返回
 * @param topic     MQTT主题
 * @return
 */
int DeviceHelper::ResponseToPlatform(ThingDevice *device, string &msgId, int code, string &topic, Json::Value &value)
{
    return mInterface->ResponseToPlatform(device, msgId, code, topic, value);
}



void DeviceHelper::SetDeviceInterface(DeviceInterface *impl)
{
    mInterface = impl;
}

/**
 * 计划任务
 * @param deviceName
 * @param period
 * @param single 是否一次性
 */
void DeviceHelper::ScheduleWork(string deviceName, int period, bool single)
{
    mInterface->ScheduleWork(deviceName, period, single);
}
