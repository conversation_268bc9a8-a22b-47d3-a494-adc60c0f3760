/**
 * 声光报警器
 */

#ifndef GATEWAY_IOT_SL_ALARM_DEV_H
#define GATEWAY_IOT_SL_ALARM_DEV_H

#include "device.h"


typedef struct {
    //传感器类型固定 0x9
    unsigned char type;

    //帧类型 0x0传感器在线
    unsigned char state;
}__attribute__((packed)) SLAlarmRxSt;


typedef struct {
    //门磁状态 0未触发 1触发
    int state;
}SLAlarmSt;

/**
 * 声光报警器
 */
class SLAlarmDev : Device{
public:
    SLAlarmDev(DeviceParam &device);
    ~SLAlarmDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    SLAlarmSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //解析设备数据
    int UnpackSensorData(string &data);
};


#endif //GATEWAY_IOT_SL_ALARM_DEV_H
