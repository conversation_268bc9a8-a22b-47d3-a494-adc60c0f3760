//
// Created by seashell on 6/30/21.
//

#include "hydrant_dev.h"
#include <iostream>
#include <cstring>
#include <device_interface.h>
#include <cmath>
#include <iostream>
#include <iomanip>


HydrantDev::HydrantDev(DeviceParam &device) : Device(device)
{
}

HydrantDev::~HydrantDev()
{
}


/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool HydrantDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }

    if (!res.isNull())
    {
        return RET_OK == DeviceHelper::getInstance().ResponseToPlatform(this,
                                                               message_id,
                                                               result,
                                                               strTopic.append("_res"),
                                                               res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool HydrantDev::OnRxSensorMessage(string &data)
{
    LOG(INFO) << "HydrantDev::OnRxSensorMessage data=" << data;
    UnpackSensorData(data);
    PostProperty();

    if (mData.alarmType != 0)
    {
        PostPressureAlarmEvent();
    }
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int HydrantDev::UnpackSensorData(string &data)
{
    if (data.length() != sizeof(HydrantRxSt)){
        LOG(ERROR) << "hydrant UnpackSensorData error:" << data <<endl;
        return -1;
    }
    HydrantRxSt st = {0};
    memcpy(&st,data.c_str(),sizeof(HydrantRxSt));

    mData.electricQuantity = st.batteryLevel;
    mData.signalStrength = st.signalStrength;

    // Extract each byte from the 4-byte data
    uint8_t byte1 = (st.sampleData >> 24) & 0xFF;
    uint8_t byte2 = (st.sampleData >> 16) & 0xFF;
    uint8_t byte3 = (st.sampleData >> 8) & 0xFF;
    uint8_t byte4 = st.sampleData & 0xFF;

    // Extract information from byte1
    uint8_t status = (byte1 >> 4) & 0x0F;
    uint8_t unit = byte1 & 0x0F;

    // Extract information from byte2
    uint8_t decimalPosition = (byte2 >> 4) & 0x0F;
    uint8_t firstDigit = byte2 & 0x0F;

    // Extract digits from byte3 and byte4
    uint8_t secondDigit = (byte3 >> 4) & 0x0F;
    uint8_t thirdDigit = byte3 & 0x0F;
    uint8_t fourthDigit = (byte4 >> 4) & 0x0F;
    uint8_t fifthDigit = byte4 & 0x0F;

    // Print status and unit
    std::cout << "Status: " << static_cast<int>(status) << "\n";
    std::cout << "Unit: " << static_cast<int>(unit) << "\n";

    // Handle temperature special case
    double value;
    if (unit == 5) {
        bool isNegative = firstDigit == 1;
        std::cout << "Temperature: " << (isNegative ? "-" : "+");
        std::cout << static_cast<int>(secondDigit) << static_cast<int>(thirdDigit) << "." << static_cast<int>(fourthDigit) << static_cast<int>(fifthDigit) << " °C" << "\n";
    } else {
        // Calculate and print the value based on the digits and decimal position
        value = firstDigit * 10000 + secondDigit * 1000 + thirdDigit * 100 + fourthDigit * 10 + fifthDigit;
        value /= pow(10, decimalPosition);
        std::cout << "Value: " << std::fixed << std::setprecision(decimalPosition) << value << "\n";

    }

    //unit
    //低四位表示传输数据的单位
    // 1：表示压力 MPa，
    // 2：表示压力 Bar，
    // 3：表示压力 KPa，
    // 4：表示液位 M；
    // 5：表示温度℃；
    // 6：表示流量 m³/h；
    // 7：表示角度°。

    //status
    //0：表示数据正常；
    // 1：表示数据阈值下限告警；
    // 2：表示数据阈值上限
    //告警，
    // 3：表示设备故障；
    // 4：表示数据动态变化阈值告警；
    // 5：表示碰撞告警；
    // 6：表示倾斜告警；
    // 7：表示水流告警；
    // 8：表示进水告警；
    // 9：表示低电量告警；
    // 10：开盖告警；
    // 11：阀门异常。
    if (status == 0){
        //data is normal
        if (unit == 1){
            mData.pressure = value;
        }

    } else if(status == 1){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "LowThresholdWarningType", v);
    } else if(status == 2){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "HighThresholdWarningType", v);

    } else if(status == 3){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "DeviceFaultType", v);

    } else if(status == 4){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "DynamicThresholdWarningType", v);

    } else if(status == 5){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "CollisionWarningType", v);

    } else if(status == 6){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "TiltWarningType", v);

    } else if(status == 7){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "WaterFlowWarningType", v);

    } else if(status == 8){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "WaterIngressWarningType", v);

    } else if(status == 9){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "LowBatteryWarningType", v);

    } else if(status == 10){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "CoverOpenWarningType", v);
    } else if(status == 11){
        Json::Value v;
        v["reserve"] = 1;
        DeviceHelper::getInstance().ReportDevEvent(this, "ValveAbnormalType", v);
    }



    return 0;
}


/**
 * 设置属性
 * @param jValue
 * @return
 */
int HydrantDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int HydrantDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == "pressure"){
            res["pressure"] = mData.pressure;
        } else if (i.asString() == "signalStrength"){
            res["signalStrength"] = mData.pressure;
        } else if (i.asString() == "electricQuantity"){
            res["electricQuantity"] = mData.electricQuantity;
        }else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int HydrantDev::PostProperty()
{
    LOG(ERROR) << "###################### signalStrength:" << mData.signalStrength ;
    Json::Value v;
    v["pressure"] = mData.pressure;
    v["signalStrength"] = mData.signalStrength;
    v["electricQuantity"] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}






/**
 * 水压事件上报
 * @return
 */
int HydrantDev::PostPressureAlarmEvent()
{
    Json::Value v;
    v["pressure"] = mData.pressure;

    if (mData.alarmType == 1){
        return DeviceHelper::getInstance().ReportDevEvent(this, "low_pressure", v);
    } else if (mData.alarmType == 2){
        return DeviceHelper::getInstance().ReportDevEvent(this, "high_pressure", v);
    }
    return RET_OK;
}


