/**
 * 物理设备数据协议
 */

#ifndef GATEWAY_IOT_LORA_NODE_H
#define GATEWAY_IOT_LORA_NODE_H


#include <string>
using namespace std;

#if 0
enum DeviceType
{

    DEV_UNKNOWN = 0,            /*未知*/
    DEV_GATEWAY = 1,            /*hi3516IOT*/
    DEV_SOS = 2,                /*一键报警器*/
    DEV_MANHOLE_COVER = 3 ,     /*井盖*/
    DEV_DISPLACEMENT = 4,       /*位移*/
    DEV_LAMP_SWITCH = 5,        /*灯控制器*/
    DEV_SL_ALARM = 6,           /*声光报警器 */
    DEV_TH = 7,                 /*温湿度报警器*/
    DEV_SMOKE = 8,              /*烟雾*/
    DEV_INFRARED = 9,           /*人体红外*/
    DEV_MAGNETISM = 10,         /*门磁*/
    DEV_WEATHER_STATION = 11,   /*综合气象*/
    DEV_RAINFALL = 12,          /*雨量*/
    DEV_WIND_DIREC = 13,        /*风向*/
    DEV_WIND_SPEED = 14,        /*风速*/
    DEV_WATER_PRESSURE = 15,    /*水压*/
    DEV_SOIL_PH = 16,           /*土壤ph*/
    DEV_SOIL_TH = 17,           /*土壤温湿度*/
    DEV_LEVEL = 18,             /*液位*/
    DEV_WATER_IMMERSION = 19,   /*水浸*/
    DEV_WATER_PH = 20,          /*水质PH*/
    DEV_LEVEL_UNDERGROUND = 21, /*井下液位*/
    DEV_HYDRANT = 22,           /*消防栓*/

    DEV_RADAR_LIGHT = 60,       /*雷达灯 0x20*/
    DEV_TRANSPARENT = 61,       /*透传模块 0x20*/


};

#else
enum DeviceType
{
    DEV_UNKNOWN = -1,                   /*未知*/
    DEV_GATEWAY = 0,                    /*hi3516IOT*/
    DEV_WIND_SPEED = 1,                 /*风速*/
    DEV_WIND_DIREC = 2,                 /*风向*/
    DEV_SOIL_PH = 3,                    /*土壤ph*/
    DEV_SOIL_TH = 4,                    /*土壤温湿度*/
    DEV_RADAR_LEVEL = 5,                /*超声液位*/
    DEV_LEVEL = 6,                      /*液位*/
    DEV_MANHOLE_COVER = 7,              /*井盖*/
    DEV_WATER_IMMERSION = 8,            /*水浸*/
    DEV_RAINFALL = 9,                   /*雨量*/
    DEV_WATER_PRESSURE = 10,            /*水压*/
    DEV_WEATHER_STATION = 11,           /*综合气象*/
    DEV_WATER_PH = 12,                  /*水质PH*/
    DEV_LEVEL_UNDERGROUND = 13,         /*井下液位*/
    DEV_SOS = 14,                       /*一键报警器*/
    DEV_DISPLACEMENT = 15,              /*位移*/
    DEV_LAMP_SWITCH = 16,               /*灯控制器*/
    DEV_SL_ALARM = 17,                  /*声光报警器 */
    DEV_TH = 18,                        /*温湿度报警器*/
    DEV_SMOKE = 19,                     /*烟雾*/
    DEV_INFRARED = 20,                  /*人体红外*/
    DEV_MAGNETISM = 21,                 /*门磁*/
    DEV_WATER_METER = 22,               //水表
    DEV_ELECTRICITY_METER = 23,         //电表
    DEV_HYDRANT = 24,                   /*消防栓*/
    //水泵启停 25
    DEV_RADAR_LIGHT = 26,               /*雷达灯 0x1a*/
    DEV_TRANSPARENT = 27,               /*透传模块 0x1b*/
    DEV_SOLENOID_VALVE = 34,            //电磁阀
    DEV_THREE_ELECTRICITY_METER = 35,   //三相电表
    DEV_FX_THEFTPROOF = 36,        //灭火器防盗

};
#endif


/**
 * 钰辰物联网协议数据头
 */
typedef struct {
    //前导码
    unsigned char preamble;
    //传感器厂商
    unsigned char company;
    //传感器类型
    unsigned char type;
    //命令
    unsigned char cmd;
    //数据长度
    unsigned char length;

}__attribute__((packed)) MessageHeader;


/**
 * 0x33设置传感baud rate
 */
typedef struct {
    //消息头
    MessageHeader header;
    /*baud rate
     1:2400
     2:4800
     3:9600
     4:19200
     5:38400
     6:57600
     7:76800
     8:115200
     */
    unsigned char baudRateFlag;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetDevBaudRateSt;

/**
 * 0x35设置传感器类型
 */
typedef struct {
    //消息头
    MessageHeader header;
    //设备状态 0正常
    unsigned char devType;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetDevTypeSt;



/**
 * 0x36
 */
typedef struct {
    //消息头
    MessageHeader header;
    //分钟
    unsigned short period;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetDevReportPeriodSt;



/**
 * 0x38设置传感报警阈值
 */
typedef struct {
    //消息头
    MessageHeader header;
    //阈值
    unsigned short thresholdH;
    //阈值
    unsigned short thresholdL;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetDevReportThresholdSt;


/**
 * 0x51 恢复出厂设置
 */
typedef struct {
    //消息头
    MessageHeader header;
    //是否保留DEVEUI 0x0 完全恢复出厂设置 0x1 DEVEUI保留不变
    unsigned char reserveEUI;
    //crc
    unsigned char crc;
}__attribute__((packed)) SetDevResetSt;





/**
 * 查询设备数据
 */
typedef struct {
    //消息头
    MessageHeader header;
    //crc
    unsigned char crc;
}__attribute__((packed)) GetDevDataSt;







/**
 * 设置波特率
 * @param devType
 * @param baudFlag
     1:2400
     2:4800
     3:9600
     4:19200
     5:38400
     6:57600
     7:76800
     8:115200
 * @param outBase64Str
 * @return
 */
int SetDevBaudRate(int devType, int baudRateFlag,string &outBase64Str);

/**
 * 设置传感器类型
 * @return
 */
int SetDevType(int devType,int newDevType, string &outBase64Str);


/**
 * 设置传感器上报周期
 * @return
 */
int SetDevReportPeriod(int devType,int period, string &outBase64Str);

/**
 * 设置传感器报警阈值
 * @param threshold
 * @param outBase64Str
 * @return
 */
int SetDevReportThreshold(int devType,int threshold, string &outBase64Str);

/**
 * 设置传感器恢复出厂
 * @param reserveEUI 0x0不保留，0x1保留DEVEUI
 * @param outBase64Str
 * @return
 */
int SetDevReset(int devType,int reserveEUI, string &outBase64Str);




/****************************************************查询****************************************************************/
/**
 * 查询
 * @param devType
 * @param cmd
 * 0x97 查询RS485 波特率
 * 0x99 查询传感器类型
 * 0x9A 查询上报周期
 * 0x9B 查询传感器报警阈值
 * 0x9C 查询达到报警阈值后，更新上传的偏差值
 * 0xB1 查询软件版本
 * 0xB2 查询硬件版本
 * @param outBase64Str
 * @return
 */
int GetBaudRate(int devType,int cmd,string &outBase64Str);




#endif //GATEWAY_IOT_LORA_NODE_H
