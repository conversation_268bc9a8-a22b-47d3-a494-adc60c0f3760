//
// Created by seashell on 6/30/21.
//

#ifndef GATEWAY_IOT_SOIL_TEMPERATURE_DEV_H
#define GATEWAY_IOT_SOIL_TEMPERATURE_DEV_H

#include <cstdlib>
#include "device.h"




typedef struct {

    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //temperature
    short temperature;

    //humidity
    short humidity;

    //crc
    unsigned short crc;
}__attribute__((packed))  SoilTHRxSt;






typedef struct {
    //电池电压，上报的数据除以10
    int electricQuantity;
    //温度
    float temperature;
    //湿度
    float humidity;
    //温度阈值
    float temperature_threshold;
    //湿度阈值
    float humidity_threshold;

    float voltage_threshold;;

}SoilTHSt;

class SoilTemperatureDev : public Device
{
public:
    SoilTemperatureDev(DeviceParam &device);
    ~SoilTemperatureDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    SoilTHSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int postLowPowerEvent();
    int postTempEvent(string e);
    int postHumidityEvent(string e);
    int UnpackSensorData(string &data);
};

#endif //GATEWAY_IOT_SOIL_TEMPERATURE_DEV_H
