//
// Created by seashell on 7/28/21.
//

#include <common_func.h>
#include "lora_node.h"
#include "thing_server.h"
#if 0

//auto:VQAgNQEAQQ==
    //manual:VQAgNQEBQA==
    SetLightSt setl = {0};
    setl.header.preamble = 0x55;
    setl.header.company = 0x0;
    setl.header.type = 0x20;
    setl.header.cmd = 0x35;
    setl.header.length = 0x1;
    setl.value = 0;
    setl.crc = calXor((u8 *) &setl, sizeof(SetLightSt) - 1);
    string data = base64Encode((char *) &setl, sizeof(SetLightSt));
    std::cout <<"auto:"<< data <<endl;

    SetLightSt setl2 = {0};
    setl2.header.preamble = 0x55;
    setl2.header.company = 0x0;
    setl2.header.type = 0x20;
    setl2.header.cmd = 0x35;
    setl2.header.length = 0x1;
    setl2.value = 1;
    setl2.crc = calXor((u8 *) &setl2, sizeof(SetLightSt) - 1);
    string dataa = base64Encode((char *) &setl2, sizeof(SetLightSt));
    std::cout <<"manual:"<< dataa <<endl;

    SetLightSt set3 = {0};
    set3.header.preamble = 0x55;
    set3.header.company = 0x0;
    set3.header.type = 0x20;
    set3.header.cmd = 0x65;
    set3.header.length = 0x1;
    set3.value = 0;
    set3.crc = calXor((u8 *) &set3, sizeof(SetLightSt) - 1);
    string data3 = base64Encode((char *) &set3, sizeof(SetLightSt));
    std::cout << "close:" <<data3 <<endl;


    SetLightSt set6 = {0};
    set6.header.preamble = 0x55;
    set6.header.company = 0x0;
    set6.header.type = 0x20;
    set6.header.cmd = 0x65;
    set6.header.length = 0x1;
    set6.value = 30;
    set6.crc = calXor((u8 *) &set3, sizeof(SetLightSt) - 1);
    string data6 = base64Encode((char *) &set3, sizeof(SetLightSt));
    std::cout << "open:" <<data6 <<endl;

    SetLightSt set4 = {0};
    set4.header.preamble = 0x55;
    set4.header.company = 0x0;
    set4.header.type = 0x20;
    set4.header.cmd = 0x33;
    set4.header.length = 0x1;
    set4.value = 0;
    set4.crc = calXor((u8 *) &set4, sizeof(SetLightSt) - 1);
    string data4 = base64Encode((char *) &set4, sizeof(SetLightSt));
    std::cout <<"min brightness:" << data4 <<endl;

    SetLightSt set5 = {0};
    set5.header.preamble = 0x55;
    set5.header.company = 0x0;
    set5.header.type = 0x20;
    set5.header.cmd = 0x34;
    set5.header.length = 0x1;
    set5.value = 100;
    set5.crc = calXor((u8 *) &set5, sizeof(SetLightSt) - 1);
    string data5 = base64Encode((char *) &set5, sizeof(SetLightSt));
    std::cout <<"max brightness:" << data5 <<endl;

    SetDevTypeSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x1;
    set.header.type = 0x1;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.devType = 1;
    set.crc = calXor((u8 *) &set, sizeof(SetDevTypeSt) - 1);




    char hello[250] = {0};
    memcpy(hello,(void *)&set,sizeof(SetDevTypeSt));
    string helloStr = hello;
    string downStr = (char*)&set;


    string base64Data = base64Encode(helloStr);
    std::cout << base64Data <<endl;
    string base64Data2 = base64Encode(downStr);
    std::cout << base64Data2 <<endl;

#endif
/****************************************************设置****************************************************************/
/**
 * 设置波特率
 * @param devType
 * @param baudFlag
     1:2400
     2:4800
     3:9600
     4:19200
     5:38400
     6:57600
     7:76800
     8:115200
 * @param outBase64Str
 * @return
 */
int SetDevBaudRate(int devType, int baudRateFlag,string &outBase64Str)
{
    SetDevBaudRateSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = devType;
    set.header.cmd = 0x33;
    set.header.length = 0x1;
    set.baudRateFlag = baudRateFlag;
    set.crc = calXor((unsigned char *) &set, sizeof(SetDevBaudRateSt) - 1);
    outBase64Str = Base64Encode((char *) &set, sizeof(SetDevBaudRateSt));
    return 0;
}


/**
 * 设置传感器类型
 * @return
 */
int SetDevType(int devType,int newDevType, string &outBase64Str)
{
    SetDevTypeSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = 0x1;
    set.header.cmd = 0x35;
    set.header.length = 0x1;
    set.devType = newDevType;
    set.crc = calXor((unsigned char *) &set, sizeof(SetDevTypeSt) - 1);
    outBase64Str = Base64Encode((char *) &set, sizeof(SetDevTypeSt));
    return 0;
}



/**
 * 设置传感器上报周期
 * @return
 */
int SetDevReportPeriod(int devType,int period, string &outBase64Str)
{
    SetDevReportPeriodSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = devType;
    set.header.cmd = 0x36;
    set.header.length = 0x2;
    set.period = period;
    set.crc = calXor((unsigned char *) &set, sizeof(SetDevReportPeriodSt) - 1);
    outBase64Str = Base64Encode((char *) &set, sizeof(SetDevReportPeriodSt));
    return 0;
}



/**
 * 设置传感器报警阈值
 * @param threshold
 * @param outBase64Str
 * @return
 */
int SetDevReportThreshold(int devType,int threshold, string &outBase64Str)
{
    SetDevReportThresholdSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = devType;
    set.header.cmd = 0x38;
    set.header.length = 0x4;
    //TODO 阈值
    set.thresholdH = threshold;
    set.thresholdL = threshold;
    set.crc = calXor((unsigned char *) &set, sizeof(SetDevReportThresholdSt) - 1);
    outBase64Str = Base64Encode((char *) &set, sizeof(SetDevReportThresholdSt));
    return 0;
}



/**
 * 设置传感器恢复出厂
 * @param reserveEUI 0x0不保留，0x1保留DEVEUI
 * @param outBase64Str
 * @return
 */
int SetDevReset(int devType,int reserveEUI, string &outBase64Str)
{
    SetDevResetSt set = {0};
    set.header.preamble = 0x55;
    set.header.company = 0x0;
    set.header.type = devType;
    set.header.cmd = 0x51;
    set.header.length = 0x4;
    set.reserveEUI = reserveEUI;
    set.crc = calXor((unsigned char *) &set, sizeof(SetDevResetSt) - 1);
    outBase64Str = Base64Encode((char *) &set, sizeof(SetDevResetSt));
    return 0;
}





/****************************************************查询****************************************************************/
/**
 * 查询
 * @param devType
 * @param cmd
 * 0x97 查询RS485 波特率
 * 0x99 查询传感器类型
 * 0x9A 查询上报周期
 * 0x9B 查询传感器报警阈值
 * 0x9C 查询达到报警阈值后，更新上传的偏差值
 * 0xB1 查询软件版本
 * 0xB2 查询硬件版本
 * @param outBase64Str
 * @return
 */
int GetBaudRate(int devType,int cmd,string &outBase64Str)
{
    GetDevDataSt get = {0};
    get.header.preamble = 0x55;
    get.header.company = 0x0;
    get.header.type = devType;
    get.header.cmd = 0x51;
    get.header.length = 0x4;
    get.crc = calXor((unsigned char *) &get, sizeof(GetDevDataSt) - 1);
    outBase64Str = Base64Encode((char *) &get, sizeof(GetDevDataSt));
    return 0;
}