/**
 *  基础设备类，继承于能力集AbilityDevice
 *
 */
#ifndef DEVICE_NODE_NODE_H
#define DEVICE_NODE_NODE_H

#include "thing_server.h"
#include <common_func.h>
#include "lora_node.h"
#include <netdb.h>
#include "device_helper.h"
using namespace std;


#define USE_AD_SENSOR   0
#define USE_SELF_SENSOR 0
#define USE_ZKX_SENSOR  1

class DeviceParam
{
public:
    string device_eui;          //eui
    string alias;               //名称
    string app_name;            //应用名称
    string company_id;          //厂商ID
    int dev_type;               //设备类型
    int f_port;                 //f_port
    string app_id;              //应用ID
    string app_eui;             //APP_EUI

    string device_name;         //等同deviceName
    string device_secret;       //deviceSecret
    string product_key;         //产品key
    string product_secret;      //产品秘钥
    string message_id;          //接收到的mID
    bool is_register = false;   //是否注册
    bool is_bind = false;       //是否绑定
    bool is_online = false;     //设备是否上线
    bool is_gateway = false;    //是否是网关设备

    string origin_data;         //原始数据

};


class Device : public ThingDevice
{
public:
    string device_eui;          //eui
    string alias;               //名称
    string app_name;            //应用名称
    int company_id;             //厂商ID
    int dev_type;               //设备类型
    int f_port;                 //f_port
    string app_id;              //应用ID
    string origin_data;          //原始数据
    bool use_base64_to_hex;     //使用base64转16进制


    Device(DeviceParam &device){
        this->origin_data = device.origin_data;
        this->use_base64_to_hex = false;
        this->product_key = device.product_key;
        this->product_secret = device.product_secret;
        this->device_name = device.device_name;
        this->device_secret = device.device_secret;
        this->is_gateway = device.is_gateway;
        this->is_bind = device.is_bind;
        this->is_register = device.is_register;
        this->is_online = device.is_online;

        this->f_port = device.f_port;
        this->app_id = device.app_id;
        this->dev_type = device.dev_type;
        this->device_eui = device.device_eui;

        string prefixThing = "$thing/";
        m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_PRO_SET);
        m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_RES_PRO_POST);
    };

#if 0
    //发送消息到设备
    int sendMessageToSensor(string &base64Data);

    /* 事件上报 */
    int reportDevEvent(string event_name, Json::Value &data);

    /* 属性上报 */
    int reportPropEvent(Json::Value &data);

    /*应答平台的设置属性、获取属性和服务调用指令*/
    int responseToPlatform(string &msgId, int code, string &topic, Json::Value &value, bool isLocalReq = false);
#endif
};


#endif //DEVICE_NODE_NODE_H
