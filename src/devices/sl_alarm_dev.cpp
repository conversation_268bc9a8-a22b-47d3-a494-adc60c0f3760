//
// Created by seashell on 6/6/22.
//

#include "sl_alarm_dev.h"


/***** 能力集属性 *****/

#define DEVICE_STATE    "state"

/***** 服务 *****/

#define TOPIC_S_RING    "/service/open_device/call"


SLAlarmDev::SLAlarmDev(DeviceParam &device) : Device(device)
{
    string prefixThing = "$thing/";
    m_topic.emplace_back(prefixThing + product_key + "/" + device_name + TOPIC_S_RING);
}




SLAlarmDev::~SLAlarmDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SLAlarmDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_S_RING)) //属性获取
    {
        //TODO ring alarm
        //result = setProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SLAlarmDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();

    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int SLAlarmDev::UnpackSensorData(string &data)
{
    SLAlarmRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(SLAlarmRxSt));
    mData.state = st.state;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int SLAlarmDev::SetProperty(Json::Value &req)
{

    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SLAlarmDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == DEVICE_STATE){
            res[DEVICE_STATE] = mData.state;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SLAlarmDev::PostProperty()
{
    Json::Value v;
    v[DEVICE_STATE] = mData.state;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
