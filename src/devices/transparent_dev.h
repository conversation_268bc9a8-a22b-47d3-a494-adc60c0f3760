/**
 * 透传模块
 */

#ifndef GATEWAY_IOT_TRANSPARENT_DEV_H
#define GATEWAY_IOT_TRANSPARENT_DEV_H


#include "device.h"


typedef struct {
    //消息头
    MessageHeader header;
    //设备状态 0正常
    unsigned char state;
    //风向
    unsigned short speed;
    //crc
    unsigned char crc;
}__attribute__((packed)) TransparentRxSt;

/**
 * smoke property
 */
typedef struct {
    //风向
    char buffer[2048];

}TransparentSt;

/**
 * 透传模块
 */
class TransparentDev : public Device
{
public:
    TransparentDev(DeviceParam &device);
    ~TransparentDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);


private:
    //主题

    //属性
    TransparentSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    //服务调用
    int UnpackSensorData(string &data);

};


#endif //GATEWAY_IOT_TRANSPARENT_DEV_H
