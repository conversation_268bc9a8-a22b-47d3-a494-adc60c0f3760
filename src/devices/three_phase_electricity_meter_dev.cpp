//
// Created by seashell on 7/9/22.
//

#include "three_phase_electricity_meter_dev.h"



/***** 主题 *****/



/***** 能力集属性 *****/


//x角度
#define X_ANGLE    "xangle"
//y角度
#define Y_ANGLE    "yangle"
//z角度
#define Z_ANGLE    "zangle"

//井盖翘起状态
#define MANHOLE_COVER_COCKED "manholeCoverCocked"

//井盖震动状态
#define VIBRATE "vibrate"

//井盖竖立状态
#define MANHOLE_COVE_ERECTION "manholeCoverErection"

//井盖翻起状态
#define MANHOLE_COVERS_TURNED_UP "manholeCoversTurnedUp"

//电池电压
#define BATTERY_VOLTAGE "battery_voltage"


/***** 事件 *****/
//低电压告警
#define EVENT_LOW_VOLTAGE "low_voltage"
//井盖告警
#define EVENT_ALARM "alarm"



ThreePhaseElectricityMeterDev::ThreePhaseElectricityMeterDev(DeviceParam &device) : Device(device)
{
}




ThreePhaseElectricityMeterDev::~ThreePhaseElectricityMeterDev()
{
}




/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool ThreePhaseElectricityMeterDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool ThreePhaseElectricityMeterDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int ThreePhaseElectricityMeterDev::UnpackSensorData(string &data)
{
    if (data.length() < sizeof(ThreeElectricityMeterRxHead)){
        LOG(ERROR) << "recv msg from three phase meter,but msg length too small";
    }

    ThreeElectricityMeterRxHead head = {0};
    memcpy((void *)&head,data.c_str(),sizeof(ThreeElectricityMeterRxHead));


    //1 packet
    if (head.dataFlag == 858993203)
    {

        ThreeElectricityMeterRxSt13 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt13));
        mData.totalElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) * 0.01;
        int v1 = bcd_decimal_code(st.jianElectricity) - bcd_decimal_code(0x33333333);
        int v2 = bcd_decimal_code(st.fengElectricity) - bcd_decimal_code(0x33333333);
        int v3 = bcd_decimal_code(st.pingElectricity) - bcd_decimal_code(0x33333333);
        int v4 = bcd_decimal_code(st.guElectricity) - bcd_decimal_code(0x33333333);

        Json::Value v;
        v["totalElectricity"] = mData.totalElectricity;
        return DeviceHelper::getInstance().ReportPropEvent(this, v);
    }
    else if(head.dataFlag == 859058739)//2
    {
        ThreeElectricityMeterRxSt13 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt13));
        mData.forwardElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) * 0.01;

        Json::Value v;
        v["forwardElectricity"] = mData.forwardElectricity;
        return DeviceHelper::getInstance().ReportPropEvent(this, v);
    }
    else if(head.dataFlag == 859124275)//3
    {
        ThreeElectricityMeterRxSt13 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt13));
        mData.reverseElectricity = (bcd_decimal_code(st.totalElectricity) - bcd_decimal_code(0x33333333)) *0.01;


        Json::Value v;
        v["reverseElectricity"] = mData.reverseElectricity;
        return DeviceHelper::getInstance().ReportPropEvent(this, v);

    }
    else if(head.dataFlag == 932398389)//4
    {

        ThreeElectricityMeterRxSt4 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt4));
        mData.totalPowerFactor = (bcd_decimal_code(st.totalPowerFactor) - bcd_decimal_code(0x3333)) * 0.001;


        int power = 0;
        memcpy((void *)&power, st.shunshiPower,3);
        mData.totalPower  = (bcd_decimal_code(power) - bcd_decimal_code(0x333333)) * 0.0001;


        int powerA = 0;
        memcpy((void *)&powerA, st.shunshiAPower,3);
        mData.powerA  = (bcd_decimal_code(powerA) - bcd_decimal_code(0x333333)) * 0.0001;

        int powerB = 0;
        memcpy((void *)&powerB, st.shunshiBPower,3);
        mData.powerB  = (bcd_decimal_code(powerB) - bcd_decimal_code(0x333333)) * 0.0001;

        int powerC = 0;
        memcpy((void *)&powerC, st.shunshiCPower,3);
        mData.powerC  = (bcd_decimal_code(powerC) - bcd_decimal_code(0x333333)) * 0.0001;


        Json::Value v;
        v["totalPower"] = mData.totalPower;
        v["powerA"] = mData.powerA;
        v["powerB"] = mData.powerB;
        v["powerC"] = mData.powerC;
        v["totalPowerFactor"] = mData.totalPowerFactor;

        return DeviceHelper::getInstance().ReportPropEvent(this, v);

    } else if(head.dataFlag == 932398388){

        ThreeElectricityMeterRxSt5 st = {0};
        memcpy((void *)&st,data.c_str(),sizeof(ThreeElectricityMeterRxSt5));
        mData.voltageA = (bcd_decimal_code(st.APhaseVoltage) - bcd_decimal_code(0x3333)) * 0.1;
        mData.voltageB = (bcd_decimal_code(st.BPhaseVoltage) - bcd_decimal_code(0x3333)) * 0.1;
        mData.voltageC = (bcd_decimal_code(st.CPhaseVoltage) - bcd_decimal_code(0x3333)) * 0.1;


        mData.currentA = (bcd_decimal_code(st.APhaseCurrent) - bcd_decimal_code(0x3333)) * 0.001;
        mData.currentB = (bcd_decimal_code(st.BPhaseCurrent) - bcd_decimal_code(0x3333)) * 0.001;
        mData.currentC = (bcd_decimal_code(st.CPhaseCurrent) - bcd_decimal_code(0x3333)) * 0.001;


        Json::Value v;
        v["voltageA"] = mData.voltageA;
        v["currentA"] = mData.currentA;
        v["voltageB"] = mData.voltageB;
        v["currentB"] = mData.currentB;
        v["voltageC"] = mData.voltageC;
        v["currentC"] = mData.currentC;

        return DeviceHelper::getInstance().ReportPropEvent(this, v);
    }


    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int ThreePhaseElectricityMeterDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int ThreePhaseElectricityMeterDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == BATTERY_VOLTAGE){
            //res[BATTERY_VOLTAGE] = mData.voltage;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int ThreePhaseElectricityMeterDev::PostProperty()
{


    Json::Value v;
   /* v[BATTERY_VOLTAGE] = mData.voltage;
    v[X_ANGLE] = mData.angleX;
    v[Y_ANGLE] = mData.angleY;
    v[Z_ANGLE] = mData.angleZ;
    v[MANHOLE_COVER_COCKED] = mData.manholeCoverCockedAlarm;
    v[VIBRATE] = mData.vibrateAndAlarm;
    v[MANHOLE_COVE_ERECTION] = mData.manholeCoverErectionAlarm;
    v[MANHOLE_COVERS_TURNED_UP] = mData.manholeCoversTurnedUp;*/

    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}



