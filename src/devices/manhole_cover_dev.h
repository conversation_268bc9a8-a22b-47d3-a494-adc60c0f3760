/**
 * 井盖监测器
 */

#ifndef GATEWAY_IOT_MANHOLE_COVER_DEV_H
#define GATEWAY_IOT_MANHOLE_COVER_DEV_H

#include "device.h"
#include <iostream>
#include <vector>
#include <cstdint>
#include <iomanip>
#include <sstream>

/**
 *  上报数据与心跳数据的负载
 */

#if 0
//feiqi old device,
typedef struct {

    //高度表示设备到液位 (或物体) 表面的距离； 2 个字节，16 进制发送，单位：毫米 (mm) 注意：410 井盖设备中， 由于不带高度检测功能， 因此该字节默认为 0x0000；
    unsigned short height;

    //是否携带经纬度表示数据中是否携带经纬度信息； 1 个字节；此字节内容为 0x00 或 0x01; 其中 0x00 表 示数据中不含经纬度信息；0x01 表示上传经纬度信
    unsigned char locationFlag;

    //经度
    //unsigned int  longitude;

    //纬度浮点型、单精度 ( IEEE-754 标准) ，高位在前，低位在后的顺序。 注意：如果经纬度选择位为0x00，不上传此纬度信息；
    //unsigned int latitude;

    //温度
    unsigned char temperature;

    //保留
    unsigned char reserve;

    //倾斜角度
    unsigned char tiltangle;

    // 表示空满、保留位、倾倒、 电量 4 种状态位； 共 2 个字节；各状态位取值为 0 或 1,0 表示报警 (异 常) 状态，1 表示正常状态；
    // 前字节高 4 位为空满状态位，低 4 位为保留位位，即 0x00 表示空，0x10 表示满溢， 同理，后字节一样，0x0X 表示未倾倒，0x1X 表示倾 倒报警；0xX0 表示电量正常，0xX1 表示低电量报警；
    // 注意：对于 410 井盖设备，由于不具有满空检测功 能，因此此处空满状态位默认为 0。
    unsigned char reserve1;

    // 0x0X 表示未倾倒，0x1X 表示倾 倒报警；0xX0 表示电量正常，0xX1 表示低电量报警；
    unsigned char state;
    //unsigned short state;

    //帧计数器
    unsigned short counter;

}__attribute__((packed)) ManholeCoverRxPayload;



/**
 * 数据上报包与心跳包
 */
typedef struct {

    //80 00 01 03 19 03 04 18  0A 1E 4B 1E 01 02 01 00   | ..........K.....
    //00 00 00 00 00 00 00 00  81
    //前导码  0x80
    unsigned char frameHead;

    //强制位  0x00
    unsigned char qiangzhi;

    //设备类型 0x01
    unsigned char devType;

    //消息类型 0x1主动上报 0x2心跳包 0x3下行回复确认包
    unsigned char msgType;

    //帧大小
    unsigned char frameLength;

    //
    ManholeCoverRxPayload report;

    //保留
    unsigned char retain;

    //帧尾
    unsigned char frameTail;

}__attribute__((packed)) ManholeCoverRxSt;




/**
 *  下行数据
 */
typedef struct {

    //前导码  0x80
    unsigned char frameHead;

    //0x02 下行参数配置
    unsigned char cmd;

    //payload head
    unsigned short payloadHead;

    // 配置类型
    // 0x1周期上报事件设置如修改 10h，该位为 0x0A,高位 0 不可省略；
    // 0x02高度阈值配置如修改高度阈值 30cm；该位为 0x1E，高位 0 不可省略；
    // 0x04角度阈值设置如 修改角度阈值 30° ，该位为 0x1E，高位 0 不可省略
    // 0x05电量阈值设置，如修 改 20%，该位设置为 0x14；
    // 0x08周期检测时间设置， 如修改为 10min,该位设置为 0x0A；
    // 0x09重启设备
    unsigned char configType;

    //帧尾 0x81
    unsigned char frameTail;

}__attribute__((packed)) ManholeCoverRequestSt;



/**
 *  下行确认包的负载
 */
typedef struct {

    //设备版本
    unsigned short devVersion;

    //上报周期
    unsigned char reportingCycle;

    //检测周期 表示周期检测时间间隔； 1 个字节；16 进制发送；单位：分钟； 注意：410 设备中不具有周期检测，可忽略此内 容；
    unsigned char detectionCycle;

    //表示高度 (满、空) 报警阈值； 1 个字节；16 进制发送；单位：厘米； 注意：对于 410 设备，不具有高度检测，可忽略 此内容；
    unsigned char heightThreshold;

    //温度阈值
    unsigned char temperatureThreshold;

    //倾斜角度阈值
    unsigned char tiltangleThreshold;

    //表示移位角度检测功能选项，打开或关闭； 1 个字节，取值 00 或 01；00 表示关闭移位检测功 能，01 表示打开；
    unsigned char tiltangleDetection;

    //超声波测距量程表示超声波测距量程，仅针对 420 传感器；1 个 字节，取值 00 或 01；00 表示 2m 版本，01 表示 5m 版本； 注意：对于 410 设备，由于不具备超声波高度检 测功能，可忽略此内容；
    unsigned char ultrasonicRangingRange;

    //工作模式表示周期检测功能开关选项； 1 个字节，可取值 00 或 01,00 表示周期检测功能 打开，01 表示关闭； 注意：此内容仅针对 420 设备，410 设备无周 期检测功能
    unsigned char workingMode;

    //保留
    unsigned char reserve[8];


}__attribute__((packed)) ManholeCoverAckPayload;



/**
 * 0x3
 * 下行确认包
 */
typedef struct {


    //前导码  0x80
    unsigned char frameHead;

    //强制位  0x00
    unsigned char qiangzhi;

    //设备类型 0x01
    unsigned char devType;

    //消息类型 0x1主动上报 0x2心跳包 0x3下行回复确认包
    unsigned char msgType;

    //帧大小
    unsigned char frameLength;

    //
    ManholeCoverAckPayload ack;

    //保留
    unsigned char retain;

    //帧尾
    unsigned char frameTail;

}__attribute__((packed)) ManholeCoverRxAckSt;



#endif

struct ManholeCoverDevTLV
{
    vector<uint8_t> tag;
    uint32_t length;
    vector<uint8_t> value;
};

typedef struct {
    //addr
    unsigned char addr;

    //0x3 modbus read
    unsigned char cmd;

    //lenth
    unsigned char length;

    //Xzhou
    unsigned short x;

    //yzhou
    unsigned short y;

    //zzhou
    unsigned short z;

    //crc
    unsigned char crc;

}__attribute__((packed))  ManholeCoverRxSt;


/**
 *
 *  井盖监测器数据结构
 */
typedef struct {

    //高度表示设备到液位 (或物体) 表面的距离； 2 个字节，16 进制发送，单位：毫米 (mm) 注意：410 井盖设备中， 由于不带高度检测功能， 因此该字节默认为 0x0000；
    //unsigned short height;

    //是否携带经纬度表示数据中是否携带经纬度信息； 1 个字节；此字节内容为 0x00 或 0x01; 其中 0x00 表 示数据中不含经纬度信息；0x01 表示上传经纬度信
    //unsigned char locationFlag;

    //经度
    //unsigned int  longitude;

    //纬度浮点型、单精度 ( IEEE-754 标准) ，高位在前，低位在后的顺序。 注意：如果经纬度选择位为0x00，不上传此纬度信息；
    //unsigned int latitude;

    //温度
    float temperature;

    //倾斜角度
    float tiltangle;

    // 表示空满、保留位、倾倒、 电量 4 种状态位； 共 2 个字节；各状态位取值为 0 或 1,0 表示报警 (异 常) 状态，1 表示正常状态；
    // 前字节高 4 位为空满状态位，低 4 位为保留位位，即 0x00 表示空，0x10 表示满溢， 同理，后字节一样，0x0X 表示未倾倒，0x1X 表示倾 倒报警；0xX0 表示电量正常，0xX1 表示低电量报警；
    // 注意：对于 410 井盖设备，由于不具有满空检测功 能，因此此处空满状态位默认为 0。
    //unsigned short state;


    //移位状态
    int move_state;

    //0xX0 表示电量正常，0xX1 表示低电量报警；
    int battery_state;

    //帧计数器
    //unsigned short counter;

}ManholeCoverSt;

/**
 * 井盖监测器
 */
class ManholeCoverDev : public Device
{
public:
    ManholeCoverDev(DeviceParam &device);
    ~ManholeCoverDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:

    //属性
    ManholeCoverSt mData;

    //设置属性
    int SetProperty(Json::Value &req);

    //获取属性
    int GetProperty(Json::Value &req, Json::Value &res);

    //属性上报
    int PostProperty();

    //事件上报
    int postLowPowerEvent();
    int PostAlarmEvent();
    //服务调用


    int UnpackSensorData(string &data);

    vector<unsigned char> base64_decode(const string &in);

    string bytes_to_hex(const vector<unsigned char> &bytes);

    float bytes_to_float(const vector<unsigned char> &bytes);

    vector<ManholeCoverDevTLV> parse_tlv(const vector<unsigned char> &data);

    char get_alarm_type(const vector<ManholeCoverDevTLV> &tlvs);

    float get_angle_value(const vector<ManholeCoverDevTLV> &tlvs);
};

#endif //GATEWAY_IOT_MANHOLE_COVER_DEV_H
