/**
 * 户外消防栓监测器
 */

#ifndef GATEWAY_IOT_HYDRANT_DEV_H
#define GATEWAY_IOT_HYDRANT_DEV_H


#include "device.h"


/**
 *  example:
    A5 5A 10 12 01 32 DA B4 34 B2 22 01 01 F4 34 4A 55 AA

    parsing:
    A5 5A//header
    10//LORA数据命令类型 0x10
    12//payload len   18 bytes  A5 5A  to  55AA
    01 32 DA B4// ID  20110004
    34//Battery status  0x34&0x1F * 5 = 100   %  电量
    B2//Signal strength -72 dbm 信号强度
    22//Device type  设备类型34 消火栓压力
    01//alarm = 0x01 低报警    0x02高报警
    01 F4  //int16  当前数据   500 * 0.001MPa =  0.5MPa
    34 4A//crc16  MODBUS
    55AA//end

 */


/* torch version
typedef struct {

    //消息头A5 5A//header
    short header;

    //lora数据命令类型 0x10
    unsigned char type;

    //payload len 12
    unsigned char len;

    //id  01 32 DA B4// ID  20110004
    unsigned  int id;

    //0x34&0x1F * 5 = 100   %  电量
    unsigned char electricQuantity;

    //B2  -72 dbm 信号强度
    char signalStrength;

    //设备类型
    //22 Device type  设备类型0x22 = 34 消火栓压力
    unsigned char devType;

    //01 alarm = 0x01 低报警    0x02高报警
    unsigned char alarmType;

    //01 F4 int16  当前数据   500 * 0.001MPa =  0.5MPa
    unsigned short pressure;

    //34 4A crc16
    unsigned short crc;

    //55AA
    unsigned short end;

}__attribute__((packed))  HydrantRxSt;
*/

typedef struct {

    //header tpsl
    unsigned int header;

    //lora数据命令类型 0x10
    unsigned char type;

    //payload len 12
    unsigned short len;

    //device type 4 is hydrant
    unsigned  char deviceType;

    //device id
    unsigned short deviceId;

    //send time
    unsigned char sendTime[6];

    //battery level
    unsigned char batteryLevel;

    //signalStrength
    unsigned char signalStrength;

    //sample data
    unsigned int sampleData;

    unsigned short crc16;

    unsigned char tail[3];

}__attribute__((packed))  HydrantRxSt;



typedef struct {

    //设备状态 bit0:1异常 0正常
    //bit1-7 保留
    //int state;

    //0-100 百分比电量
    int electricQuantity;

    //B2  -72 dbm 信号强度
    char signalStrength;

    //alarm = 0x01 低报警    0x02高报警
    int alarmType;

    // 压力数据，传感器压力数据为0x9c， 十进制156KPa; 液位为156cm
    float pressure;

}__attribute__((packed))HydrantSt;



/**
 * 户外消防栓监测器
 */
class HydrantDev : public Device
{
public:
    HydrantDev(DeviceParam &device);
    ~HydrantDev();

    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq);
    bool OnRxSensorMessage(string &data);

private:
    //属性
    HydrantSt mData;

    /**
     * 设置属性
     * @param req
     * @return
     */
    int SetProperty(Json::Value &req);

    /**
     * 获取属性
     * @param req
     * @param res
     * @return
     */
    int GetProperty(Json::Value &req, Json::Value &res);

    /**
     * 上报属性
     * @return
     */
    int PostProperty();

    /**
     * 上报低电压事件
     * @return
     */
    int PostLowPowerEvent();

    /**
     * 上报角度异常事件
     * @return
     */
    int PostAngleAlarmEvent();

    /**
     * 水压事件上报
     * @return
     */
    int PostPressureAlarmEvent();

    /**
     * 解析设备数据
     * @param data
     * @return
     */
    int UnpackSensorData(string &data);
};

#endif //GATEWAY_IOT_HYDRANT_DEV_H
