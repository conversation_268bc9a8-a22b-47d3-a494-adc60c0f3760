//
// Created by seashell on 6/30/21.
//

#include "radar_level_dev.h"
#include <iostream>
#include <device_interface.h>


/***** 主题 *****/



/***** 能力集属性 *****/
#define VOLTAGE "battery_voltage"
#define WATER_LEVEL    "water_level"



/***** 事件 *****/
#define EVENT_LOW_VOLTAGE "low_voltage"




RadarLevelDev::RadarLevelDev(DeviceParam &device) : Device(device)
{
}


RadarLevelDev::~RadarLevelDev()
{
}



/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool RadarLevelDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool RadarLevelDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    if (mData.battery_voltage <= 3.0){
        postLowPowerEvent();
    }
    return true;
}


/**
 * 解析设备数据
 * @param data
 * @return
 */
int RadarLevelDev::UnpackSensorData(string &data)
{
    RadarLevelRxSt st = {0};
    memcpy((void *)&st,data.c_str(),sizeof(RadarLevelRxSt));
    mData.battery_voltage = 0.0;//st.voltage/10.0;
    mData.water_level = st.water_level/10.0;
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int RadarLevelDev::SetProperty(Json::Value &req)
{
    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int RadarLevelDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == WATER_LEVEL){
            res[WATER_LEVEL] = mData.water_level;
        } else if (i.asString() == VOLTAGE){
            res[VOLTAGE] = mData.battery_voltage;
        } else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int RadarLevelDev::PostProperty()
{
    Json::Value v;
    v[WATER_LEVEL] = mData.water_level;
    v[VOLTAGE] = mData.battery_voltage;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}

/**
 * 电池低压事件上报
 * @return
 */
int RadarLevelDev::postLowPowerEvent()
{
    Json::Value v;
    v[VOLTAGE] = mData.battery_voltage;
    return DeviceHelper::getInstance().ReportDevEvent(this, EVENT_LOW_VOLTAGE, v);

}

