//
// Created by seashell on 6/6/22.
//

#include "smoke_dev.h"
/***** 主题 *****/



/***** 能力集属性 *****/

#define TEMPERATURE                         "temperature"
#define SMOKECONCENTRATION                  "smokeConcentration"
#define ELECTRICQUANTITY                        "electricQuantity"
/***** 事件 *****/
#define TOPIC_E_FIRE_ALARM                  "fireAlarm"
#define TOPIC_E_HIGH_TEMPERATURE_ALARM      "highTemperature"
#define TOPIC_E_BREAKDOWN                   "breakdown"
#define TOPIC_E_LOW_VOLTAGE                 "lowVoltage"
#define TOPIC_E_TEST                        "test"


SmokeDev::SmokeDev(DeviceParam &device) : Device(device)
{
}

SmokeDev::~SmokeDev()
{
}


/**
 * 平台下发指令，获取属性，设置属性，调用服务
 * @param topic
 */
bool SmokeDev::OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq)
{
    string topic = strTopic;
    //stringReplace(topic, "/", ".");

    message_id = jValue[J_ID].asString();
    int result  = 0;
    Json::Value res;
    Json::Value req = jValue[J_PARAMS];

    if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_GET)) //属性设置
    {
        result = GetProperty(req, res);
    }
    else if (IS_TOPIC_MESSAGE(topic, TOPIC_PRO_SET)) //属性获取
    {
        result = SetProperty(req);
    }
    else
    {
        ;
    }

    if (!res.isNull())
    {
        return RET_OK ==
                DeviceHelper::getInstance().ResponseToPlatform(this, message_id, result, strTopic.append("_res"), res);
    }

    return false;
}


/**
 * 设备上报
 * @param jsData
 * @return
 */
bool SmokeDev::OnRxSensorMessage(string &data)
{
    UnpackSensorData(data);
    PostProperty();
    return true;
}

std::vector<TLV> SmokeDev::parseTLV(const char* data, size_t length) {
    std::vector<TLV> result;

    size_t i = 0;
    while (i < length) {
        TLV tlv;
        tlv.type = data[i++];
        tlv.length = data[i++];
        for (size_t j = 0; j < tlv.length; ++j) {
            tlv.value.push_back(data[i++]);
        }
        result.push_back(tlv);
    }

    return result;
}

/**
 * 解析设备数据
 * @param data
 * @return
 */
int SmokeDev::UnpackSensorData(string &data)
{

    int i = 0;
    int bodyLen = 0;
    char sensorData[256] = {0};
    char body[256] = {0};
    SmokeRxSt *st = (SmokeRxSt *)&sensorData;
    memcpy((void *)&sensorData,data.c_str(),data.length());

    bodyLen = ntohs(st->length);
    memcpy((void *)&body,st->body,bodyLen) ;

    std::vector<TLV> tlvs = parseTLV(body, bodyLen);
    for (const auto& tlv : tlvs) {

        //
        if (tlv.type == 0x14){
            mData.smokeConcentration = (int)*tlv.value.data();

        } else if (tlv.type == 0x0B){
            mData.temperature = (short)*tlv.value.data();
        }
        else if (tlv.type == 0x19){
            //test
            Json::Value v;
            v["reserve"] = 1;
            DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_TEST, v);
        }
        else if (tlv.type == 0x24){
            mData.electricQuantity = (char)*tlv.value.data();

        }
        else if (tlv.type == 0x01){
            int xx = (char)*tlv.value.data();
            if (xx == 1){
                Json::Value v;
                v["reserve"] = 1;
                DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_FIRE_ALARM, v);
            } else if (xx == 2){
                Json::Value v;
                v["reserve"] = 1;
                DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_HIGH_TEMPERATURE_ALARM, v);
            }

        }
        else if (tlv.type == 0x02){
            int xx = (char)*tlv.value.data();

            if (xx == 2){
                Json::Value v;
                v["reserve"] = 1;
                DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_LOW_VOLTAGE, v);
            } else if (xx == 3){
                Json::Value v;
                v["reserve"] = 1;
                DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_BREAKDOWN, v);
            } else if (xx == 7){
                Json::Value v;
                v["reserve"] = 1;
                DeviceHelper::getInstance().ReportDevEvent(this, TOPIC_E_LOW_VOLTAGE, v);
            }
        }
    }
    return 0;
}



/**
 * 设置属性
 * @param jValue
 * @return
 */
int SmokeDev::SetProperty(Json::Value &req)
{

    return RET_OK;
}

/**
 * 获取属性
 * @param req
 * @param res
 * @return
 */
int SmokeDev::GetProperty(Json::Value &req, Json::Value &res)
{
    for (const auto &i : req)
    {
        if (i.asString() == TEMPERATURE){
            res[TEMPERATURE] = mData.temperature;
        } else if (i.asString() == SMOKECONCENTRATION){
            res[SMOKECONCENTRATION] = mData.smokeConcentration;
        }
        else if (i.asString() == ELECTRICQUANTITY){
            res[ELECTRICQUANTITY] = mData.electricQuantity;
        }  else{
            LOG(ERROR) << "property not exist,name:" << i.asString() <<endl;
            res = Json::nullValue;
        }
    }
    return RET_OK;
}



/**
 * 属性上报
 * @return
 */
int SmokeDev::PostProperty()
{
    Json::Value v;
    v[TEMPERATURE] = mData.temperature;
    v[SMOKECONCENTRATION] = mData.smokeConcentration;
    v[ELECTRICQUANTITY] = mData.electricQuantity;
    return DeviceHelper::getInstance().ReportPropEvent(this, v);
}
