/**
 * @Description:   网关设备
 */
#ifndef GATEWAY_DEV_H_
#define GATEWAY_DEV_H_

#include <cstdlib>
#include "temperature_humidity.h"
#include "adc.h"
#include "device.h"
#include "device_interface.h"

#define TOPIC_TRANSPARENT_SERVICE "/service/transparent/call"               //服务调用
#define TOPIC_OTA_UPDATE_FIRMWARE "$ota/update"
#define TOPIC_OTA_PROGRESS_REPORT "ota.progress.report"


#define VERSION_LENGTH 10           //
#define IP_LENGTH 20

typedef struct {
    //电压
    float voltage;

    float temperature;

    float humidity;

    float disk_size;

    float disk_usage;

    float mem_size;

    float mem_usage;

    float cpu_usage;

    float high_temperature_threshold;

    char version[VERSION_LENGTH];

    char ip[IP_LENGTH];
}GatewaySt;



class GatewayDev : public Device
{
public:
    explicit GatewayDev(DeviceParam &deviceInfo);
    ~GatewayDev(void) override;

    bool OnRxSensorMessage(string &data) override;
    bool OnRxPlatformMessage(string strTopic, Json::Value jValue, bool isLocalReq) override;
    int ScheduledTasks();
private:
    //外部接口
    DeviceInterface *mInterface;
    //计数器
    bool mNeedCheckOTA = true;
    //属性
    GatewaySt mData;

    /**
     * 设置属性
     * @param req
     * @return
     */
    int SetProperty(Json::Value &req);

    /**
     * 获取属性
     * @param req
     * @param res
     * @return
     */
    int GetProperty(Json::Value &req, Json::Value &res);


    /**
     * 上报属性
     * @return
     */
    int PostProperty();

    /**
     * 上报掉电事件
     * @param e
     * @return
     */
    int PostPowerOffEvent(string e);

    /**
     * 升级
     * @param ota
     * @return
     */
    int Upgrade(Json::Value ota);

    /**
     * 透传指令
     * @param value
     * @param resJson
     * @return
     */
    int TransparentCommand(Json::Value value, Json::Value &resJson);

    /**
     * 设备上报固件版本号
     * @param version
     * @return
     */
    int ReportDeviceVersion(string version);


    /**
     * @description: 设备上报升级状态
     * @param: progress　升级进度
     * @param: desc 进度描述信息
     * @return:
     */
    int ReportUpgradeProgress(string status, string desc);
};

#endif