{"info": {"pk": "3X7EfvvuqAmfSF1m"}, "props": [{"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "门磁状态", "id": "door_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "拆除报警", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}, {"name": "门窗开闭", "id": "anti_theft", "type": "info", "action": "thing.event.anti_theft.post", "out": [{"name": "门磁状态", "id": "door_state", "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}]}, {"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}]}