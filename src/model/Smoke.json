{"info": {"pk": "4FItghD46Mmn1agh"}, "props": [{"name": "状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "火警", "2": "故障", "3": "低电压", "4": "火警消除", "5": "故障消除", "6": "低电压恢复", "0xe": "探测器上电", "0xd": "按键自检"}}}, {"name": "电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "火警", "id": "fire_alarm", "type": "alarm", "action": "thing.event.fire_alarm.post", "out": [{"name": "状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "火警", "2": "故障", "3": "低电压", "4": "火警消除", "5": "故障消除", "6": "低电压恢复", "0xe": "探测器上电", "0xd": "按键自检"}}}]}, {"name": "火警消除", "id": "fire_alarm_remove", "type": "alarm", "action": "thing.event.fire_alarm_remove.post", "out": [{"name": "状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "火警", "2": "故障", "3": "低电压", "4": "火警消除", "5": "故障消除", "6": "低电压恢复", "0xe": "探测器上电", "0xd": "按键自检"}}}]}, {"name": "故障", "id": "breakdown", "type": "alarm", "action": "thing.event.breakdown.post", "out": [{"name": "状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "火警", "2": "故障", "3": "低电压", "4": "火警消除", "5": "故障消除", "6": "低电压恢复", "0xe": "探测器上电", "0xd": "按键自检"}}}]}, {"name": "故障消除", "id": "breakdown_remove", "type": "alarm", "action": "thing.event.breakdown_remove.post", "out": [{"name": "状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "火警", "2": "故障", "3": "低电压", "4": "火警消除", "5": "故障消除", "6": "低电压恢复", "14": "探测器上电", "13": "按键自检"}}}]}, {"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "低电压解除", "id": "low_voltage_remove", "type": "alarm", "action": "thing.event.low_voltage_remove.post", "out": [{"name": "电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "设备开机", "id": "power_on", "type": "alarm", "action": "thing.event.power_on.post", "out": [{"name": "电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "按键自检", "id": "key_test", "type": "alarm", "action": "thing.event.key_test.post", "out": [{"name": "电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}]}