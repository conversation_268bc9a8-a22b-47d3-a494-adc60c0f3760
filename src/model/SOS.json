{"info": {"pk": "bw63z6jt04p5RRTt"}, "props": [{"name": "告警状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "没有告警", "1": "有告警", "2": "有告警延时上报"}}}, {"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "拆除报警", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}, {"name": "告警", "id": "sos", "type": "alarm", "action": "thing.event.sos.post", "out": [{"name": "告警状态", "id": "sos_state", "data": {"type": "enum", "rules": {"0": "没有告警", "1": "有告警", "2": "有告警延时上报"}}}]}, {"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}]}