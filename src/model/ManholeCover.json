{"info": {"pk": "Ee54RIu3dZ5smjQ8"}, "props": [{"name": "X角度", "id": "x-angle", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "°"}}}, {"name": "Y角度", "id": "y-angle", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "°"}}}, {"name": "Z角度", "id": "z-angle", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "°"}}}, {"name": "井盖翘起状态", "id": "manholeCoverCocked", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}, {"name": "井盖震动状态", "id": "vibrate", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}, {"name": "井盖竖立状态", "id": "manholeCoverErection", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}, {"name": "井盖翻起状态", "id": "manholeCoversTurnedUp", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "异常"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "井盖告警", "id": "alarm", "type": "alarm", "action": "thing.event.alarm.post", "out": [{"name": "井盖状态", "id": "manhole_cover_tate", "data": {"type": "enum", "rules": {"0": "无报警", "1": "井盖翘起报警", "2": "井盖震动报警", "3": "井盖竖立报警", "4": "井盖翻起报警"}}}]}, {"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double"}}]}]}