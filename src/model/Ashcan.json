{"info": {"pk": "0YUZSaYf7HN8msJz"}, "props": [{"name": "距离值", "id": "distance", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "毫米"}}}, {"name": "距离状态", "id": "state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "超过距离值", "1": "小于距离值", "2": "等于距离值"}}}], "events": [{"name": "满溢上报", "id": "full", "type": "info", "action": "thing.event.full.post", "out": [{"name": "距离值", "id": "distance", "data": {"type": "double", "rules": {"unit": "毫米"}}}]}]}