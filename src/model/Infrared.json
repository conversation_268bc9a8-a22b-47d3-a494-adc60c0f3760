{"info": {"pk": "G8a88OBFYh0U7eo3"}, "props": [{"name": "红外状态", "id": "infrared_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未触发", "1": "触发"}}}, {"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "有人报警", "id": "body_alarm", "type": "alarm", "action": "thing.event.body_alarm.post", "out": [{"name": "红外状态", "id": "infrared_state", "data": {"type": "bool", "rules": {"0": "未触发", "1": "触发"}}}]}, {"name": "被拆除", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}, {"name": "低压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double"}}]}]}