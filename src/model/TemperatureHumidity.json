{"info": {"pk": "WJ5vunLo6tS1MD7k"}, "props": [{"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "室内温度", "id": "temperature", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "℃"}}}, {"name": "室内湿度", "id": "humidity", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "%RH"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "被拆除", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}]}