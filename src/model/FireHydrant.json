{"info": {"pk": "5m0Xi8VcdLOV7SMZ"}, "props": [{"name": "角度状态", "id": "angle_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "告警"}}}, {"name": "消防水压", "id": "pressure", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "kPa"}}}, {"name": "压力状态", "id": "pressure_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "告警"}}}, {"name": "电池电压状态", "id": "battery_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "告警"}}}, {"name": "电池电压", "id": "battery", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "压力报警", "id": "pressure_alarm", "type": "alarm", "action": "thing.event.pressure_alarm.post", "out": [{"name": "消防水压", "id": "pressure", "data": {"type": "double", "rules": {"unit": "kPa"}}}, {"name": "压力状态", "id": "pressure_state", "data": {"type": "bool", "rules": {"0": "正常", "1": "告警"}}}]}, {"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "角度报警", "id": "angle_alarm", "type": "alarm", "action": "thing.event.angle_alarm.post", "out": [{"name": "角度状态", "id": "angle_state", "data": {"type": "bool", "rules": {"0": "正常", "1": "告警"}}}]}]}