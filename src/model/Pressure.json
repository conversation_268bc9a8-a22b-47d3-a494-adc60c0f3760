{"info": {"pk": "2J7TGd84xNIZc9JH"}, "props": [{"name": "水压", "id": "pressure", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "kPa"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}]}