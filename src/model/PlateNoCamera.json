{"info": {"pk": "TjO9eth2D50kf3L8"}, "props": [{"name": "资源编号", "id": "src_index", "model": "r", "optional": false, "required": false, "data": {"type": "String"}}], "events": [{"name": "车辆入场放行", "id": "car_in_event", "type": "info", "action": "thing.event.car_in_event.post", "out": [{"name": "车牌号码", "id": "plate_no", "data": {"type": "String"}}, {"name": "车牌图片", "id": "plate_url", "data": {"type": "String"}}, {"name": "车辆图片", "id": "vehicle_url", "data": {"type": "String"}}, {"name": "出入口名称", "id": "gate_name", "data": {"type": "String"}}, {"name": "出入口编号", "id": "gate_index", "data": {"type": "String"}}]}, {"name": "车辆出场放行", "id": "car_out_event", "type": "info", "action": "thing.event.car_out_event.post", "out": [{"name": "车牌", "id": "plate_no", "data": {"type": "String"}}, {"name": "车牌图片", "id": "plate_url", "data": {"type": "String"}}, {"name": "车辆图片", "id": "vehicle_url", "data": {"type": "String"}}, {"name": "出入口名称", "id": "gate_name", "data": {"type": "String"}}, {"name": "出入口编号", "id": "gate_index", "data": {"type": "String"}}]}]}