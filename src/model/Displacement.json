{"info": {"pk": "5hLo3xb9e64aUCvK"}, "props": [{"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "移动状态", "id": "move_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "静止", "1": "移动"}}}, {"name": "充电状态", "id": "recharge", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未充电", "1": "充电"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "低电压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}, {"name": "被拆除", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}, {"name": "移动报警", "id": "move", "type": "alarm", "action": "thing.event.move.post", "out": [{"name": "移动状态", "id": "move_state", "data": {"type": "bool", "rules": {"0": "静止", "1": "移动"}}}]}]}