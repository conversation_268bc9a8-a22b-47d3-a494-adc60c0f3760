{"info": {"pk": "5Lj6J72S0Dh9Xl5F"}, "props": [{"name": "电压", "id": "voltage", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "V"}}}, {"name": "电流", "id": "current", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "mA"}}}, {"name": "功率", "id": "power", "model": "r", "optional": false, "required": false, "data": {"type": "float", "rules": {"unit": "W"}}}, {"name": "开关状态", "id": "switch_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "关灯", "1": "开灯"}}}, {"name": "调光值", "id": "brightness", "model": "r", "optional": false, "required": false, "data": {"type": "int"}}, {"name": "电压状态", "id": "voltage_state", "model": "r", "optional": false, "required": false, "data": {"type": "int", "rules": {"0": "正常", "1": "过压"}}}, {"name": "电流状态", "id": "current_state", "model": "r", "optional": false, "required": false, "data": {"type": "int", "rules": {"0": "正常", "1": "过压"}}}, {"name": "电量传感器状态", "id": "sensor_state", "model": "r", "optional": false, "required": false, "data": {"type": "int", "rules": {"0": "正常", "1": "过压"}}}], "services": [{"name": "开灯", "id": "open_light", "action": "thing.service.open_light", "type": "sync", "in": [{"name": "延时时间", "id": "delay", "data": {"type": "int", "rules": {"unit": "秒"}}}], "out": [{"name": "操作结果", "id": "state", "data": {"type": "bool", "rules": {"0": "成功", "1": "失败"}}}]}, {"name": "关灯", "id": "close_light", "action": "thing.service.close_light", "type": "sync", "in": [{"name": "延时时间", "id": "delay", "data": {"type": "int"}}], "out": [{"name": "操作结果", "id": "state", "data": {"type": "bool", "rules": {"0": "成功", "1": "失败"}}}]}]}