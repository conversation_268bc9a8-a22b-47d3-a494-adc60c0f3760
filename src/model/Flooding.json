{"info": {"pk": "5983w5FTMPiZ7uH9"}, "props": [{"name": "水浸状态", "id": "flooding_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未被水浸", "1": "水浸"}}}, {"name": "电池电压", "id": "battery_voltage", "model": "r", "optional": false, "required": false, "data": {"type": "double", "rules": {"unit": "V"}}}], "events": [{"name": "发生水浸", "id": "flooding", "type": "alarm", "action": "thing.event.flooding.post", "out": [{"name": "水浸状态", "id": "flooding_state", "data": {"type": "bool", "rules": {"0": "未被水浸", "1": "水浸"}}}]}, {"name": "低压报警", "id": "low_voltage", "type": "alarm", "action": "thing.event.low_voltage.post", "out": [{"name": "电池电压", "id": "battery_voltage", "data": {"type": "double", "rules": {"unit": "V"}}}]}]}