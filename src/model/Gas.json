{"info": {"pk": "0ycXb6yH1cx4Q8m4"}, "props": [{"name": "燃气状态", "id": "gas_state", "model": "r", "optional": false, "required": false, "data": {"type": "enum", "rules": {"0": "正常", "1": "告警", "2": "故障", "3": "预热"}}}, {"name": "防拆状态", "id": "anti_theft_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}, {"name": "按键状态", "id": "key_state", "model": "r", "optional": false, "required": false, "data": {"type": "bool", "rules": {"0": "正常", "1": "按键自检", "2": "消音"}}}, {"name": "气体浓度", "id": "concentration", "model": "r", "optional": false, "required": false, "data": {"type": "double"}}], "events": [{"name": "告警", "id": "danger", "type": "alarm", "action": "thing.event.danger.post", "out": [{"name": "燃气状态", "id": "gas_state", "data": {"type": "enum", "rules": {"0": "正常", "1": "告警", "2": "故障", "3": "预热"}}}]}, {"name": "故障", "id": "error", "type": "alarm", "action": "thing.event.error.post", "out": [{"name": "燃气状态", "id": "gas_state", "data": {"type": "enum", "rules": {"0": "正常", "1": "告警", "2": "故障", "3": "预热"}}}]}, {"name": "预热", "id": "warm", "type": "info", "action": "thing.event.warm.post", "out": [{"name": "燃气状态", "id": "gas_state", "data": {"type": "enum", "rules": {"0": "正常", "1": "告警", "2": "故障", "3": "预热"}}}]}, {"name": "被拆除", "id": "remove", "type": "alarm", "action": "thing.event.remove.post", "out": [{"name": "防拆状态", "id": "anti_theft_state", "data": {"type": "bool", "rules": {"0": "未拆", "1": "拆除"}}}]}, {"name": "按键触发", "id": "key_click", "type": "info", "action": "thing.event.key_click.post", "out": [{"name": "按键状态", "id": "key_state", "data": {"type": "enum", "rules": {"0": "正常", "1": "按键自检", "2": "消音"}}}]}]}